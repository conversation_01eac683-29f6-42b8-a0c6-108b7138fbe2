package com.sux.system.service.impl;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sux.system.mapper.EmploymentInfoMapper;
import com.sux.system.domain.EmploymentInfo;
import com.sux.system.service.IEmploymentInfoService;

/**
 * 用工信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class EmploymentInfoServiceImpl extends ServiceImpl<EmploymentInfoMapper, EmploymentInfo> implements IEmploymentInfoService
{
    @Autowired
    private EmploymentInfoMapper employmentInfoMapper;

    /**
     * 查询用工信息列表
     * 
     * @param employmentInfo 用工信息
     * @return 用工信息
     */
    @Override
    public List<EmploymentInfo> selectEmploymentInfoList(EmploymentInfo employmentInfo)
    {
        return employmentInfoMapper.selectEmploymentInfoList(employmentInfo);
    }

    /**
     * 查询用工信息
     * 
     * @param employmentId 用工信息主键
     * @return 用工信息
     */
    @Override
    public EmploymentInfo selectEmploymentInfoByEmploymentId(Long employmentId)
    {
        return employmentInfoMapper.selectEmploymentInfoByEmploymentId(employmentId);
    }

    /**
     * 新增用工信息
     * 
     * @param employmentInfo 用工信息
     * @return 结果
     */
    @Override
    public int insertEmploymentInfo(EmploymentInfo employmentInfo)
    {
        employmentInfo.setCreateId(SecurityUtils.getUserId());
        employmentInfo.setCreateTime(DateUtils.getNowDate());
        employmentInfo.setPublisherUserId(SecurityUtils.getUserId());
        
        // 设置默认值
        if (StringUtils.isEmpty(employmentInfo.getStatus())) {
            employmentInfo.setStatus("draft");
        }
        if (StringUtils.isEmpty(employmentInfo.getUrgencyLevel())) {
            employmentInfo.setUrgencyLevel("normal");
        }
        if (employmentInfo.getPositionsNeeded() == null) {
            employmentInfo.setPositionsNeeded(1);
        }
        if (employmentInfo.getPositionsFilled() == null) {
            employmentInfo.setPositionsFilled(0);
        }
        if (employmentInfo.getIsVerified() == null) {
            employmentInfo.setIsVerified(0);
        }
        if (employmentInfo.getIsFeatured() == null) {
            employmentInfo.setIsFeatured(0);
        }
        if (employmentInfo.getViewCount() == null) {
            employmentInfo.setViewCount(0);
        }
        if (employmentInfo.getApplicationCount() == null) {
            employmentInfo.setApplicationCount(0);
        }
        if (StringUtils.isEmpty(employmentInfo.getDelFlag())) {
            employmentInfo.setDelFlag("0");
        }

        return employmentInfoMapper.insertEmploymentInfo(employmentInfo);
    }

    /**
     * 修改用工信息
     * 
     * @param employmentInfo 用工信息
     * @return 结果
     */
    @Override
    public int updateEmploymentInfo(EmploymentInfo employmentInfo)
    {
        employmentInfo.setUpdateId(SecurityUtils.getUserId());
        employmentInfo.setUpdateTime(DateUtils.getNowDate());
        return employmentInfoMapper.updateEmploymentInfo(employmentInfo);
    }

    /**
     * 批量删除用工信息
     * 
     * @param employmentIds 需要删除的用工信息主键
     * @return 结果
     */
    @Override
    public int deleteEmploymentInfoByEmploymentIds(Long[] employmentIds)
    {
        return employmentInfoMapper.deleteEmploymentInfoByEmploymentIds(employmentIds);
    }

    /**
     * 删除用工信息信息
     * 
     * @param employmentId 用工信息主键
     * @return 结果
     */
    @Override
    public int deleteEmploymentInfoByEmploymentId(Long employmentId)
    {
        return employmentInfoMapper.deleteEmploymentInfoByEmploymentId(employmentId);
    }

    /**
     * 查询已发布的用工信息列表
     * 
     * @param employmentInfo 用工信息
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectPublishedEmploymentInfoList(EmploymentInfo employmentInfo)
    {
        return employmentInfoMapper.selectPublishedEmploymentInfoList(employmentInfo);
    }

    /**
     * 查询推荐用工信息列表
     * 
     * @param employmentInfo 用工信息
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectFeaturedEmploymentInfoList(EmploymentInfo employmentInfo)
    {
        return employmentInfoMapper.selectFeaturedEmploymentInfoList(employmentInfo);
    }

    /**
     * 查询我发布的用工信息列表
     * 
     * @param employmentInfo 用工信息
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectMyEmploymentInfoList(EmploymentInfo employmentInfo)
    {
        return employmentInfoMapper.selectMyEmploymentInfoList(employmentInfo);
    }

    /**
     * 根据用工类型查询用工信息列表
     * 
     * @param employmentType 用工类型
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectEmploymentInfoByType(String employmentType)
    {
        return employmentInfoMapper.selectEmploymentInfoByType(employmentType);
    }

    /**
     * 根据工作类别查询用工信息列表
     * 
     * @param workCategory 工作类别
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectEmploymentInfoByCategory(String workCategory)
    {
        return employmentInfoMapper.selectEmploymentInfoByCategory(workCategory);
    }

    /**
     * 根据区域代码查询用工信息列表
     * 
     * @param regionCode 区域代码
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectEmploymentInfoByRegion(String regionCode)
    {
        return employmentInfoMapper.selectEmploymentInfoByRegion(regionCode);
    }

    /**
     * 根据薪资类型查询用工信息列表
     * 
     * @param salaryType 薪资类型
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectEmploymentInfoBySalaryType(String salaryType)
    {
        return employmentInfoMapper.selectEmploymentInfoBySalaryType(salaryType);
    }

    /**
     * 根据薪资范围查询用工信息列表
     * 
     * @param minSalary 最低薪资
     * @param maxSalary 最高薪资
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectEmploymentInfoBySalaryRange(java.math.BigDecimal minSalary, java.math.BigDecimal maxSalary)
    {
        return employmentInfoMapper.selectEmploymentInfoBySalaryRange(minSalary, maxSalary);
    }

    /**
     * 根据紧急程度查询用工信息列表
     * 
     * @param urgencyLevel 紧急程度
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectEmploymentInfoByUrgency(String urgencyLevel)
    {
        return employmentInfoMapper.selectEmploymentInfoByUrgency(urgencyLevel);
    }

    /**
     * 查询用工信息统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectEmploymentInfoStatistics()
    {
        return employmentInfoMapper.selectEmploymentInfoStatistics();
    }

    /**
     * 根据关键词搜索用工信息
     * 
     * @param keyword 关键词
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectEmploymentInfoByKeyword(String keyword)
    {
        return employmentInfoMapper.selectEmploymentInfoByKeyword(keyword);
    }

    /**
     * 更新用工信息浏览次数
     * 
     * @param employmentId 用工信息ID
     * @return 结果
     */
    @Override
    public int updateEmploymentInfoViewCount(Long employmentId)
    {
        return employmentInfoMapper.updateEmploymentInfoViewCount(employmentId);
    }

    /**
     * 更新用工信息申请次数
     * 
     * @param employmentId 用工信息ID
     * @return 结果
     */
    @Override
    public int updateEmploymentInfoApplicationCount(Long employmentId)
    {
        return employmentInfoMapper.updateEmploymentInfoApplicationCount(employmentId);
    }

    /**
     * 查询即将到期的用工信息
     * 
     * @param days 天数
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectEmploymentInfoExpiringSoon(Integer days)
    {
        return employmentInfoMapper.selectEmploymentInfoExpiringSoon(days);
    }

    /**
     * 查询用工信息详细信息（包含关联信息）
     * 
     * @param employmentId 用工信息ID
     * @return 用工信息
     */
    @Override
    public EmploymentInfo selectEmploymentInfoDetailByEmploymentId(Long employmentId)
    {
        return employmentInfoMapper.selectEmploymentInfoDetailByEmploymentId(employmentId);
    }

    /**
     * 查询相似的用工信息列表
     * 
     * @param employmentInfo 用工信息
     * @param limit 限制数量
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectSimilarEmploymentInfoList(EmploymentInfo employmentInfo, Integer limit)
    {
        return employmentInfoMapper.selectSimilarEmploymentInfoList(employmentInfo, limit);
    }

    /**
     * 获取所有用工类型列表
     * 
     * @return 用工类型列表
     */
    @Override
    public List<String> selectAllEmploymentTypes()
    {
        return employmentInfoMapper.selectAllEmploymentTypes();
    }

    /**
     * 获取所有工作类别列表
     * 
     * @return 工作类别列表
     */
    @Override
    public List<String> selectAllWorkCategories()
    {
        return employmentInfoMapper.selectAllWorkCategories();
    }

    /**
     * 获取所有薪资类型列表
     * 
     * @return 薪资类型列表
     */
    @Override
    public List<String> selectAllSalaryTypes()
    {
        return employmentInfoMapper.selectAllSalaryTypes();
    }

    /**
     * 获取所有区域列表
     * 
     * @return 区域列表
     */
    @Override
    public List<Map<String, String>> selectAllRegions()
    {
        return employmentInfoMapper.selectAllRegions();
    }

    /**
     * 获取所有学历要求列表
     * 
     * @return 学历要求列表
     */
    @Override
    public List<String> selectAllEducationRequirements()
    {
        return employmentInfoMapper.selectAllEducationRequirements();
    }

    /**
     * 根据核心字段搜索用工信息
     * 
     * @param employmentType 用工类型
     * @param workCategory 工作类别
     * @param salaryType 薪资类型
     * @param regionCode 区域代码
     * @param keyword 关键词
     * @return 用工信息集合
     */
    @Override
    public List<EmploymentInfo> selectEmploymentInfoByCoreFields(String employmentType, String workCategory, 
                                                                String salaryType, String regionCode, String keyword)
    {
        return employmentInfoMapper.selectEmploymentInfoByCoreFields(employmentType, workCategory, salaryType, regionCode, keyword);
    }
}
