<template>
    <div class="job-container app-container">
        <!-- 使用 TableList 组件 -->
        <TableList v-if="isTableReady" :columns="tableColumns" :data="tableData" :loading="tableLoading"
            :showIndex="true" :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作"
            operationWidth="200" :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
            :defaultPage="{ pageSize: pageSize, currentPage: currentPage, total: total }"
            @current-change="handleCurrentChange" @size-change="handleSizeChange">
            
            <!-- 左侧按钮插槽 -->
            <template #menu-left>
                <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['monitor:job:add']">
                    新增
                </el-button>
                <el-button type="warning" class="custom-btn" @click="handleExport" v-hasPermi="['monitor:job:export']">
                    导出
                </el-button>
                <el-button type="info" class="custom-btn" @click="handleJobLog" v-hasPermi="['monitor:job:query']">
                    日志
                </el-button>
            </template>

            <!-- 任务分组列插槽 -->
            <template #jobGroup="{ row }">
                <dict-tag :options="sys_job_group" :value="row.jobGroup" />
            </template>

            <!-- 状态列插槽 -->
            <template #status="{ row }">
                <el-switch
                    v-model="row.status"
                    active-value="0"
                    inactive-value="1"
                    @change="handleStatusChange(row)"
                ></el-switch>
            </template>

            <!-- 操作列插槽 -->
            <template #menu="{ row }">
                <div class="operation-btns">
                    <el-tooltip content="修改" placement="top">
                        <el-button type="primary" link class="op-btn" @click="handleEdit(row)" v-hasPermi="['monitor:job:edit']">
                            <el-icon><Edit /></el-icon>
                        </el-button>
                    </el-tooltip>
                    <el-tooltip content="删除" placement="top">
                        <el-button type="danger" link class="op-btn" @click="handleDelete(row)" v-hasPermi="['monitor:job:remove']">
                            <el-icon><Delete /></el-icon>
                        </el-button>
                    </el-tooltip>
                    <el-tooltip content="执行一次" placement="top">
                        <el-button type="success" link class="op-btn" @click="handleRun(row)" v-hasPermi="['monitor:job:changeStatus']">
                            <el-icon><CaretRight /></el-icon>
                        </el-button>
                    </el-tooltip>
                    <el-tooltip content="任务详细" placement="top">
                        <el-button type="info" link class="op-btn" @click="handleView(row)" v-hasPermi="['monitor:job:query']">
                            <el-icon><View /></el-icon>
                        </el-button>
                    </el-tooltip>
                    <el-tooltip content="调度日志" placement="top">
                        <el-button type="warning" link class="op-btn" @click="handleJobLog(row)" v-hasPermi="['monitor:job:query']">
                            <el-icon><Operation /></el-icon>
                        </el-button>
                    </el-tooltip>
                </div>
            </template>
        </TableList>
        <div v-else class="loading-placeholder">
            <el-empty description="正在加载表格配置..."></el-empty>
        </div>

        <!-- 表单弹窗组件 -->
        <JobFormDialog ref="jobFormDialogRef" :formFields="formFields" :formOption="formOption"
            @submit="handleFormSubmit" @cancel="handleFormCancel" />
    </div>
</template>

<script setup name="JobTemplateIndex">
import { ref, reactive, onMounted, getCurrentInstance, computed, nextTick } from 'vue';
import { createJobTableOption } from "@/const/system/job";
import { listJob, getJob, delJob, addJob, updateJob, runJob, changeJobStatus } from "@/api/monitor/job";
import TableList from '@/components/TableList/index.vue';
import JobFormDialog from './JobFormDialog.vue';

const router = useRouter();
const { proxy } = getCurrentInstance();
const { sys_job_group, sys_job_status } = proxy.useDict("sys_job_group", "sys_job_status");

const tableColumns = ref([]);
const searchableColumns = ref([]); // 可搜索的字段列表
const tableLoading = ref(false);
const isTableReady = ref(false);
const formOption = ref({
    dialogWidth: '900px',
    dialogHeight: '70vh'
});
const tableListRef = ref(null);
const jobFormDialogRef = ref(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref({});

// 表格数据
const tableData = ref([]);

// 表单字段配置
const formFields = ref([]);

// 初始化时获取数据
onMounted(() => {
    initializeConfig();
});

// 初始化配置
const initializeConfig = () => {
    try {
        // 获取基础配置
        const baseOption = createJobTableOption(proxy);

        // 保存弹窗配置
        if (baseOption.dialogWidth) {
            formOption.value.dialogWidth = baseOption.dialogWidth;
        }
        if (baseOption.dialogHeight) {
            formOption.value.dialogHeight = baseOption.dialogHeight;
        }

        // 提取表格列配置
        tableColumns.value = extractTableColumns(baseOption);

        // 提取搜索列配置
        searchableColumns.value = extractSearchColumns(baseOption);

        // 提取表单字段配置
        formFields.value = extractFormFields(baseOption);

        isTableReady.value = true;

        // 加载表格数据
        loadData();
    } catch (error) {
        isTableReady.value = false;
        console.error('初始化配置失败:', error);
    }
};

// 提取表格列配置
const extractTableColumns = (config) => {
    if (!config || !config.column || !config.column.length) {
        return [];
    }

    return config.column
        .filter(col => col.showColumn !== false && col.divider !== true)
        .map(col => ({
            prop: col.prop,
            label: col.label,
            search: !!col.search,
            type: col.type,
            dicData: col.dicData,
            width: col.width,
            minWidth: col.minWidth || (!col.width ? 100 : null),
            slot: col.slot || false,
            searchRange: col.searchRange,
            sortable: col.sortable || false,
            align: col.align || 'center'
        }));
};

// 提取搜索列配置
const extractSearchColumns = (config) => {
    if (!config || !config.column || !config.column.length) {
        return [];
    }

    return config.column
        .filter(col => col.search === true || col.search === 1)
        .map(col => ({
            prop: col.prop,
            label: col.label,
            type: col.type || 'input',
            dicData: col.dicData,
            searchMultiple: col.searchMultiple,
            searchRange: col.searchRange,
            searchSlot: col.searchSlot
        }));
};

// 提取表单字段配置
const extractFormFields = (config) => {
    if (!config || !config.column || !config.column.length) {
        return [];
    }

    return config.column.map(col => ({
        ...col,
        required: col.isRequired || (col.rules && col.rules.some(rule => rule.required)) || false,
    }));
};

// 加载表格数据
const loadData = () => {
    tableLoading.value = true;
    
    const queryParams = {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        ...searchParams.value
    };

    listJob(queryParams).then(response => {
        tableData.value = response.rows;
        total.value = response.total;
        
        // 更新TableList组件的分页信息
        updateTablePagination();
        
        tableLoading.value = false;
    }).catch(() => {
        tableLoading.value = false;
    });
};

// 更新表格分页信息
const updateTablePagination = () => {
    if (tableListRef.value && tableListRef.value.page) {
        tableListRef.value.page.total = total.value;
        tableListRef.value.page.currentPage = currentPage.value;
        tableListRef.value.page.pageSize = pageSize.value;
    }
};

// 搜索处理
const handleSearch = (params) => {
    searchParams.value = params;
    currentPage.value = 1;
    loadData();
};

// 重置搜索
const resetSearch = () => {
    searchParams.value = {};
    currentPage.value = 1;
    loadData();
};

// 分页处理
const handleCurrentChange = (page) => {
    currentPage.value = page;
    loadData();
};

const handleSizeChange = (size) => {
    pageSize.value = size;
    currentPage.value = 1;
    loadData();
};

// 任务状态修改
const handleStatusChange = (row) => {
    let text = row.status === "0" ? "启用" : "停用";
    proxy.$modal.confirm('确认要"' + text + '""' + row.jobName + '"任务吗?').then(function () {
        return changeJobStatus(row.jobId, row.status);
    }).then(() => {
        proxy.$modal.msgSuccess(text + "成功");
    }).catch(function () {
        row.status = row.status === "0" ? "1" : "0";
    });
};

// 立即执行一次
const handleRun = (row) => {
    proxy.$modal.confirm('确认要立即执行一次"' + row.jobName + '"任务吗?').then(function () {
        return runJob(row.jobId, row.jobGroup);
    }).then(() => {
        proxy.$modal.msgSuccess("执行成功");
    }).catch(() => {});
};

// 新增
const handleAdd = () => {
    jobFormDialogRef.value.openDialog('add', '新增任务');
};

// 编辑
const handleEdit = (row) => {
    getJob(row.jobId).then(response => {
        jobFormDialogRef.value.openDialog('edit', '修改任务', response.data);
    });
};

// 查看
const handleView = (row) => {
    getJob(row.jobId).then(response => {
        jobFormDialogRef.value.openDialog('view', '任务详细', response.data);
    });
};

// 删除
const handleDelete = (row) => {
    const jobIds = row.jobId;
    proxy.$modal.confirm('是否确认删除定时任务编号为"' + jobIds + '"的数据项?').then(function () {
        return delJob(jobIds);
    }).then(() => {
        loadData();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
};

// 导出
const handleExport = () => {
    proxy.download("monitor/job/export", {
        ...searchParams.value,
    }, `job_${new Date().getTime()}.xlsx`);
};

// 任务日志
const handleJobLog = (row) => {
    const jobId = row?.jobId || 0;
    router.push('/monitor/job-log/index/' + jobId);
};

// 表单提交处理
const handleFormSubmit = ({ type, data }) => {
    if (type === 'add') {
        addJob(data).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            jobFormDialogRef.value.onSubmitSuccess();
            loadData();
        }).catch(() => {
            jobFormDialogRef.value.onSubmitError();
        });
    } else if (type === 'edit') {
        updateJob(data).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            jobFormDialogRef.value.onSubmitSuccess();
            loadData();
        }).catch(() => {
            jobFormDialogRef.value.onSubmitError();
        });
    }
};

// 表单取消处理
const handleFormCancel = () => {
    // 可以在这里添加取消时的逻辑
};
</script>

<style lang="scss" scoped>
.job-container {
    .custom-btn {
        margin-right: 10px;
    }

    .loading-placeholder {
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .operation-btns {
        display: flex;
        gap: 2px;
        
        .op-btn {
            padding: 2px 4px;
            font-size: 12px;
        }
    }
}
</style> 