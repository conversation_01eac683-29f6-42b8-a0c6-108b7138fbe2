<template>
  <!-- 自定义PDF预览弹窗组件 -->
  <div v-if="visible" class="custom-dialog-overlay">
    <div class="custom-dialog-container" :style="containerStyle">
      <div class="custom-dialog-header">
        <h3>{{ title }}</h3>
        <div class="header-actions">
          <!-- 缩放功能已移除 -->
          <el-tooltip :content="isFullscreen ? '退出全屏' : '全屏'" placement="top" :hide-after="1500">
            <el-button type="primary" link class="action-btn" @click="toggleFullscreen">
              <el-icon>
                <FullScreen v-if="!isFullscreen" />
                <svg v-else viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-78e17ca8="" class="exit-fullscreen-icon">
                  <path fill="currentColor" d="M160 96.064l192 .192a32 32 0 0 1 0 64l-192-.192V352a32 32 0 0 1-64 0V96h64v.064zm0 831.872V928H96V672a32 32 0 1 1 64 0v191.936l192-.192a32 32 0 1 1 0 64l-192 .192zM864 96.064V96h64v256a32 32 0 1 1-64 0V160.064l-192 .192a32 32 0 1 1 0-64l192-.192zm0 831.872-192-.192a32 32 0 0 1 0-64l192 .192V672a32 32 0 1 1 64 0v256h-64v-.064z"></path>
                </svg>
              </el-icon>
            </el-button>
          </el-tooltip>
          <el-button type="primary" link @click="closeDialog" class="close-btn">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
      <div :style="contentStyle" class="content-wrapper">
        <div class="custom-dialog-body">
          <vue-office-pdf v-if="fileUrl" :src="fileUrl" :style="pdfStyle" />
          <div v-else class="loading-template">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onBeforeUnmount, computed } from 'vue';
import VueOfficePdf from '@vue-office/pdf';
import { Loading, Close, FullScreen } from '@element-plus/icons-vue';

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '预览'
  },
  fileUrl: {
    type: String,
    default: ''
  },
  width: {
    type: [String, Number],
    default: '50%'
  }
});

// 定义emit
const emit = defineEmits(['update:visible', 'close']);

// 全屏状态
const isFullscreen = ref(false);

// 容器样式
const containerStyle = computed(() => {
  if (isFullscreen.value) {
    return {
      width: '100%',
      height: '100%',
      maxWidth: '100%',
      margin: 0,
      borderRadius: 0
    };
  }
  return {
    width: typeof props.width === 'number' ? `${props.width}px` : props.width
  };
});

// 内容区域样式
const contentStyle = computed(() => {
  return {
    height: isFullscreen.value ? 'calc(100vh - 60px)' : '80vh',
    overflow: 'hidden'
  };
});

// PDF样式
const pdfStyle = computed(() => {
  return {
    width: '100%',
    height: '100%'
  };
});

// 关闭对话框
const closeDialog = () => {
  // 如果是全屏状态，先退出全屏
  if (isFullscreen.value) {
    toggleFullscreen();
  }
  emit('update:visible', false);
  emit('close');
};

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
};

// 添加键盘ESC关闭功能
watch(() => props.visible, (val) => {
  if (val) {
    // 添加键盘事件监听
    document.addEventListener('keydown', handleEscKey);
  } else {
    // 移除键盘事件监听
    document.removeEventListener('keydown', handleEscKey);
    // 重置状态
    isFullscreen.value = false;
  }
});

// 处理ESC键按下事件
const handleEscKey = (event) => {
  if (event.key === 'Escape') {
    // 如果处于全屏状态，先退出全屏
    if (isFullscreen.value) {
      toggleFullscreen();
      return;
    }
    closeDialog();
  }
};

// 确保组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleEscKey);
});
</script>

<style scoped>
/* 自定义弹窗样式 */
.custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-dialog-container {
  width: 1200px;
  max-width: 95%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

.custom-dialog-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e4e7ed;
  min-height: 24px;
}

.custom-dialog-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  padding: 0;
  font-size: 16px;
  color: #606266;
}

.action-btn:hover {
  color: #409EFF;
}

.action-btn[disabled] {
  color: #C0C4CC;
  cursor: not-allowed;
}

.close-btn {
  padding: 0;
  font-size: 20px;
  margin-left: 8px;
}

.content-wrapper {
  position: relative;
  transition: height 0.3s ease;
}

.custom-dialog-body {
  flex: 1;
  height: 100%;
  width: 100%;
  padding: 0;
  overflow: auto;
  position: relative;
}

.exit-fullscreen-icon {
  width: 1em;
  height: 1em;
}

/* 模板加载状态样式 */
.loading-template {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  gap: 10px;
}
</style>
