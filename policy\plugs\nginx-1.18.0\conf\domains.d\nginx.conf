server {
        listen       80;
        server_name  localhost;

        server_name_in_redirect off;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        fastcgi_connect_timeout 1200;
 

        location /sux-admin/ {
			proxy_pass http://127.0.0.1:7001/;

			# 基础超时设置
			proxy_connect_timeout 60s;    # 后端连接超时(默认60s)
			proxy_send_timeout 20s;     # 发送数据超时(默认60s)
			proxy_read_timeout 20s;     # 读取响应超时(默认60s)
        }  
		
        location /sux-app/ {
			proxy_pass http://127.0.0.1:7002/;
		   
		   # 基础超时设置
			proxy_connect_timeout 60s;    # 后端连接超时(默认60s)
			proxy_send_timeout 20s;     # 发送数据超时(默认60s)
			proxy_read_timeout 20s;     # 读取响应超时(默认60s)
        }
		
		location /sux-file/ {
			alias E:/;
			autoindex off;
			
			# 禁用路径转换正则表达式
			# 直接依赖Nginx的路径解析能力
			
			# 添加MIME类型识别
			types {
				image/jpeg jpg jpeg;
				image/png png;
				image/gif gif;
				application/pdf pdf;
			}
			default_type image/png;
		}



}

