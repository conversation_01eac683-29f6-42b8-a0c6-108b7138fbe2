import request from '@/utils/request'

// 查询培训报名列表
export function listTrainingApplication(query) {
  return request({
    url: '/training/application/list',
    method: 'get',
    params: query
  })
}

// 查询培训报名详细
export function getTrainingApplication(applicationId) {
  return request({
    url: '/training/application/' + applicationId,
    method: 'get'
  })
}

// 新增培训报名
export function addTrainingApplication(data) {
  return request({
    url: '/training/application',
    method: 'post',
    data: data
  })
}

// 修改培训报名
export function updateTrainingApplication(data) {
  return request({
    url: '/training/application',
    method: 'put',
    data: data
  })
}

// 删除培训报名
export function delTrainingApplication(applicationId) {
  return request({
    url: '/training/application/' + applicationId,
    method: 'delete'
  })
}

// 审核培训报名
export function reviewTrainingApplication(applicationId, status, reviewComment) {
  return request({
    url: '/training/application/review/' + applicationId,
    method: 'put',
    params: {
      status: status,
      reviewComment: reviewComment
    }
  })
}

// 批量审核培训报名
export function batchReviewTrainingApplication(applicationIds, status, reviewComment) {
  return request({
    url: '/training/application/batch-review',
    method: 'put',
    params: {
      applicationIds: applicationIds,
      status: status,
      reviewComment: reviewComment
    }
  })
}

// 取消培训报名
export function cancelTrainingApplication(applicationId) {
  return request({
    url: '/training/application/cancel/' + applicationId,
    method: 'put'
  })
}

// 获取某个培训订单的报名列表
export function getApplicationsByOrderId(orderId) {
  return request({
    url: '/training/application/order/' + orderId,
    method: 'get'
  })
}

// 统计某个培训订单的报名人数
export function countApplicationsByOrderId(orderId) {
  return request({
    url: '/training/application/count/' + orderId,
    method: 'get'
  })
}

// ========== 公开API接口（无需登录） ==========

// 提交培训报名申请（公开接口）
export function submitTrainingApplication(data) {
  return request({
    url: '/public/training/application/submit',
    method: 'post',
    data: data
  })
}

// 检查用户报名状态（公开接口）
export function checkApplicationStatus(orderId, phone, userId) {
  return request({
    url: '/public/training/application/check-status',
    method: 'get',
    params: {
      orderId: orderId,
      phone: phone,
      userId: userId
    }
  })
}

// 获取当前登录用户的报名状态
export function getMyApplicationStatus(orderId) {
  return request({
    url: '/public/training/application/my-status/' + orderId,
    method: 'get'
  })
}

// 获取当前登录用户的所有报名记录
export function getMyApplications() {
  return request({
    url: '/public/training/application/my-applications',
    method: 'get'
  })
}

// 取消我的报名
export function cancelMyApplication(applicationId) {
  return request({
    url: '/public/training/application/cancel/' + applicationId,
    method: 'put'
  })
}

// 获取培训订单的报名统计信息（公开接口）
export function getApplicationStatistics(orderId) {
  return request({
    url: '/public/training/application/statistics/' + orderId,
    method: 'get'
  })
}
