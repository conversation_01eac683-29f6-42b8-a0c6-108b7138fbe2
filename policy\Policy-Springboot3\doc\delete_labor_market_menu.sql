-- 删除劳务市场相关菜单SQL脚本
-- 注意：执行前请备份数据库，删除操作不可逆

-- ==================== 删除劳务市场菜单及权限 ====================

-- 1. 删除劳务市场的所有权限按钮 (4301-4399)
DELETE FROM `sys_menu` WHERE `menu_id` BETWEEN 4301 AND 4399;

-- 2. 删除劳务市场主菜单 (4003)
DELETE FROM `sys_menu` WHERE `menu_id` = 4003;

-- ==================== 删除具体菜单项（明确指定） ====================

-- 删除劳务市场权限按钮
DELETE FROM `sys_menu` WHERE `menu_id` = 4301 AND `menu_name` = '劳务市场查询';
DELETE FROM `sys_menu` WHERE `menu_id` = 4302 AND `menu_name` = '劳务市场新增';
DELETE FROM `sys_menu` WHERE `menu_id` = 4303 AND `menu_name` = '劳务市场修改';
DELETE FROM `sys_menu` WHERE `menu_id` = 4304 AND `menu_name` = '劳务市场删除';
DELETE FROM `sys_menu` WHERE `menu_id` = 4305 AND `menu_name` = '劳务市场导出';
DELETE FROM `sys_menu` WHERE `menu_id` = 4306 AND `menu_name` = '劳务市场审核';
DELETE FROM `sys_menu` WHERE `menu_id` = 4307 AND `menu_name` = '劳务市场统计';

-- 删除劳务市场主菜单
DELETE FROM `sys_menu` WHERE `menu_id` = 4003 AND `menu_name` = '劳务市场';

-- ==================== 按权限标识删除（更安全的方式） ====================

-- 删除所有包含 'place:market' 权限标识的菜单
DELETE FROM `sys_menu` WHERE `perms` LIKE 'place:market:%';

-- 删除劳务市场主菜单（通过路径和组件路径识别）
DELETE FROM `sys_menu` WHERE `path` = 'market' AND `component` = 'place/market/index';

-- ==================== 清理角色权限关联 ====================

-- 删除角色菜单关联表中的相关记录（如果存在sys_role_menu表）
-- DELETE FROM `sys_role_menu` WHERE `menu_id` BETWEEN 4301 AND 4399;
-- DELETE FROM `sys_role_menu` WHERE `menu_id` = 4003;

-- ==================== 验证删除结果 ====================

-- 查询确认劳务市场相关菜单已删除
SELECT 
    menu_id, 
    menu_name, 
    parent_id, 
    path, 
    component, 
    perms, 
    menu_type
FROM `sys_menu` 
WHERE `menu_name` LIKE '%劳务市场%' 
   OR `perms` LIKE '%market%' 
   OR `menu_id` BETWEEN 4301 AND 4399
   OR `menu_id` = 4003;

-- 查询剩余的场地管理菜单结构
SELECT 
    menu_id, 
    menu_name, 
    parent_id, 
    path, 
    component, 
    perms, 
    menu_type,
    order_num
FROM `sys_menu` 
WHERE `menu_id` BETWEEN 4000 AND 4999 
ORDER BY menu_id;

-- ==================== 备份恢复SQL（如需恢复） ====================
/*
-- 如果需要恢复劳务市场菜单，可以执行以下SQL：

-- 恢复劳务市场主菜单
INSERT INTO `sys_menu` VALUES (4003, '劳务市场', 4000, 3, 'market', 'place/market/index', '', '', 1, 0, 'C', '0', '0', 'place:market:list', 'shopping', 1, NOW(), 1, NULL, '劳务市场管理菜单');

-- 恢复劳务市场基本权限
INSERT INTO `sys_menu` VALUES (4301, '劳务市场查询', 4003, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:query', '#', 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4302, '劳务市场新增', 4003, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:add', '#', 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4303, '劳务市场修改', 4003, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:edit', '#', 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4304, '劳务市场删除', 4003, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:remove', '#', 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4305, '劳务市场导出', 4003, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:export', '#', 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4306, '劳务市场审核', 4003, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:review', '#', 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4307, '劳务市场统计', 4003, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:statistics', '#', 1, NOW(), 1, NULL, '');
*/

-- ==================== 使用说明 ====================
/*
执行顺序：
1. 先备份数据库
2. 执行删除SQL
3. 执行验证查询确认删除成功
4. 清除浏览器缓存并重新登录系统
5. 检查前端菜单是否正确更新

注意事项：
- 删除菜单后，相关的角色权限也会失效
- 如果有用户正在使用相关功能，建议在维护时间执行
- 前端Vue组件文件不会被删除，只是菜单入口被移除
- 后端Controller和Service代码也不会受影响
*/
