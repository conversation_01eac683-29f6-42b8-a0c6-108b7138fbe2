package com.sux.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.StringUtils;
import com.sux.system.domain.JobPosting;
import com.sux.system.domain.WorkerProfile;
import com.sux.system.mapper.JobPostingMapper;
import com.sux.system.mapper.WorkerProfileMapper;
import com.sux.system.service.IWorkerProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 零工信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
public class WorkerProfileServiceImpl extends ServiceImpl<WorkerProfileMapper, WorkerProfile> implements IWorkerProfileService
{
    @Autowired
    private WorkerProfileMapper workerProfileMapper;
    
    @Autowired
    private JobPostingMapper jobPostingMapper;

    /**
     * 查询零工信息列表
     * 
     * @param workerProfile 零工信息
     * @return 零工信息
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileList(WorkerProfile workerProfile)
    {
        return workerProfileMapper.selectWorkerProfileList(workerProfile);
    }

    /**
     * 查询零工信息
     * 
     * @param workerId 零工信息主键
     * @return 零工信息
     */
    @Override
    public WorkerProfile selectWorkerProfileByWorkerId(Long workerId)
    {
        return workerProfileMapper.selectWorkerProfileByWorkerId(workerId);
    }

    /**
     * 根据用户ID查询零工信息
     * 
     * @param userId 用户ID
     * @return 零工信息
     */
    @Override
    public WorkerProfile selectWorkerProfileByUserId(Long userId)
    {
        return workerProfileMapper.selectWorkerProfileByUserId(userId);
    }

    /**
     * 新增零工信息
     * 
     * @param workerProfile 零工信息
     * @return 结果
     */
    @Override
    public int insertWorkerProfile(WorkerProfile workerProfile)
    {
        workerProfile.setCreateId(SecurityUtils.getUserId());
        workerProfile.setCreateTime(DateUtils.getNowDate());
        workerProfile.setUserId(SecurityUtils.getUserId());
        
        // 设置默认值
        if (StringUtils.isEmpty(workerProfile.getStatus())) {
            workerProfile.setStatus("active");
        }
        if (workerProfile.getRatingAverage() == null) {
            workerProfile.setRatingAverage(new BigDecimal("0.00"));
        }
        if (workerProfile.getRatingCount() == null) {
            workerProfile.setRatingCount(0);
        }
        if (workerProfile.getCompletedJobs() == null) {
            workerProfile.setCompletedJobs(0);
        }
        if (workerProfile.getSuccessRate() == null) {
            workerProfile.setSuccessRate(new BigDecimal("0.00"));
        }
        if (workerProfile.getIsVerified() == null) {
            workerProfile.setIsVerified(0);
        }
        // 设置最后活跃时间
        workerProfile.setLastActiveTime(DateUtils.getNowDate());
        
        return workerProfileMapper.insertWorkerProfile(workerProfile);
    }

    /**
     * 修改零工信息
     * 
     * @param workerProfile 零工信息
     * @return 结果
     */
    @Override
    public int updateWorkerProfile(WorkerProfile workerProfile)
    {
        workerProfile.setUpdateId(SecurityUtils.getUserId());
        workerProfile.setUpdateTime(DateUtils.getNowDate());
        return workerProfileMapper.updateWorkerProfile(workerProfile);
    }

    /**
     * 批量删除零工信息
     * 
     * @param workerIds 需要删除的零工信息主键
     * @return 结果
     */
    @Override
    public int deleteWorkerProfileByWorkerIds(Long[] workerIds)
    {
        return workerProfileMapper.deleteWorkerProfileByWorkerIds(workerIds);
    }

    /**
     * 删除零工信息信息
     * 
     * @param workerId 零工信息主键
     * @return 结果
     */
    @Override
    public int deleteWorkerProfileByWorkerId(Long workerId)
    {
        return workerProfileMapper.deleteWorkerProfileByWorkerId(workerId);
    }

    /**
     * 查询活跃的零工信息列表
     * 
     * @param workerProfile 零工信息
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectActiveWorkerProfileList(WorkerProfile workerProfile)
    {
        return workerProfileMapper.selectActiveWorkerProfileList(workerProfile);
    }

    /**
     * 查询已验证的零工信息列表
     * 
     * @param workerProfile 零工信息
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectVerifiedWorkerProfileList(WorkerProfile workerProfile)
    {
        return workerProfileMapper.selectVerifiedWorkerProfileList(workerProfile);
    }

    /**
     * 激活零工信息
     * 
     * @param workerId 零工信息ID
     * @return 结果
     */
    @Override
    public int activateWorkerProfile(Long workerId)
    {
        WorkerProfile workerProfile = new WorkerProfile();
        workerProfile.setWorkerId(workerId);
        workerProfile.setStatus("active");
        workerProfile.setUpdateId(SecurityUtils.getUserId());
        workerProfile.setUpdateTime(DateUtils.getNowDate());
        return workerProfileMapper.updateWorkerProfile(workerProfile);
    }

    /**
     * 停用零工信息
     * 
     * @param workerId 零工信息ID
     * @return 结果
     */
    @Override
    public int deactivateWorkerProfile(Long workerId)
    {
        WorkerProfile workerProfile = new WorkerProfile();
        workerProfile.setWorkerId(workerId);
        workerProfile.setStatus("inactive");
        workerProfile.setUpdateId(SecurityUtils.getUserId());
        workerProfile.setUpdateTime(DateUtils.getNowDate());
        return workerProfileMapper.updateWorkerProfile(workerProfile);
    }

    /**
     * 暂停零工信息
     * 
     * @param workerId 零工信息ID
     * @return 结果
     */
    @Override
    public int suspendWorkerProfile(Long workerId)
    {
        WorkerProfile workerProfile = new WorkerProfile();
        workerProfile.setWorkerId(workerId);
        workerProfile.setStatus("suspended");
        workerProfile.setUpdateId(SecurityUtils.getUserId());
        workerProfile.setUpdateTime(DateUtils.getNowDate());
        return workerProfileMapper.updateWorkerProfile(workerProfile);
    }

    /**
     * 禁用零工信息
     * 
     * @param workerId 零工信息ID
     * @return 结果
     */
    @Override
    public int banWorkerProfile(Long workerId)
    {
        WorkerProfile workerProfile = new WorkerProfile();
        workerProfile.setWorkerId(workerId);
        workerProfile.setStatus("banned");
        workerProfile.setUpdateId(SecurityUtils.getUserId());
        workerProfile.setUpdateTime(DateUtils.getNowDate());
        return workerProfileMapper.updateWorkerProfile(workerProfile);
    }

    /**
     * 验证零工信息
     * 
     * @param workerId 零工信息ID
     * @return 结果
     */
    @Override
    public int verifyWorkerProfile(Long workerId)
    {
        WorkerProfile workerProfile = new WorkerProfile();
        workerProfile.setWorkerId(workerId);
        workerProfile.setIsVerified(1);
        workerProfile.setVerificationTime(DateUtils.getNowDate());
        workerProfile.setUpdateId(SecurityUtils.getUserId());
        workerProfile.setUpdateTime(DateUtils.getNowDate());
        return workerProfileMapper.updateWorkerProfile(workerProfile);
    }

    /**
     * 更新零工最后活跃时间
     * 
     * @param workerId 零工ID
     * @return 结果
     */
    @Override
    public int updateLastActiveTime(Long workerId)
    {
        return workerProfileMapper.updateWorkerProfileLastActiveTime(workerId);
    }

    /**
     * 更新零工评分信息
     * 
     * @param workerId 零工ID
     * @param rating 新评分
     * @return 结果
     */
    @Override
    public int updateWorkerRating(Long workerId, Double rating)
    {
        WorkerProfile workerProfile = selectWorkerProfileByWorkerId(workerId);
        if (workerProfile == null) {
            return 0;
        }
        
        // 计算新的平均评分
        int currentCount = workerProfile.getRatingCount() != null ? workerProfile.getRatingCount() : 0;
        double currentAverage = workerProfile.getRatingAverage() != null ? workerProfile.getRatingAverage().doubleValue() : 0.0;
        
        double newAverage = (currentAverage * currentCount + rating) / (currentCount + 1);
        int newCount = currentCount + 1;
        
        return workerProfileMapper.updateWorkerProfileRating(workerId, newAverage, newCount);
    }

    /**
     * 更新零工完成工作统计
     * 
     * @param workerId 零工ID
     * @param isSuccess 是否成功完成
     * @return 结果
     */
    @Override
    public int updateWorkerJobStats(Long workerId, Boolean isSuccess)
    {
        WorkerProfile workerProfile = selectWorkerProfileByWorkerId(workerId);
        if (workerProfile == null) {
            return 0;
        }
        
        int currentCompleted = workerProfile.getCompletedJobs() != null ? workerProfile.getCompletedJobs() : 0;
        double currentSuccessRate = workerProfile.getSuccessRate() != null ? workerProfile.getSuccessRate().doubleValue() : 0.0;
        
        int newCompleted = currentCompleted + 1;
        double newSuccessRate;
        
        if (currentCompleted == 0) {
            newSuccessRate = isSuccess ? 100.0 : 0.0;
        } else {
            int successfulJobs = (int) Math.round(currentCompleted * currentSuccessRate / 100.0);
            if (isSuccess) {
                successfulJobs++;
            }
            newSuccessRate = (double) successfulJobs / newCompleted * 100.0;
        }
        
        return workerProfileMapper.updateWorkerProfileJobStats(workerId, newCompleted, newSuccessRate);
    }

    /**
     * 查询高评分零工信息列表
     *
     * @param minRating 最低评分
     * @param limit 限制数量
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectHighRatedWorkerProfileList(Double minRating, Integer limit)
    {
        return workerProfileMapper.selectHighRatedWorkerProfileList(minRating, limit);
    }

    /**
     * 查询经验丰富的零工信息列表
     *
     * @param minExperience 最少经验年数
     * @param limit 限制数量
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectExperiencedWorkerProfileList(Integer minExperience, Integer limit)
    {
        return workerProfileMapper.selectExperiencedWorkerProfileList(minExperience, limit);
    }

    /**
     * 根据工作类别统计零工数量
     *
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectWorkerProfileCountByCategory()
    {
        return workerProfileMapper.selectWorkerProfileCountByCategory();
    }

    /**
     * 根据所在地统计零工数量
     *
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectWorkerProfileCountByLocation()
    {
        return workerProfileMapper.selectWorkerProfileCountByLocation();
    }

    /**
     * 根据学历统计零工数量
     *
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectWorkerProfileCountByEducation()
    {
        return workerProfileMapper.selectWorkerProfileCountByEducation();
    }

    /**
     * 根据经验年数统计零工数量
     *
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectWorkerProfileCountByExperience()
    {
        return workerProfileMapper.selectWorkerProfileCountByExperience();
    }

    /**
     * 根据关键词搜索零工信息
     *
     * @param keyword 关键词
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileByKeyword(String keyword)
    {
        return workerProfileMapper.selectWorkerProfileByKeyword(keyword);
    }

    /**
     * 查询推荐零工信息
     *
     * @param limit 限制数量
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectRecommendedWorkerProfileList(Integer limit)
    {
        return workerProfileMapper.selectRecommendedWorkerProfileList(limit);
    }

    /**
     * 查询新注册的零工信息
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectNewWorkerProfileList(Integer days, Integer limit)
    {
        return workerProfileMapper.selectNewWorkerProfileList(days, limit);
    }

    /**
     * 查询即将可工作的零工信息
     *
     * @param days 天数
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileAvailableSoon(Integer days)
    {
        return workerProfileMapper.selectWorkerProfileAvailableSoon(days);
    }

    /**
     * 批量更新零工状态
     *
     * @param workerIds 零工ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    public int batchUpdateWorkerProfileStatus(Long[] workerIds, String status)
    {
        return workerProfileMapper.batchUpdateWorkerProfileStatus(workerIds, status);
    }

    /**
     * 查询零工统计数据
     *
     * @param userId 用户ID（可选）
     * @return 统计数据
     */
    @Override
    public Map<String, Object> selectWorkerProfileStatistics(Long userId)
    {
        return workerProfileMapper.selectWorkerProfileStatistics(userId);
    }

    /**
     * 查询零工详情（包含用户信息）
     *
     * @param workerId 零工ID
     * @return 零工详情
     */
    @Override
    public WorkerProfile selectWorkerProfileDetailByWorkerId(Long workerId)
    {
        return workerProfileMapper.selectWorkerProfileDetailByWorkerId(workerId);
    }

    /**
     * 根据技能匹配零工信息
     *
     * @param skills 技能列表
     * @param limit 限制数量
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileBySkills(List<String> skills, Integer limit)
    {
        return workerProfileMapper.selectWorkerProfileBySkills(skills, limit);
    }

    /**
     * 根据地理位置匹配零工信息
     *
     * @param location 地理位置
     * @param radius 半径（公里）
     * @param limit 限制数量
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileByLocation(String location, Double radius, Integer limit)
    {
        return workerProfileMapper.selectWorkerProfileByLocation(location, radius, limit);
    }

    /**
     * 根据薪资期望匹配零工信息
     *
     * @param salaryMin 最低薪资
     * @param salaryMax 最高薪资
     * @param salaryType 薪资类型
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileBySalaryExpectation(Double salaryMin, Double salaryMax, String salaryType)
    {
        return workerProfileMapper.selectWorkerProfileBySalaryExpectation(salaryMin, salaryMax, salaryType);
    }

    /**
     * 查询相似的零工信息
     *
     * @param workerProfile 零工信息
     * @param limit 限制数量
     * @return 零工信息集合
     */
    @Override
    public List<WorkerProfile> selectSimilarWorkerProfileList(WorkerProfile workerProfile, Integer limit)
    {
        return workerProfileMapper.selectSimilarWorkerProfileList(workerProfile, limit);
    }

    /**
     * 根据招聘信息匹配零工信息
     *
     * @param jobId 招聘信息ID
     * @param limit 限制数量
     * @return 匹配的零工信息集合
     */
    @Override
    public List<WorkerProfile> matchWorkerProfileForJob(Long jobId, Integer limit)
    {
        // 获取招聘信息
        JobPosting jobPosting = jobPostingMapper.selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return null;
        }

        // 构建匹配参数
        Map<String, Object> matchParams = new java.util.HashMap<>();

        // 工作地点匹配
        if (StringUtils.isNotEmpty(jobPosting.getWorkLocation())) {
            matchParams.put("workLocation", jobPosting.getWorkLocation());
        }

        // 工作类别匹配
        if (StringUtils.isNotEmpty(jobPosting.getJobCategory())) {
            matchParams.put("jobCategory", jobPosting.getJobCategory());
        }

        // 薪资匹配
        if (jobPosting.getSalaryMin() != null) {
            matchParams.put("salaryMin", jobPosting.getSalaryMin());
        }
        if (jobPosting.getSalaryMax() != null) {
            matchParams.put("salaryMax", jobPosting.getSalaryMax());
        }
        if (StringUtils.isNotEmpty(jobPosting.getSalaryType())) {
            matchParams.put("salaryType", jobPosting.getSalaryType());
        }

        // 工作类型匹配
        if (StringUtils.isNotEmpty(jobPosting.getJobType())) {
            matchParams.put("jobType", jobPosting.getJobType());
        }

        // 学历要求匹配
        if (StringUtils.isNotEmpty(jobPosting.getEducationRequired())) {
            matchParams.put("educationRequired", jobPosting.getEducationRequired());
        }

        // 经验要求匹配
        if (StringUtils.isNotEmpty(jobPosting.getExperienceRequired())) {
            matchParams.put("experienceRequired", jobPosting.getExperienceRequired());
        }

        // 工作时间匹配
        if (jobPosting.getStartDate() != null) {
            matchParams.put("startDate", jobPosting.getStartDate());
        }
        if (jobPosting.getEndDate() != null) {
            matchParams.put("endDate", jobPosting.getEndDate());
        }

        matchParams.put("limit", limit);
        matchParams.put("status", "active");

        return workerProfileMapper.selectWorkerProfileByMatchParams(matchParams);
    }

    /**
     * 根据匹配参数查询零工信息
     *
     * @param matchParams 匹配参数
     * @return 匹配的零工信息集合
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileByMatchParams(Map<String, Object> matchParams)
    {
        return workerProfileMapper.selectWorkerProfileByMatchParams(matchParams);
    }

    /**
     * 智能推荐零工信息
     *
     * @param employerId 雇主ID
     * @param limit 限制数量
     * @return 推荐的零工信息集合
     */
    @Override
    public List<WorkerProfile> recommendWorkerProfileForEmployer(Long employerId, Integer limit)
    {
        // 获取雇主最近发布的招聘信息
        JobPosting jobPosting = new JobPosting();
        jobPosting.setPublisherUserId(employerId);
        List<JobPosting> recentJobs = jobPostingMapper.selectMyJobPostingList(jobPosting, employerId);

        if (recentJobs != null && !recentJobs.isEmpty()) {
            // 基于最新的招聘信息进行匹配
            return matchWorkerProfileForJob(recentJobs.get(0).getJobId(), limit);
        }

        // 如果没有招聘信息，返回推荐零工信息
        return selectRecommendedWorkerProfileList(limit);
    }

    /**
     * 校验零工信息是否可以编辑
     *
     * @param workerId 零工信息ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean checkWorkerProfileEditable(Long workerId, Long userId)
    {
        WorkerProfile workerProfile = selectWorkerProfileByWorkerId(workerId);
        if (workerProfile == null) {
            return false;
        }

        // 只有本人可以编辑
        if (!workerProfile.getUserId().equals(userId)) {
            return false;
        }

        // 被禁用的零工信息不能编辑
        if ("banned".equals(workerProfile.getStatus())) {
            return false;
        }

        return true;
    }

    /**
     * 校验零工信息是否可以删除
     *
     * @param workerId 零工信息ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean checkWorkerProfileDeletable(Long workerId, Long userId)
    {
        WorkerProfile workerProfile = selectWorkerProfileByWorkerId(workerId);
        if (workerProfile == null) {
            return false;
        }

        // 只有本人可以删除
        if (!workerProfile.getUserId().equals(userId)) {
            return false;
        }

        // 有完成工作记录的零工信息不能删除
        if (workerProfile.getCompletedJobs() != null && workerProfile.getCompletedJobs() > 0) {
            return false;
        }

        return true;
    }

    /**
     * 校验用户是否已有零工信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean checkUserHasWorkerProfile(Long userId)
    {
        WorkerProfile workerProfile = selectWorkerProfileByUserId(userId);
        return workerProfile != null;
    }

    // ==================== 核心匹配优化方法实现 ====================

    /**
     * 基于核心字段搜索零工信息
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileByCoreFields(String jobTypesPreferred, String workCategories,
                                                              String salaryTypePreference, String educationLevel, String keyword) {
        return workerProfileMapper.selectWorkerProfileByCoreFields(jobTypesPreferred, workCategories,
                                                                   salaryTypePreference, educationLevel, keyword);
    }

    /**
     * 根据偏好工作类型查询零工信息
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileByJobType(String jobType) {
        return workerProfileMapper.selectWorkerProfileByJobType(jobType);
    }

    /**
     * 根据工作类别偏好查询零工信息
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileByJobCategory(String jobCategory) {
        return workerProfileMapper.selectWorkerProfileByJobCategory(jobCategory);
    }

    /**
     * 根据薪资类型偏好查询零工信息
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileBySalaryType(String salaryType) {
        return workerProfileMapper.selectWorkerProfileBySalaryType(salaryType);
    }

    /**
     * 根据学历水平查询零工信息
     */
    @Override
    public List<WorkerProfile> selectWorkerProfileByEducation(String educationLevel) {
        return workerProfileMapper.selectWorkerProfileByEducation(educationLevel);
    }

    /**
     * 获取所有偏好工作类型列表
     */
    @Override
    public List<String> selectAllJobTypesPreferred() {
        return workerProfileMapper.selectAllJobTypesPreferred();
    }

    /**
     * 获取所有工作类别偏好列表
     */
    @Override
    public List<String> selectAllWorkCategories() {
        return workerProfileMapper.selectAllWorkCategories();
    }

    /**
     * 获取所有薪资类型偏好列表
     */
    @Override
    public List<String> selectAllSalaryTypePreferences() {
        return workerProfileMapper.selectAllSalaryTypePreferences();
    }

    /**
     * 获取所有学历水平列表
     */
    @Override
    public List<String> selectAllEducationLevels() {
        return workerProfileMapper.selectAllEducationLevels();
    }
}
