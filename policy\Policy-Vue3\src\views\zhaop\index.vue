<template>
  <div class="job-posting-container app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="jobPostingList" :loading="tableLoading" :showIndex="true"
      :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作" operationWidth="200"
      :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['job:posting:add']">新 增</el-button>
        <el-button type="warning" plain class="custom-btn" @click="handleExport" v-hasPermi="['job:posting:export']">导 出</el-button>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)" size="small">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <!-- 工作类型列插槽 -->
      <template #jobType="{ row }">
        <el-tag :type="getJobTypeTagType(row.jobType)" size="small">
          {{ row.jobType }}
        </el-tag>
      </template>

      <!-- 工作类别列插槽 -->
      <template #jobCategory="{ row }">
        <el-tag :type="getCategoryTagType(row.jobCategory)" size="small">
          {{ row.jobCategory }}
        </el-tag>
      </template>

      <!-- 薪资范围列插槽 -->
      <template #salaryRange="{ row }">
        <span v-if="row.salaryMin && row.salaryMax">
          {{ row.salaryMin }}-{{ row.salaryMax }} {{ getSalaryTypeText(row.salaryType) }}
        </span>
        <span v-else-if="row.salaryMin">
          {{ row.salaryMin }}+ {{ getSalaryTypeText(row.salaryType) }}
        </span>
        <span v-else>面议</span>
      </template>

      <!-- 招聘人数列插槽 -->
      <template #positionsCount="{ row }">
        {{ row.positionsFilled || 0 }}/{{ row.positionsAvailable || 0 }}
      </template>

      <!-- 紧急程度列插槽 -->
      <template #urgencyLevel="{ row }">
        <el-tag :type="getUrgencyTagType(row.urgencyLevel)" size="small">
          {{ getUrgencyText(row.urgencyLevel) }}
        </el-tag>
      </template>

      <!-- 浏览申请列插槽 -->
      <template #viewApplication="{ row }">
        {{ row.viewCount || 0 }}/{{ row.applicationCount || 0 }}
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button type="primary" link @click="handleUpdate(row)" v-hasPermi="['job:posting:edit']">编辑</el-button>
          <el-button v-if="row.status === 'draft'" type="success" link @click="handlePublish(row)" v-hasPermi="['job:posting:publish']">发布</el-button>
          <el-button v-if="row.status === 'published'" type="warning" link @click="handlePause(row)" v-hasPermi="['job:posting:pause']">暂停</el-button>
          <el-button type="danger" link @click="handleDelete(row)" v-hasPermi="['job:posting:remove']">删除</el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..."></el-empty>
    </div>

    <!-- 表单弹窗组件 -->
    <JobPostingFormDialog ref="jobPostingFormDialogRef" :formFields="formFields" :formOption="formOption"
      @submit="handleFormSubmit" @cancel="handleFormCancel" />
  </div>
</template>

<script setup name="JobPosting">
import { listJobPosting, getJobPosting, delJobPosting, addJobPosting, updateJobPosting, publishJobPosting, pauseJobPosting, closeJobPosting, completeJobPosting } from "@/api/job/posting"
import { createJobPostingTableOption } from "@/const/job/posting"
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils"
import TableList from '@/components/TableList/index.vue'
import JobPostingFormDialog from './JobPostingFormDialog.vue'

const { proxy } = getCurrentInstance()

const jobPostingList = ref([])
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const isInitializing = ref(true) // 添加初始化标志

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([]) // 可搜索的字段列表
const tableLoading = ref(false)
const isTableReady = ref(false)
const formOption = ref({
  dialogWidth: '800px',
  dialogHeight: '60vh'
})
const tableListRef = ref(null)
const jobPostingFormDialogRef = ref(null)
const formFields = ref([])
const searchParams = ref({})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    jobTitle: undefined,
    jobType: undefined,
    jobCategory: undefined,
    workLocation: undefined,
    status: undefined,
    companyName: undefined,
    publisherUserName: undefined,
    createTime: undefined
  }
})

const { queryParams } = toRefs(data)

// 初始化配置
onMounted(async () => {
  await initializeConfig()
  getList()
})

// 初始化配置
const initializeConfig = async () => {
  try {
    // 获取基础配置
    const baseOption = createJobPostingTableOption(proxy);

    // 使用工具类获取合并后的配置
    const mergedConfig = await getCoSyncColumn({
      baseOption,
      proxy
    });

    // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
    const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

    // 设置表格和搜索配置
    tableColumns.value = extractedTableColumns;
    searchableColumns.value = searchColumns;

    // 设置表单字段配置
    formFields.value = extractedFormFields;

    // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
    formOption.value = {
      ...formOption.value, // 保留默认配置
      ...formOptions       // 使用从配置文件中提取的完整选项
    };

    isTableReady.value = true;
  } catch (error) {
    isTableReady.value = false;
    console.error('初始化配置失败:', error);
  }
};

/** 查询招聘信息列表 */
function getList() {
  tableLoading.value = true
  loading.value = true
  // 处理日期范围搜索参数
  let params = { ...queryParams.value }
  if (searchParams.value.createTime && Array.isArray(searchParams.value.createTime) && searchParams.value.createTime.length === 2) {
    params = proxy.addDateRange(params, searchParams.value.createTime)
  }

  listJobPosting(params).then(res => {
    tableLoading.value = false
    loading.value = false
    jobPostingList.value = res.rows
    total.value = res.total

    // 数据加载完成后，设置初始化完成
    nextTick(() => {
      isInitializing.value = false
    })
  })
}

// 处理搜索
const handleSearch = (params) => {
  // 标记为初始化状态，防止状态开关误触发
  isInitializing.value = true;

  // 保存搜索参数（包括日期范围）
  searchParams.value = { ...params };

  // 合并搜索参数到queryParams（排除日期范围，因为API需要特殊处理）
  const { createTime, ...otherParams } = params || {};
  Object.assign(queryParams.value, otherParams);
  queryParams.value.pageNum = 1;
  getList();
};

// 重置搜索
const resetSearch = () => {
  // 标记为初始化状态，防止状态开关误触发
  isInitializing.value = true;

  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    jobTitle: undefined,
    jobType: undefined,
    jobCategory: undefined,
    workLocation: undefined,
    status: undefined,
    companyName: undefined,
    publisherUserName: undefined,
    createTime: undefined
  };
  searchParams.value = {};
  getList();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  isInitializing.value = true;
  queryParams.value.pageNum = page;
  getList();
};

// 处理每页条数变化
const handleSizeChange = (size) => {
  isInitializing.value = true;
  queryParams.value.pageSize = size;
  queryParams.value.pageNum = 1;
  getList();
};

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.jobId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

// 查看
const handleView = (row) => {
  jobPostingFormDialogRef.value?.openDialog('view', '查看招聘信息', row)
}

// 编辑
const handleEdit = (row) => {
  const jobId = row.jobId
  getJobPosting(jobId).then(response => {
    jobPostingFormDialogRef.value?.openDialog('edit', '编辑招聘信息', response.data)
  })
}

// 新增
const handleAddJobPosting = () => {
  const defaultData = {
    status: "draft"
  }
  jobPostingFormDialogRef.value?.openDialog('add', '新增招聘信息', defaultData)
}

// 处理表单提交事件
const handleFormSubmit = async (payload) => {
  try {
    if (payload.type === 'add') {
      // 新增
      await addJobPosting(payload.data)
      proxy.$modal.msgSuccess("添加成功")
    } else if (payload.type === 'edit') {
      // 编辑
      await updateJobPosting(payload.data)
      proxy.$modal.msgSuccess("修改成功")
    }

    // 通知子组件提交成功
    jobPostingFormDialogRef.value?.onSubmitSuccess()
    getList()
  } catch (error) {
    // 通知子组件提交失败
    jobPostingFormDialogRef.value?.onSubmitError()
    console.error('提交失败:', error)
  }
}

// 处理表单取消事件
const handleFormCancel = () => {
  // 可以在这里添加取消逻辑
}

/** 新增按钮操作 */
function handleAdd() {
  handleAddJobPosting()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  if (row) {
    handleEdit(row)
  } else {
    // 批量编辑
    const jobId = ids.value[0]
    const selectedRow = jobPostingList.value.find(item => item.jobId === jobId)
    if (selectedRow) {
      handleEdit(selectedRow)
    }
  }
}

/** 发布按钮操作 */
function handlePublish(row) {
  const jobIds = row?.jobId ? [row.jobId] : ids.value
  proxy.$modal.confirm('是否确认发布选中的招聘信息？').then(function () {
    return publishJobPosting(jobIds[0])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("发布成功")
  }).catch(() => {})
}

/** 暂停按钮操作 */
function handlePause(row) {
  const jobIds = row?.jobId ? [row.jobId] : ids.value
  proxy.$modal.confirm('是否确认暂停选中的招聘信息？').then(function () {
    return pauseJobPosting(jobIds[0])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("暂停成功")
  }).catch(() => {})
}

/** 关闭按钮操作 */
function handleClose(row) {
  const jobIds = row?.jobId ? [row.jobId] : ids.value
  proxy.$modal.confirm('是否确认关闭选中的招聘信息？').then(function () {
    return closeJobPosting(jobIds[0])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("关闭成功")
  }).catch(() => {})
}

/** 完成按钮操作 */
function handleComplete(row) {
  const jobIds = row?.jobId ? [row.jobId] : ids.value
  proxy.$modal.confirm('是否确认完成选中的招聘信息？').then(function () {
    return completeJobPosting(jobIds[0])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("完成成功")
  }).catch(() => {})
}

/** 删除按钮操作 */
function handleDelete(row) {
  const jobIds = row?.jobId ? [row.jobId] : ids.value
  proxy.$modal.confirm('是否确认删除选中的招聘信息？').then(function () {
    return delJobPosting(jobIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('job/posting/export', {
    ...queryParams.value
  }, `job_posting_${new Date().getTime()}.xlsx`)
}



// 工作类型标签类型
function getJobTypeTagType(jobType) {
  const typeMap = {
    '全职': 'success',
    '兼职': 'info',
    '临时工': 'warning',
    '小时工': 'danger',
    '实习': ''
  }
  return typeMap[jobType] || ''
}

// 工作类别标签类型
function getCategoryTagType(category) {
  const typeMap = {
    '服务员': 'success',
    '保洁': 'info',
    '搬运工': 'warning',
    '销售': 'danger',
    '客服': '',
    '配送员': 'success',
    '厨师': 'info',
    '司机': 'warning',
    '保安': 'danger',
    '其他': ''
  }
  return typeMap[category] || ''
}

// 紧急程度标签类型
function getUrgencyTagType(urgency) {
  const typeMap = {
    'urgent': 'danger',
    'high': 'warning',
    'normal': 'info',
    'low': ''
  }
  return typeMap[urgency] || 'info'
}

// 紧急程度文本
function getUrgencyText(urgency) {
  const textMap = {
    'urgent': '紧急',
    'high': '高',
    'normal': '普通',
    'low': '低'
  }
  return textMap[urgency] || '普通'
}

// 状态标签类型
function getStatusTagType(status) {
  const typeMap = {
    'draft': 'info',
    'published': 'success',
    'paused': 'warning',
    'closed': 'danger',
    'completed': ''
  }
  return typeMap[status] || 'info'
}

// 状态文本
function getStatusText(status) {
  const textMap = {
    'draft': '草稿',
    'published': '已发布',
    'paused': '已暂停',
    'closed': '已关闭',
    'completed': '已完成'
  }
  return textMap[status] || '未知'
}

// 薪资类型文本
function getSalaryTypeText(salaryType) {
  const textMap = {
    'hourly': '元/小时',
    'daily': '元/天',
    'monthly': '元/月',
    'piece': '元/件'
  }
  return textMap[salaryType] || '元'
}
</script>

<style lang="scss" scoped></style>