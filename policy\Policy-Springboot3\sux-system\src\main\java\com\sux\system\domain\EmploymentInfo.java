package com.sux.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;


/**
 * 用工信息对象 employment_info
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@TableName("employment_info")
public class EmploymentInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用工信息ID */
    @TableId(type = IdType.AUTO)
    private Long employmentId;

    /** 用工标题 */
    @Excel(name = "用工标题")
    @NotBlank(message = "用工标题不能为空")
    @Size(min = 2, max = 200, message = "用工标题长度必须介于 2 和 200 之间")
    private String title;

    /** 用工类型（日结/周结/月结/计件） */
    @Excel(name = "用工类型")
    @NotBlank(message = "用工类型不能为空")
    @Size(min = 0, max = 50, message = "用工类型不能超过50个字符")
    private String employmentType;

    /** 工作类别（服务员/保洁/搬运工/销售/厨师助手/快递员/保安等） */
    @Excel(name = "工作类别")
    @NotBlank(message = "工作类别不能为空")
    @Size(min = 0, max = 100, message = "工作类别不能超过100个字符")
    private String workCategory;

    /** 工作地点 */
    @Excel(name = "工作地点")
    @Size(min = 0, max = 200, message = "工作地点不能超过200个字符")
    private String workLocation;

    /** 区域代码 */
    @Excel(name = "区域代码")
    @Size(min = 0, max = 20, message = "区域代码不能超过20个字符")
    private String regionCode;

    /** 区域名称 */
    @Excel(name = "区域名称")
    @Size(min = 0, max = 100, message = "区域名称不能超过100个字符")
    private String regionName;

    /** 薪资类型（hourly/daily/monthly/piece） */
    @Excel(name = "薪资类型")
    @NotBlank(message = "薪资类型不能为空")
    @Size(min = 0, max = 20, message = "薪资类型不能超过20个字符")
    private String salaryType;

    /** 最低薪资 */
    @Excel(name = "最低薪资")
    private BigDecimal salaryMin;

    /** 最高薪资 */
    @Excel(name = "最高薪资")
    private BigDecimal salaryMax;

    /** 每日工作小时数 */
    @Excel(name = "每日工作小时数")
    @Min(value = 1, message = "每日工作小时数不能小于1小时")
    @Max(value = 24, message = "每日工作小时数不能大于24小时")
    private Integer workHoursPerDay;

    /** 每周工作天数 */
    @Excel(name = "每周工作天数")
    @Min(value = 1, message = "每周工作天数不能小于1天")
    @Max(value = 7, message = "每周工作天数不能大于7天")
    private Integer workDaysPerWeek;

    /** 开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 需要人数 */
    @Excel(name = "需要人数")
    @Min(value = 1, message = "需要人数不能小于1人")
    private Integer positionsNeeded;

    /** 已招聘人数 */
    @Excel(name = "已招聘人数")
    private Integer positionsFilled;

    /** 学历要求 */
    @Excel(name = "学历要求")
    @Size(min = 0, max = 50, message = "学历要求不能超过50个字符")
    private String educationRequired;

    /** 经验要求 */
    @Excel(name = "经验要求")
    @Size(min = 0, max = 100, message = "经验要求不能超过100个字符")
    private String experienceRequired;

    /** 最小年龄要求 */
    @Excel(name = "最小年龄要求")
    @Min(value = 16, message = "最小年龄不能小于16岁")
    @Max(value = 80, message = "最小年龄不能大于80岁")
    private Integer ageMin;

    /** 最大年龄要求 */
    @Excel(name = "最大年龄要求")
    @Min(value = 16, message = "最大年龄不能小于16岁")
    @Max(value = 80, message = "最大年龄不能大于80岁")
    private Integer ageMax;

    /** 性别要求（male/female/unlimited） */
    @Excel(name = "性别要求")
    @Size(min = 0, max = 10, message = "性别要求不能超过10个字符")
    private String genderRequired;

    /** 技能要求（JSON格式存储） */
    @Excel(name = "技能要求")
    private String skillsRequired;

    /** 工作描述 */
    @Excel(name = "工作描述")
    private String workDescription;

    /** 福利待遇 */
    @Excel(name = "福利待遇")
    private String welfareBenefits;

    /** 联系人 */
    @Excel(name = "联系人")
    @Size(min = 0, max = 100, message = "联系人不能超过100个字符")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @Size(min = 0, max = 20, message = "联系电话不能超过20个字符")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    @Size(min = 0, max = 100, message = "联系邮箱不能超过100个字符")
    private String contactEmail;

    /** 公司名称 */
    @Excel(name = "公司名称")
    @Size(min = 0, max = 200, message = "公司名称不能超过200个字符")
    private String companyName;

    /** 公司地址 */
    @Excel(name = "公司地址")
    @Size(min = 0, max = 500, message = "公司地址不能超过500个字符")
    private String companyAddress;

    /** 公司描述 */
    @Excel(name = "公司描述")
    private String companyDescription;

    /** 紧急程度（urgent/high/normal/low） */
    @Excel(name = "紧急程度")
    @Size(min = 0, max = 20, message = "紧急程度不能超过20个字符")
    private String urgencyLevel;

    /** 申请截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applicationDeadline;

    /** 状态（draft/published/paused/closed/completed） */
    @Excel(name = "状态", readConverterExp = "draft=草稿,published=已发布,paused=已暂停,closed=已关闭,completed=已完成")
    @Size(min = 0, max = 20, message = "状态不能超过20个字符")
    private String status;

    /** 是否已验证（0否 1是） */
    @Excel(name = "是否已验证", readConverterExp = "0=否,1=是")
    private Integer isVerified;

    /** 是否推荐（0否 1是） */
    @Excel(name = "是否推荐", readConverterExp = "0=否,1=是")
    private Integer isFeatured;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 申请次数 */
    @Excel(name = "申请次数")
    private Integer applicationCount;

    /** 发布者用户ID */
    @Excel(name = "发布者用户ID")
    private Long publisherUserId;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;
}
