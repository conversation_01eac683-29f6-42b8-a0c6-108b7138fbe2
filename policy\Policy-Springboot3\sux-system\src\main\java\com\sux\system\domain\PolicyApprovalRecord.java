package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.Date;

/**
 * 政策审批记录对象 policy_approval_record
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@TableName("policy_approval_record")
public class PolicyApprovalRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 审批记录ID */
    @TableId(type = IdType.AUTO)
    private Long recordId;

    /** 申请ID */
    @Excel(name = "申请ID")
    @NotNull(message = "申请ID不能为空")
    private Long applicationId;

    /** 审批层级（1初审 2终审） */
    @Excel(name = "审批层级", readConverterExp = "1=初审,2=终审")
    @NotNull(message = "审批层级不能为空")
    private Integer approvalLevel;

    /** 审批状态（0待审批 1审批通过 2审批拒绝） */
    @Excel(name = "审批状态", readConverterExp = "0=待审批,1=审批通过,2=审批拒绝")
    private String approvalStatus;

    /** 审批人用户ID */
    @Excel(name = "审批人用户ID")
    private Long approverUserId;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /** 审批意见 */
    @Excel(name = "审批意见")
    @Size(min = 0, max = 1000, message = "审批意见不能超过1000个字符")
    private String approvalComment;

    /** 审批相关文件JSON数据 */
    @Excel(name = "审批相关文件")
    private String approvalFiles;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField("del_flag")
    private String delFlag;

    // 关联查询字段
    /** 审批人用户名 */
    @TableField(exist = false)
    private String approverUserName;

    /** 审批人昵称 */
    @TableField(exist = false)
    private String approverNickName;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }

    public void setApplicationId(Long applicationId) 
    {
        this.applicationId = applicationId;
    }

    public Long getApplicationId() 
    {
        return applicationId;
    }

    public void setApprovalLevel(Integer approvalLevel) 
    {
        this.approvalLevel = approvalLevel;
    }

    public Integer getApprovalLevel() 
    {
        return approvalLevel;
    }

    public void setApprovalStatus(String approvalStatus) 
    {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatus() 
    {
        return approvalStatus;
    }

    public void setApproverUserId(Long approverUserId) 
    {
        this.approverUserId = approverUserId;
    }

    public Long getApproverUserId() 
    {
        return approverUserId;
    }

    public void setApprovalTime(Date approvalTime) 
    {
        this.approvalTime = approvalTime;
    }

    public Date getApprovalTime() 
    {
        return approvalTime;
    }

    public void setApprovalComment(String approvalComment) 
    {
        this.approvalComment = approvalComment;
    }

    public String getApprovalComment() 
    {
        return approvalComment;
    }

    public void setApprovalFiles(String approvalFiles) 
    {
        this.approvalFiles = approvalFiles;
    }

    public String getApprovalFiles() 
    {
        return approvalFiles;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public String getApproverUserName() {
        return approverUserName;
    }

    public void setApproverUserName(String approverUserName) {
        this.approverUserName = approverUserName;
    }

    public String getApproverNickName() {
        return approverNickName;
    }

    public void setApproverNickName(String approverNickName) {
        this.approverNickName = approverNickName;
    }

    @Override
    public String toString() {
        return "PolicyApprovalRecord{" +
                "recordId=" + recordId +
                ", applicationId=" + applicationId +
                ", approvalLevel=" + approvalLevel +
                ", approvalStatus='" + approvalStatus + '\'' +
                ", approverUserId=" + approverUserId +
                ", approvalTime=" + approvalTime +
                ", approvalComment='" + approvalComment + '\'' +
                ", approvalFiles='" + approvalFiles + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", approverUserName='" + approverUserName + '\'' +
                ", approverNickName='" + approverNickName + '\'' +
                '}';
    }
}
