import request from '@/utils/request'

// 查询培训订单列表
export function listTrainingOrder(query) {
  return request({
    url: '/training/order/list',
    method: 'get',
    params: query
  })
}

// 查询培训订单详细
export function getTrainingOrder(orderId) {
  return request({
    url: '/training/order/' + orderId,
    method: 'get'
  })
}

// 新增培训订单
export function addTrainingOrder(data) {
  return request({
    url: '/training/order',
    method: 'post',
    data: data
  })
}

// 修改培训订单
export function updateTrainingOrder(data) {
  return request({
    url: '/training/order',
    method: 'put',
    data: data
  })
}

// 删除培训订单
export function delTrainingOrder(orderId) {
  return request({
    url: '/training/order/' + orderId,
    method: 'delete'
  })
}

// 发布培训订单
export function publishTrainingOrder(orderId) {
  return request({
    url: '/training/order/publish/' + orderId,
    method: 'put'
  })
}

// 取消培训订单
export function cancelTrainingOrder(orderId) {
  return request({
    url: '/training/order/cancel/' + orderId,
    method: 'put'
  })
}

// 获取即将开始的培训订单
export function getUpcomingTrainingOrders(days) {
  return request({
    url: '/training/order/upcoming/' + days,
    method: 'get'
  })
}

// 获取已过期的培训订单
export function getExpiredTrainingOrders() {
  return request({
    url: '/training/order/expired',
    method: 'get'
  })
}

// 获取订单状态统计
export function getOrderStatistics() {
  return request({
    url: '/training/order/statistics',
    method: 'get'
  })
}

// 自动更新订单状态
export function autoUpdateOrderStatus() {
  return request({
    url: '/training/order/auto-update-status',
    method: 'put'
  })
}

// 导出培训订单
export function exportTrainingOrder(query) {
  return request({
    url: '/training/order/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}
