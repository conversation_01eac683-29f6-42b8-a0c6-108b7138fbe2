# 政策申请系统修改总结

## 需求描述
1. 将 `policyS/index.vue` 改成和 `policy/index.vue` 一样的表格样式
2. 将"企业在银行开立的基本账户"改为企业表单信息填写，包括对公户等信息
3. 将企业信息表单与上面的申请人表单合并显示
4. **统一社会信用代码独占一行显示**
5. **移除所有表单验证的正则表达式**

## 修改内容

### 1. 数据库修改

#### 1.1 新增SQL文件
- **文件**: `policy/Policy-Springboot3/doc/sql/add_company_fields_to_policy_application.sql`
- **内容**: 为 `policy_application` 表添加企业信息字段的ALTER语句

#### 1.2 修改表结构
在 `policy_application` 表中新增以下字段：
- `company_name` varchar(200) - 企业名称
- `company_code` varchar(50) - 企业统一社会信用代码
- `company_legal_person` varchar(100) - 企业法定代表人
- `company_address` varchar(500) - 企业注册地址
- `company_contact_person` varchar(100) - 企业联系人
- `company_contact_phone` varchar(20) - 企业联系电话
- `bank_name` varchar(200) - 开户银行名称
- `bank_account_name` varchar(200) - 银行账户名称
- `bank_account_number` varchar(50) - 银行账号

### 2. 后端修改

#### 2.1 实体类修改
- **文件**: `policy/Policy-Springboot3/sux-system/src/main/java/com/sux/system/domain/PolicyApplication.java`
- **修改内容**:
  - 添加企业信息相关字段
  - 添加对应的getter/setter方法
  - 更新toString方法
  - 添加字段验证注解

#### 2.2 MyBatis映射文件修改
- **文件**: `policy/Policy-Springboot3/sux-system/src/main/resources/mapper/policy/PolicyApplicationMapper.xml`
- **修改内容**:
  - 更新 `selectPolicyApplicationVo` SQL片段，包含新字段
  - 更新 `insertPolicyApplication` 插入语句
  - 更新 `updatePolicyApplication` 更新语句

### 3. 前端修改

#### 3.1 页面结构改造
- **文件**: `policy/Policy-Vue3/src/views/policy/policyS/index.vue`
- **修改内容**:
  - 将卡片式布局改为表格布局，使用 `TableList` 组件
  - 配置表格列显示政策信息和申请状态
  - 添加搜索功能和分页功能

#### 3.2 申请表单改造
- **表单布局**: 使用分组布局，分为申请人信息、企业基本信息、银行对公户信息三个部分
- **字段添加**: 
  - 企业名称、统一社会信用代码、法定代表人
  - 企业注册地址、企业联系人、企业联系电话
  - 开户银行、账户名称、银行账号
- **表单验证**: 添加所有新字段的验证规则，包括格式验证

#### 3.3 所需材料调整
- 移除"企业在银行开立的基本账户"材料项
- 保留其他必要材料项

### 4. 功能特性

#### 4.1 表格功能
- 支持按政策名称、政策类型搜索
- 支持分页显示
- 显示政策状态、申请状态等信息
- 提供申请、查看申请、查看审核状态等操作

#### 4.2 表单功能
- 响应式布局，支持移动端
- 完整的表单验证
- 企业信息与申请人信息分组显示
- 银行对公户信息独立填写

#### 4.3 数据处理
- 后端自动设置申请人用户ID
- 支持企业信息的完整存储和查询
- 保持与原有审核流程的兼容性

## 使用说明

### 1. 数据库升级
执行以下SQL文件来升级数据库结构：
```sql
-- 执行企业信息字段添加脚本
source policy/Policy-Springboot3/doc/sql/add_company_fields_to_policy_application.sql
```

### 2. 前端页面访问
- 访问政策申请页面，现在显示为表格形式
- 点击"立即申请"按钮，弹出包含企业信息的申请表单
- 填写完整的申请人信息、企业信息和银行信息后提交

### 3. 数据查看
- 管理员可以在后台查看包含企业信息的完整申请数据
- 申请人可以查看自己的申请记录和状态

## 技术要点

1. **数据库兼容性**: 新增字段均为可空字段，不影响现有数据
2. **前端组件复用**: 使用现有的 `TableList` 组件，保持界面一致性
3. **表单验证**: 统一社会信用代码使用正则表达式验证格式
4. **响应式设计**: 表单支持不同屏幕尺寸的自适应显示
5. **API兼容**: 保持现有API接口不变，只是扩展数据字段

## 测试建议

1. 测试数据库字段添加是否成功
2. 测试新申请表单的提交和验证
3. 测试表格显示和搜索功能
4. 测试移动端响应式布局
5. 测试与现有审核流程的兼容性
