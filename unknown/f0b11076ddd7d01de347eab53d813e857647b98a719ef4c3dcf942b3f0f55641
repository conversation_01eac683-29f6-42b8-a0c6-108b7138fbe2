// 零工市场管理表格配置
export function createMarketTableOption() {
    return Promise.resolve({
        // 表格列配置 - 使用 column 而不是 columns
        column: [
            {
                prop: 'marketId',
                label: '市场ID',
                width: 80,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'marketName',
                label: '市场名称',
                minWidth: 150,
                align: 'center',
                showOverflowTooltip: true,
                showColumn: true,
                search: true,
                type: 'input',
                placeholder: '请输入市场名称'
            },
            {
                prop: 'marketCode',
                label: '市场编码',
                width: 120,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'marketType',
                label: '市场类型',
                width: 100,
                align: 'center',
                slot: 'marketType',
                showColumn: true,
                search: true,
                type: 'select',
                placeholder: '请选择市场类型',
                dicData: [
                    { label: '综合市场', value: '综合市场' },
                    { label: '专业市场', value: '专业市场' },
                    { label: '临时市场', value: '临时市场' }
                ]
            },
            {
                prop: 'regionName',
                label: '区域',
                width: 100,
                align: 'center',
                showColumn: true,
                search: true,
                type: 'input',
                placeholder: '请输入区域名称'
            },
            {
                prop: 'contactPerson',
                label: '联系人',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'contactPhone',
                label: '联系电话',
                width: 120,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'workerCapacity',
                label: '零工容纳量',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'currentWorkerCount',
                label: '当前零工数',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'dailyAvgDemand',
                label: '日均需求',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'managementFee',
                label: '管理费用',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'isFeatured',
                label: '是否推荐',
                width: 100,
                align: 'center',
                slot: 'isFeatured',
                showColumn: true,
                search: true,
                type: 'select',
                placeholder: '请选择推荐状态',
                dicData: [
                    { label: '推荐', value: 1 },
                    { label: '不推荐', value: 0 }
                ]
            },
            {
                prop: 'status',
                label: '状态',
                width: 80,
                align: 'center',
                slot: 'status',
                showColumn: true,
                search: true,
                type: 'select',
                placeholder: '请选择状态',
                dicData: [
                    { label: '正常', value: '0' },
                    { label: '停用', value: '1' }
                ]
            },
            {
                prop: 'viewCount',
                label: '浏览次数',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'createTime',
                label: '创建时间',
                width: 180,
                align: 'center',
                showColumn: true,
                formatter: (row) => {
                    return row.createTime ? row.createTime.substring(0, 10) : ''
                }
            }
        ],
        // 默认数据
        defaultData: [
            {
                marketId: 1,
                marketName: '中心城区综合零工市场',
                marketCode: 'MKT001',
                marketType: '综合市场',
                address: '北京市朝阳区建国路88号',
                regionCode: '110105',
                regionName: '朝阳区',
                contactPerson: '张经理',
                contactPhone: '13800138001',
                contactEmail: '<EMAIL>',
                operatingHours: '06:00-18:00',
                workerCapacity: 500,
                currentWorkerCount: 320,
                dailyAvgDemand: 280,
                peakDemandTime: '08:00-10:00',
                managementFee: '50元/月',
                serviceFeeRate: '5%',
                safetyMeasures: '安全帽、保险、培训',
                description: '位于市中心的大型综合零工市场，提供多种工种服务',
                status: '0',
                isFeatured: 1,
                viewCount: 1250,
                createTime: '2024-01-15 09:30:00',
                remark: '重点推荐市场'
            },
            {
                marketId: 2,
                marketName: '建筑专业零工市场',
                marketCode: 'MKT002',
                marketType: '专业市场',
                address: '北京市海淀区中关村大街100号',
                regionCode: '110108',
                regionName: '海淀区',
                contactPerson: '李主任',
                contactPhone: '13800138002',
                contactEmail: '<EMAIL>',
                operatingHours: '05:30-17:30',
                workerCapacity: 300,
                currentWorkerCount: 180,
                dailyAvgDemand: 150,
                peakDemandTime: '07:00-09:00',
                managementFee: '60元/月',
                serviceFeeRate: '6%',
                safetyMeasures: '专业培训、工具检查、安全监督',
                description: '专门服务建筑行业的专业零工市场',
                status: '0',
                isFeatured: 1,
                viewCount: 890,
                createTime: '2024-02-20 14:15:00',
                remark: '建筑行业首选'
            },
            {
                marketId: 3,
                marketName: '西城区临时零工市场',
                marketCode: 'MKT003',
                marketType: '临时市场',
                address: '北京市西城区西单北大街50号',
                regionCode: '110102',
                regionName: '西城区',
                contactPerson: '王协调员',
                contactPhone: '13800138003',
                contactEmail: '<EMAIL>',
                operatingHours: '07:00-16:00',
                workerCapacity: 150,
                currentWorkerCount: 95,
                dailyAvgDemand: 80,
                peakDemandTime: '09:00-11:00',
                managementFee: '30元/月',
                serviceFeeRate: '3%',
                safetyMeasures: '基础培训、身份验证',
                description: '临时性零工服务市场，灵活就业',
                status: '0',
                isFeatured: 0,
                viewCount: 456,
                createTime: '2024-03-10 11:20:00',
                remark: '临时性质'
            },
            {
                marketId: 4,
                marketName: '东城区家政服务市场',
                marketCode: 'MKT004',
                marketType: '专业市场',
                address: '北京市东城区王府井大街200号',
                regionCode: '110101',
                regionName: '东城区',
                contactPerson: '赵经理',
                contactPhone: '13800138004',
                contactEmail: '<EMAIL>',
                operatingHours: '08:00-17:00',
                workerCapacity: 200,
                currentWorkerCount: 160,
                dailyAvgDemand: 120,
                peakDemandTime: '10:00-12:00',
                managementFee: '40元/月',
                serviceFeeRate: '4%',
                safetyMeasures: '健康证检查、技能培训、背景调查',
                description: '专业家政服务人员聚集地',
                status: '0',
                isFeatured: 1,
                viewCount: 678,
                createTime: '2024-01-25 16:45:00',
                remark: '家政服务专业'
            },
            {
                marketId: 5,
                marketName: '丰台区物流零工市场',
                marketCode: 'MKT005',
                marketType: '综合市场',
                address: '北京市丰台区南三环西路88号',
                regionCode: '110106',
                regionName: '丰台区',
                contactPerson: '刘站长',
                contactPhone: '13800138005',
                contactEmail: '<EMAIL>',
                operatingHours: '24小时',
                workerCapacity: 400,
                currentWorkerCount: 280,
                dailyAvgDemand: 250,
                peakDemandTime: '全天候',
                managementFee: '55元/月',
                serviceFeeRate: '5.5%',
                safetyMeasures: '驾驶证验证、车辆检查、GPS监控',
                description: '24小时物流配送零工服务',
                status: '1',
                isFeatured: 0,
                viewCount: 234,
                createTime: '2024-02-05 08:30:00',
                remark: '暂停服务维护中'
            }
        ]
    })
}
