package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训机构申请对象 training_institution_application
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@TableName("training_institution_application")
public class TrainingInstitutionApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    @TableId(type = IdType.AUTO)
    private Long applicationId;

    /** 培训订单ID */
    @Excel(name = "培训订单ID")
    @NotNull(message = "培训订单ID不能为空")
    private Long orderId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 机构名称 */
    @Excel(name = "机构名称")
    @NotBlank(message = "机构名称不能为空")
    @Size(min = 0, max = 200, message = "机构名称不能超过200个字符")
    private String institutionName;

    /** 机构代码/统一社会信用代码 */
    @Excel(name = "机构代码")
    @Size(min = 0, max = 50, message = "机构代码不能超过50个字符")
    private String institutionCode;

    /** 法定代表人 */
    @Excel(name = "法定代表人")
    @NotBlank(message = "法定代表人不能为空")
    @Size(min = 0, max = 50, message = "法定代表人不能超过50个字符")
    private String legalPerson;

    /** 联系人 */
    @Excel(name = "联系人")
    @NotBlank(message = "联系人不能为空")
    @Size(min = 0, max = 50, message = "联系人不能超过50个字符")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @NotBlank(message = "联系电话不能为空")
    @Size(min = 0, max = 20, message = "联系电话不能超过20个字符")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    @Size(min = 0, max = 100, message = "联系邮箱不能超过100个字符")
    private String contactEmail;

    /** 机构地址 */
    @Excel(name = "机构地址")
    @NotBlank(message = "机构地址不能为空")
    @Size(min = 0, max = 500, message = "机构地址不能超过500个字符")
    private String institutionAddress;

    /** 机构类型 */
    @Excel(name = "机构类型")
    @Size(min = 0, max = 50, message = "机构类型不能超过50个字符")
    private String institutionType;

    /** 成立时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "成立时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date establishedDate;

    /** 注册资本（万元） */
    @Excel(name = "注册资本")
    private BigDecimal registeredCapital;

    /** 经营范围 */
    @Excel(name = "经营范围")
    private String businessScope;

    /** 培训经验描述 */
    @Excel(name = "培训经验")
    private String trainingExperience;

    /** 培训能力描述 */
    @Excel(name = "培训能力")
    private String trainingCapacity;

    /** 培训计划 */
    @Excel(name = "培训计划")
    private String trainingPlan;

    /** 师资信息 */
    @Excel(name = "师资信息")
    private String teacherInfo;

    /** 设施设备信息 */
    @Excel(name = "设施设备信息")
    private String facilityInfo;

    /** 资质文件路径（JSON格式存储多个文件） */
    @Excel(name = "资质文件")
    private String qualificationFiles;

    /** 培训计划文件路径 */
    @Excel(name = "培训计划文件")
    @Size(min = 0, max = 500, message = "培训计划文件路径不能超过500个字符")
    private String trainingPlanFile;

    /** 师资证明文件路径（JSON格式存储多个文件） */
    @Excel(name = "师资证明文件")
    private String teacherCertFiles;

    /** 设施设备文件路径（JSON格式存储多个文件） */
    @Excel(name = "设施设备文件")
    private String facilityFiles;

    /** 其他材料文件路径（JSON格式存储多个文件） */
    @Excel(name = "其他材料文件")
    private String otherFiles;

    /** 申请状态（0待审核 1已通过 2已拒绝 3已取消） */
    @Excel(name = "申请状态", readConverterExp = "0=待审核,1=已通过,2=已拒绝,3=已取消")
    private String applicationStatus;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applicationTime;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 审核人 */
    @Excel(name = "审核人")
    @Size(min = 0, max = 50, message = "审核人不能超过50个字符")
    private String reviewer;

    /** 审核意见 */
    @Excel(name = "审核意见")
    @Size(min = 0, max = 1000, message = "审核意见不能超过1000个字符")
    private String reviewComment;

    /** 申请备注 */
    @Excel(name = "申请备注")
    @Size(min = 0, max = 1000, message = "申请备注不能超过1000个字符")
    private String applicationNote;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField("del_flag")
    private String delFlag;

    // 关联字段 - 培训订单信息
    @TableField(exist = false)
    private String orderTitle;

    @TableField(exist = false)
    private String trainingType;

    @TableField(exist = false)
    private Date startDate;

    @TableField(exist = false)
    private Date endDate;

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getInstitutionName() {
        return institutionName;
    }

    public void setInstitutionName(String institutionName) {
        this.institutionName = institutionName;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getInstitutionAddress() {
        return institutionAddress;
    }

    public void setInstitutionAddress(String institutionAddress) {
        this.institutionAddress = institutionAddress;
    }

    public String getInstitutionType() {
        return institutionType;
    }

    public void setInstitutionType(String institutionType) {
        this.institutionType = institutionType;
    }

    public Date getEstablishedDate() {
        return establishedDate;
    }

    public void setEstablishedDate(Date establishedDate) {
        this.establishedDate = establishedDate;
    }

    public BigDecimal getRegisteredCapital() {
        return registeredCapital;
    }

    public void setRegisteredCapital(BigDecimal registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getTrainingExperience() {
        return trainingExperience;
    }

    public void setTrainingExperience(String trainingExperience) {
        this.trainingExperience = trainingExperience;
    }

    public String getTrainingCapacity() {
        return trainingCapacity;
    }

    public void setTrainingCapacity(String trainingCapacity) {
        this.trainingCapacity = trainingCapacity;
    }

    public String getTrainingPlan() {
        return trainingPlan;
    }

    public void setTrainingPlan(String trainingPlan) {
        this.trainingPlan = trainingPlan;
    }

    public String getTeacherInfo() {
        return teacherInfo;
    }

    public void setTeacherInfo(String teacherInfo) {
        this.teacherInfo = teacherInfo;
    }

    public String getFacilityInfo() {
        return facilityInfo;
    }

    public void setFacilityInfo(String facilityInfo) {
        this.facilityInfo = facilityInfo;
    }

    public String getQualificationFiles() {
        return qualificationFiles;
    }

    public void setQualificationFiles(String qualificationFiles) {
        this.qualificationFiles = qualificationFiles;
    }

    public String getTrainingPlanFile() {
        return trainingPlanFile;
    }

    public void setTrainingPlanFile(String trainingPlanFile) {
        this.trainingPlanFile = trainingPlanFile;
    }

    public String getTeacherCertFiles() {
        return teacherCertFiles;
    }

    public void setTeacherCertFiles(String teacherCertFiles) {
        this.teacherCertFiles = teacherCertFiles;
    }

    public String getFacilityFiles() {
        return facilityFiles;
    }

    public void setFacilityFiles(String facilityFiles) {
        this.facilityFiles = facilityFiles;
    }

    public String getOtherFiles() {
        return otherFiles;
    }

    public void setOtherFiles(String otherFiles) {
        this.otherFiles = otherFiles;
    }

    public String getApplicationStatus() {
        return applicationStatus;
    }

    public void setApplicationStatus(String applicationStatus) {
        this.applicationStatus = applicationStatus;
    }

    public Date getApplicationTime() {
        return applicationTime;
    }

    public void setApplicationTime(Date applicationTime) {
        this.applicationTime = applicationTime;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public String getReviewComment() {
        return reviewComment;
    }

    public void setReviewComment(String reviewComment) {
        this.reviewComment = reviewComment;
    }

    public String getApplicationNote() {
        return applicationNote;
    }

    public void setApplicationNote(String applicationNote) {
        this.applicationNote = applicationNote;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getOrderTitle() {
        return orderTitle;
    }

    public void setOrderTitle(String orderTitle) {
        this.orderTitle = orderTitle;
    }

    public String getTrainingType() {
        return trainingType;
    }

    public void setTrainingType(String trainingType) {
        this.trainingType = trainingType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return "TrainingInstitutionApplication{" +
                "applicationId=" + applicationId +
                ", orderId=" + orderId +
                ", userId=" + userId +
                ", institutionName='" + institutionName + '\'' +
                ", institutionCode='" + institutionCode + '\'' +
                ", legalPerson='" + legalPerson + '\'' +
                ", contactPerson='" + contactPerson + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", institutionAddress='" + institutionAddress + '\'' +
                ", institutionType='" + institutionType + '\'' +
                ", establishedDate=" + establishedDate +
                ", registeredCapital=" + registeredCapital +
                ", businessScope='" + businessScope + '\'' +
                ", trainingExperience='" + trainingExperience + '\'' +
                ", trainingCapacity='" + trainingCapacity + '\'' +
                ", trainingPlan='" + trainingPlan + '\'' +
                ", teacherInfo='" + teacherInfo + '\'' +
                ", facilityInfo='" + facilityInfo + '\'' +
                ", qualificationFiles='" + qualificationFiles + '\'' +
                ", trainingPlanFile='" + trainingPlanFile + '\'' +
                ", teacherCertFiles='" + teacherCertFiles + '\'' +
                ", facilityFiles='" + facilityFiles + '\'' +
                ", otherFiles='" + otherFiles + '\'' +
                ", applicationStatus='" + applicationStatus + '\'' +
                ", applicationTime=" + applicationTime +
                ", reviewTime=" + reviewTime +
                ", reviewer='" + reviewer + '\'' +
                ", reviewComment='" + reviewComment + '\'' +
                ", applicationNote='" + applicationNote + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", orderTitle='" + orderTitle + '\'' +
                ", trainingType='" + trainingType + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                '}';
    }
}
