-- 培训报名表
DROP TABLE IF EXISTS `training_application`;
CREATE TABLE `training_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `order_id` bigint(20) NOT NULL COMMENT '培训订单ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `applicant_name` varchar(50) NOT NULL COMMENT '报名人姓名',
  `applicant_phone` varchar(20) NOT NULL COMMENT '报名人手机号',
  `applicant_email` varchar(100) DEFAULT NULL COMMENT '报名人邮箱',
  `applicant_id_card` varchar(18) DEFAULT NULL COMMENT '报名人身份证号',
  `applicant_gender` varchar(10) DEFAULT NULL COMMENT '报名人性别',
  `applicant_age` int(3) DEFAULT NULL COMMENT '报名人年龄',
  `applicant_education` varchar(20) DEFAULT NULL COMMENT '报名人学历',
  `applicant_experience` text COMMENT '报名人工作经验',
  `applicant_address` varchar(200) DEFAULT NULL COMMENT '报名人地址',
  `application_status` char(1) DEFAULT '0' COMMENT '报名状态（0待审核 1已通过 2已拒绝 3已取消）',
  `application_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '报名时间',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reviewer` varchar(50) DEFAULT NULL COMMENT '审核人',
  `review_comment` varchar(500) DEFAULT NULL COMMENT '审核意见',
  `application_note` varchar(500) DEFAULT NULL COMMENT '报名备注',
  `create_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`application_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_applicant_phone` (`applicant_phone`),
  KEY `idx_application_status` (`application_status`),
  KEY `idx_application_time` (`application_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训报名表';

-- 插入测试数据
INSERT INTO `training_application` VALUES
(1, 1, 1, '张三', '13800138001', '<EMAIL>', '110101199001011234', '男', 25, '本科', '有3年Java开发经验，熟悉Spring框架', '北京市朝阳区', '1', '2025-07-20 10:00:00', '2025-07-20 14:00:00', '李老师', '符合报名条件，通过审核', '希望能参加此次培训', 1, '2025-07-20 10:00:00', 1, '2025-07-20 14:00:00', NULL, '0'),
(2, 1, 2, '李四', '13800138002', '<EMAIL>', '110101199002021234', '女', 28, '硕士', '有5年软件开发经验，精通多种编程语言', '北京市海淀区', '0', '2025-07-21 09:30:00', NULL, NULL, NULL, '对Java高级开发很感兴趣', 1, '2025-07-21 09:30:00', 1, '2025-07-21 09:30:00', NULL, '0'),
(3, 2, 3, '王五', '13800138003', '<EMAIL>', '110101199003031234', '男', 30, '大专', '有2年数据分析经验，熟悉Excel和SQL', '北京市西城区', '1', '2025-07-21 11:00:00', '2025-07-21 15:30:00', '张老师', '基础扎实，适合参加培训', '想转行做数据分析师', 1, '2025-07-21 11:00:00', 1, '2025-07-21 15:30:00', NULL, '0');

-- 培训机构申请表
DROP TABLE IF EXISTS `training_institution_application`;
CREATE TABLE `training_institution_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `order_id` bigint(20) NOT NULL COMMENT '培训订单ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `institution_name` varchar(200) NOT NULL COMMENT '机构名称',
  `institution_code` varchar(50) DEFAULT NULL COMMENT '机构代码/统一社会信用代码',
  `legal_person` varchar(50) NOT NULL COMMENT '法定代表人',
  `contact_person` varchar(50) NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `institution_address` varchar(500) NOT NULL COMMENT '机构地址',
  `institution_type` varchar(50) DEFAULT NULL COMMENT '机构类型（企业/事业单位/社会组织等）',
  `established_date` date DEFAULT NULL COMMENT '成立时间',
  `registered_capital` decimal(15,2) DEFAULT NULL COMMENT '注册资本（万元）',
  `business_scope` text COMMENT '经营范围',
  `training_experience` text COMMENT '培训经验描述',
  `training_capacity` text COMMENT '培训能力描述',
  `training_plan` text COMMENT '培训计划',
  `teacher_info` text COMMENT '师资信息',
  `facility_info` text COMMENT '设施设备信息',
  `qualification_files` text COMMENT '资质文件路径（JSON格式存储多个文件）',
  `training_plan_file` varchar(500) DEFAULT NULL COMMENT '培训计划文件路径',
  `teacher_cert_files` text COMMENT '师资证明文件路径（JSON格式存储多个文件）',
  `facility_files` text COMMENT '设施设备文件路径（JSON格式存储多个文件）',
  `other_files` text COMMENT '其他材料文件路径（JSON格式存储多个文件）',
  `application_status` char(1) DEFAULT '0' COMMENT '申请状态（0待审核 1已通过 2已拒绝 3已取消）',
  `application_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reviewer` varchar(50) DEFAULT NULL COMMENT '审核人',
  `review_comment` varchar(1000) DEFAULT NULL COMMENT '审核意见',
  `application_note` varchar(1000) DEFAULT NULL COMMENT '申请备注',
  `create_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`application_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_institution_name` (`institution_name`),
  KEY `idx_contact_phone` (`contact_phone`),
  KEY `idx_application_status` (`application_status`),
  KEY `idx_application_time` (`application_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训机构申请表';

-- 插入测试数据
INSERT INTO `training_institution_application` VALUES
(1, 1, 1, '北京优秀培训机构', '91110000123456789X', '张总', '李经理', '13800138001', '<EMAIL>', '北京市朝阳区建国路88号', '企业', '2020-01-15', 500.00, '教育培训、技能培训、管理咨询', '拥有5年Java培训经验，累计培训学员超过1000人', '具备完善的Java培训体系，拥有资深讲师团队', '针对Java高级开发的系统性培训计划，包含理论学习和实践项目', '拥有10名资深Java讲师，平均从业经验8年以上', '配备现代化机房50间，每间可容纳30人同时上课', '["qualification1.pdf","qualification2.pdf"]', 'training_plan_java.pdf', '["teacher_cert1.pdf","teacher_cert2.pdf"]', '["facility1.jpg","facility2.jpg"]', '["other1.pdf"]', '1', '2025-07-20 09:00:00', '2025-07-20 16:00:00', '审核员A', '机构资质齐全，培训能力强，通过审核', '希望能承接Java高级开发培训项目', 1, '2025-07-20 09:00:00', 1, '2025-07-20 16:00:00', NULL, '0'),
(2, 2, 2, '上海专业技能培训中心', '91310000987654321A', '王主任', '赵老师', '13800138002', '<EMAIL>', '上海市浦东新区张江高科技园区', '事业单位', '2018-06-01', 1000.00, '职业技能培训、认证考试、企业内训', '专注数据分析培训3年，与多家知名企业合作', '具备数据分析全栈培训能力，从基础到高级应用', '数据分析师培训计划，涵盖统计学、Python、机器学习等', '拥有8名数据分析专家，均具有实际项目经验', '配备高性能计算集群和专业数据分析软件', '["qualification3.pdf","qualification4.pdf"]', 'training_plan_data.pdf', '["teacher_cert3.pdf","teacher_cert4.pdf"]', '["facility3.jpg","facility4.jpg"]', '["other2.pdf"]', '0', '2025-07-21 10:00:00', NULL, NULL, NULL, '申请承接数据分析培训项目', 1, '2025-07-21 10:00:00', 1, '2025-07-21 10:00:00', NULL, '0');
