-- 招聘信息表（核心匹配优化版）
DROP TABLE IF EXISTS `job_posting`;
CREATE TABLE `job_posting` (
  `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '招聘ID',
  `job_title` varchar(200) NOT NULL COMMENT '职位名称',
  `job_description` text COMMENT '职位描述',
  `job_type` varchar(50) NOT NULL COMMENT '工作类型（全职/兼职/临时工/小时工）',
  `job_category` varchar(100) NOT NULL COMMENT '工作类别（服务员/保洁/搬运工/销售/厨师助手/快递员/保安等）',
  `work_location` varchar(200) COMMENT '工作地点（保留但不作为主要匹配条件）',
  `salary_type` varchar(20) NOT NULL COMMENT '薪资类型（hourly/daily/monthly/piece）',
  `salary_min` decimal(10,2) COMMENT '最低薪资',
  `salary_max` decimal(10,2) COMMENT '最高薪资',
  `education_required` varchar(50) COMMENT '学历要求（不限/初中/高中/中专/大专/本科/硕士/博士）',
  `experience_required` varchar(100) COMMENT '经验要求',
  `work_hours_per_day` int(11) COMMENT '每日工作小时数',
  `work_days_per_week` int(11) COMMENT '每周工作天数',
  `start_date` date COMMENT '开始日期',
  `end_date` date COMMENT '结束日期',
  `contact_person` varchar(100) COMMENT '联系人',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `company_name` varchar(200) COMMENT '公司名称',
  `urgency_level` varchar(20) DEFAULT 'normal' COMMENT '紧急程度（urgent/high/normal/low）',
  `positions_available` int(11) DEFAULT 1 COMMENT '招聘人数',
  `positions_filled` int(11) DEFAULT 0 COMMENT '已招聘人数',
  `status` varchar(20) DEFAULT 'draft' COMMENT '状态（draft/published/paused/closed/completed）',
  `view_count` int(11) DEFAULT 0 COMMENT '浏览次数',
  `application_count` int(11) DEFAULT 0 COMMENT '申请次数',
  `publisher_user_id` bigint(20) NOT NULL COMMENT '发布者用户ID',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已验证（0否 1是）',
  `featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`job_id`),
  -- 核心匹配字段索引（优化匹配查询性能）
  KEY `idx_job_type` (`job_type`),
  KEY `idx_job_category` (`job_category`),
  KEY `idx_salary_type` (`salary_type`),
  KEY `idx_education` (`education_required`),
  KEY `idx_salary_range` (`salary_min`, `salary_max`),
  -- 复合索引（提高多条件匹配查询性能）
  KEY `idx_match_core` (`job_type`, `job_category`, `salary_type`, `status`),
  KEY `idx_match_salary` (`salary_type`, `salary_min`, `salary_max`),
  -- 业务索引
  KEY `idx_status` (`status`),
  KEY `idx_publisher` (`publisher_user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='招聘信息表（核心匹配优化版）';

-- 零工信息表（核心匹配优化版）
DROP TABLE IF EXISTS `worker_profile`;
CREATE TABLE `worker_profile` (
  `worker_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '零工ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `real_name` varchar(100) NOT NULL COMMENT '真实姓名',
  `nickname` varchar(100) COMMENT '昵称',
  `gender` varchar(10) COMMENT '性别（male/female）',
  `age` int(11) COMMENT '年龄',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `current_location` varchar(200) COMMENT '当前所在地（保留但不作为主要匹配条件）',
  `education_level` varchar(50) COMMENT '学历水平（不限/初中/高中/中专/大专/本科/硕士/博士）',
  `work_experience_years` int(11) COMMENT '工作经验年数',
  `work_categories` text COMMENT '工作类别偏好（JSON数组：服务员/保洁/搬运工/销售/厨师助手/快递员/保安等）',
  `job_types_preferred` text COMMENT '偏好工作类型（JSON数组：全职/兼职/临时工/小时工）',
  `skills` text COMMENT '技能列表（JSON格式）',
  `salary_expectation_min` decimal(10,2) COMMENT '期望最低薪资',
  `salary_expectation_max` decimal(10,2) COMMENT '期望最高薪资',
  `salary_type_preference` varchar(20) COMMENT '薪资类型偏好（hourly/daily/monthly/piece）',
  `availability_start_date` date COMMENT '可开始工作日期',
  `work_days_per_week` int(11) COMMENT '每周可工作天数',
  `work_hours_per_day` int(11) COMMENT '每日可工作小时数',
  `profile_photo` varchar(500) COMMENT '头像照片URL',
  `self_introduction` text COMMENT '自我介绍',
  `rating_average` decimal(3,2) DEFAULT 0.00 COMMENT '平均评分',
  `rating_count` int(11) DEFAULT 0 COMMENT '评分次数',
  `completed_jobs` int(11) DEFAULT 0 COMMENT '完成工作数量',
  `success_rate` decimal(5,2) DEFAULT 0.00 COMMENT '成功率',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态（active/inactive/suspended/banned）',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已实名验证（0否 1是）',
  `last_active_time` datetime COMMENT '最后活跃时间',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`worker_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  -- 核心匹配字段索引（优化匹配查询性能）
  KEY `idx_education_level` (`education_level`),
  KEY `idx_salary_type` (`salary_type_preference`),
  KEY `idx_salary_expectation` (`salary_expectation_min`, `salary_expectation_max`),
  KEY `idx_work_experience` (`work_experience_years`),
  -- 复合索引（提高多条件匹配查询性能）
  KEY `idx_match_core` (`education_level`, `salary_type_preference`, `status`),
  KEY `idx_match_salary` (`salary_type_preference`, `salary_expectation_min`, `salary_expectation_max`),
  -- 业务索引
  KEY `idx_real_name` (`real_name`),
  KEY `idx_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_rating` (`rating_average`),
  KEY `idx_verified` (`is_verified`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='零工信息表（核心匹配优化版）';

-- 工作申请表（记录零工申请招聘的记录）
DROP TABLE IF EXISTS `job_application`;
CREATE TABLE `job_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `job_id` bigint(20) NOT NULL COMMENT '招聘ID',
  `worker_id` bigint(20) NOT NULL COMMENT '零工ID',
  `application_status` varchar(20) DEFAULT 'pending' COMMENT '申请状态（pending/accepted/rejected/withdrawn/completed）',
  `application_message` text COMMENT '申请留言',
  `employer_response` text COMMENT '雇主回复',
  `interview_time` datetime COMMENT '面试时间',
  `interview_location` varchar(500) COMMENT '面试地点',
  `interview_notes` text COMMENT '面试备注',
  `start_work_time` datetime COMMENT '开始工作时间',
  `end_work_time` datetime COMMENT '结束工作时间',
  `actual_salary` decimal(10,2) COMMENT '实际薪资',
  `work_rating` decimal(3,2) COMMENT '工作评分',
  `work_feedback` text COMMENT '工作反馈',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`application_id`),
  UNIQUE KEY `uk_job_worker` (`job_id`, `worker_id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_worker_id` (`worker_id`),
  KEY `idx_status` (`application_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作申请表';

-- 匹配记录表（记录系统推荐的匹配结果）
DROP TABLE IF EXISTS `job_match_record`;
CREATE TABLE `job_match_record` (
  `match_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '匹配ID',
  `job_id` bigint(20) NOT NULL COMMENT '招聘ID',
  `worker_id` bigint(20) NOT NULL COMMENT '零工ID',
  `match_score` decimal(5,2) NOT NULL COMMENT '匹配分数（0-100）',
  `match_factors` text COMMENT '匹配因素详情（JSON格式）',
  `match_type` varchar(20) DEFAULT 'system' COMMENT '匹配类型（system/manual）',
  `is_viewed_by_employer` tinyint(1) DEFAULT 0 COMMENT '雇主是否已查看（0否 1是）',
  `is_viewed_by_worker` tinyint(1) DEFAULT 0 COMMENT '零工是否已查看（0否 1是）',
  `employer_interest` varchar(20) COMMENT '雇主兴趣（interested/not_interested/contacted）',
  `worker_interest` varchar(20) COMMENT '零工兴趣（interested/not_interested/applied）',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`match_id`),
  UNIQUE KEY `uk_job_worker_match` (`job_id`, `worker_id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_worker_id` (`worker_id`),
  KEY `idx_match_score` (`match_score`),
  KEY `idx_match_type` (`match_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作匹配记录表';

-- 插入优化后的示例数据（核心匹配字段优化）
INSERT INTO `job_posting` (`job_title`, `job_description`, `job_type`, `job_category`, `work_location`, `salary_type`, `salary_min`, `salary_max`, `education_required`, `experience_required`, `work_hours_per_day`, `work_days_per_week`, `start_date`, `contact_person`, `contact_phone`, `company_name`, `positions_available`, `status`, `publisher_user_id`, `create_id`) VALUES
('餐厅服务员', '负责餐厅日常服务工作，包括点餐、上菜、收银等', '兼职', '服务员', '北京市朝阳区', 'hourly', 25.00, 35.00, '高中', '无经验要求', 8, 5, '2025-08-01', '张经理', '13800138001', '美味餐厅', 3, 'published', 1, 1),
('保洁员', '负责办公楼保洁工作，包括地面清洁、垃圾清理等', '全职', '保洁', '上海市浦东新区', 'monthly', 4000.00, 5000.00, '不限', '有相关经验优先', 8, 6, '2025-08-05', '李主管', '13900139001', '清洁服务公司', 2, 'published', 2, 2),
('搬运工', '负责货物搬运装卸工作', '临时工', '搬运工', '广州市天河区', 'daily', 200.00, 300.00, '不限', '体力好', 10, 6, '2025-08-01', '王主管', '13700137001', '物流公司', 5, 'published', 3, 3),
('销售代表', '负责产品销售和客户维护', '全职', '销售', '深圳市南山区', 'monthly', 4000.00, 8000.00, '大专', '1-3年销售经验', 8, 5, '2025-08-03', '陈经理', '13600136001', '贸易公司', 2, 'published', 4, 4),
('厨师助手', '协助厨师进行食材准备和厨房清洁', '兼职', '厨师助手', '成都市锦江区', 'hourly', 20.00, 30.00, '初中', '有厨房工作经验优先', 8, 6, '2025-08-02', '刘师傅', '13500135001', '餐饮公司', 3, 'published', 5, 5),
('快递员', '负责快递包裹的收发和配送工作', '全职', '快递员', '杭州市西湖区', 'daily', 180.00, 250.00, '高中', '熟悉当地路线优先', 10, 6, '2025-08-03', '周主管', '13400134001', '快递公司', 4, 'published', 6, 6),
('保安', '负责小区安全巡逻和门禁管理', '全职', '保安', '武汉市江汉区', 'monthly', 3500.00, 4500.00, '高中', '退伍军人优先', 12, 6, '2025-08-04', '赵队长', '13300133001', '物业公司', 2, 'published', 7, 7),
('收银员', '负责超市收银和客户服务工作', '兼职', '收银员', '西安市雁塔区', 'hourly', 18.00, 25.00, '高中', '有收银经验优先', 8, 5, '2025-08-05', '孙经理', '13200132001', '连锁超市', 3, 'published', 8, 8);

-- 零工信息示例数据（优化简化版）
INSERT INTO `worker_profile` (
  `user_id`, `real_name`, `nickname`, `gender`, `age`, `phone`, `current_location`,
  `education_level`, `work_experience_years`, `work_categories`, `job_types_preferred`,
  `skills`, `salary_expectation_min`, `salary_expectation_max`, `salary_type_preference`,
  `availability_start_date`, `work_days_per_week`, `work_hours_per_day`,
  `self_introduction`, `rating_average`, `rating_count`, `completed_jobs`,
  `success_rate`, `status`, `is_verified`, `create_id`
) VALUES
-- 1. 经验丰富的餐厅服务员
(101, '王小美', '美美', 'female', 29, '13612345678', '北京市朝阳区', '高中', 5,
'["服务员", "收银员", "前台接待"]', '["兼职", "临时工"]',
'{"服务技能": ["点餐服务", "收银操作", "客户沟通"], "语言能力": ["普通话", "基础英语"], "其他": ["熟练使用POS机", "酒水知识"]}',
20.00, 30.00, 'hourly', '2025-07-25', 5, 8,
'有5年餐饮服务经验，熟悉各类餐厅操作流程，服务态度好，沟通能力强。',
4.8, 25, 45, 95.50, 'active', 1, 1),

-- 2. 年轻的保洁员
(102, '张小强', '小强', 'male', 26, '13723456789', '上海市浦东新区', '初中', 3,
'["保洁", "搬运工", "仓库管理"]', '["全职", "兼职"]',
'{"清洁技能": ["地面清洁", "玻璃清洁", "垃圾分类"], "体力": ["搬运重物", "长时间站立"], "设备操作": ["清洁设备使用"]}',
3500.00, 4500.00, 'monthly', '2025-07-23', 6, 8,
'年轻力壮，工作认真负责，有团队合作精神，能够承担重体力劳动。',
4.5, 18, 32, 92.30, 'active', 1, 1),

-- 3. 有经验的搬运工
(103, '李大力', '大力', 'male', 39, '13834567890', '广州市天河区', '高中', 15,
'["搬运工", "装卸工", "快递员"]', '["全职", "临时工"]',
'{"体力技能": ["重物搬运", "货物装卸", "包装打包"], "驾驶": ["货车驾驶", "叉车操作"], "安全": ["安全操作规范"]}',
200.00, 300.00, 'daily', '2025-07-22', 6, 10,
'从事搬运工作15年，经验丰富，身体强壮，能够胜任各种重体力劳动，有叉车和货车驾驶经验。',
4.7, 42, 128, 96.80, 'active', 1, 1),

-- 4. 兼职销售员
(104, '陈小雅', '雅雅', 'female', 32, '13945678901', '深圳市南山区', '大专', 8,
'["销售", "客服", "市场推广"]', '["兼职", "临时工"]',
'{"销售技能": ["产品介绍", "客户开发", "谈判技巧"], "沟通": ["客户服务", "电话销售"], "办公": ["Excel", "PPT制作"]}',
25.00, 40.00, 'hourly', '2025-08-01', 4, 6,
'有丰富的销售经验，善于与客户沟通，具备良好的服务意识和团队协作能力。',
4.6, 35, 67, 94.20, 'active', 1, 1),

-- 5. 新手保安
(105, '赵小兵', '小兵', 'male', 24, '13056789012', '杭州市西湖区', '高中', 1,
'["保安", "门卫", "巡逻"]', '["全职", "兼职"]',
'{"安保技能": ["安全巡逻", "门禁管理", "应急处理"], "体能": ["体能良好", "反应敏捷"]}',
3000.00, 4000.00, 'monthly', '2025-07-25', 6, 12,
'年轻有活力，责任心强，虽然经验不多但学习能力强，愿意从基础做起。',
4.2, 8, 12, 88.50, 'active', 0, 1),

-- 6. 经验丰富的厨师助手
(106, '刘小华', '小华', 'male', 36, '13167890123', '成都市锦江区', '中专', 12,
'["厨师助手", "配菜员", "洗碗工"]', '["全职", "兼职"]',
'{"厨房技能": ["食材处理", "配菜准备", "厨房清洁"], "食品安全": ["食品卫生", "HACCP知识"], "体力": ["长时间站立", "快速操作"]}',
4000.00, 5500.00, 'monthly', '2025-07-24', 6, 10,
'在餐饮行业工作12年，熟悉各种厨房操作，工作效率高，团队配合好。',
4.4, 28, 89, 91.70, 'active', 1, 1),

-- 7. 灵活的家政服务员
(107, '孙小丽', '小丽', 'female', 34, '13278901234', '武汉市江汉区', '初中', 10,
'["家政服务", "保洁", "照顾老人"]', '["兼职", "临时工"]',
'{"家政技能": ["家庭清洁", "衣物整理", "简单烹饪"], "护理": ["老人照护", "基础护理"], "沟通": ["耐心细致", "责任心强"]}',
30.00, 50.00, 'hourly', '2025-07-26', 5, 8,
'有丰富的家政服务经验，工作细致认真，特别擅长老人照护，深受雇主信任。',
4.9, 52, 156, 97.40, 'active', 1, 1),

-- 8. 年轻的快递员
(108, '周小飞', '小飞', 'male', 28, '13389012345', '西安市雁塔区', '高中', 4,
'["快递员", "外卖员", "配送员"]', '["全职", "兼职"]',
'{"配送技能": ["路线规划", "快速配送", "客户服务"], "驾驶": ["电动车驾驶", "摩托车驾驶"], "体能": ["体力好", "速度快"]}',
150.00, 250.00, 'daily', '2025-07-23', 6, 10,
'年轻有活力，熟悉西安各区域路线，配送效率高，客户评价好。',
4.3, 31, 78, 89.60, 'active', 1, 1);