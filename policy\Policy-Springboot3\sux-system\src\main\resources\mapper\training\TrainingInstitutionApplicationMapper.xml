<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.TrainingInstitutionApplicationMapper">
    
    <resultMap type="TrainingInstitutionApplication" id="TrainingInstitutionApplicationResult">
        <result property="applicationId"    column="application_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="userId"    column="user_id"    />
        <result property="institutionName"    column="institution_name"    />
        <result property="institutionCode"    column="institution_code"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="institutionAddress"    column="institution_address"    />
        <result property="institutionType"    column="institution_type"    />
        <result property="establishedDate"    column="established_date"    />
        <result property="registeredCapital"    column="registered_capital"    />
        <result property="businessScope"    column="business_scope"    />
        <result property="trainingExperience"    column="training_experience"    />
        <result property="trainingCapacity"    column="training_capacity"    />
        <result property="trainingPlan"    column="training_plan"    />
        <result property="teacherInfo"    column="teacher_info"    />
        <result property="facilityInfo"    column="facility_info"    />
        <result property="qualificationFiles"    column="qualification_files"    />
        <result property="trainingPlanFile"    column="training_plan_file"    />
        <result property="teacherCertFiles"    column="teacher_cert_files"    />
        <result property="facilityFiles"    column="facility_files"    />
        <result property="otherFiles"    column="other_files"    />
        <result property="applicationStatus"    column="application_status"    />
        <result property="applicationTime"    column="application_time"    />
        <result property="reviewTime"    column="review_time"    />
        <result property="reviewer"    column="reviewer"    />
        <result property="reviewComment"    column="review_comment"    />
        <result property="applicationNote"    column="application_note"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <!-- 关联字段 -->
        <result property="orderTitle"    column="order_title"    />
        <result property="trainingType"    column="training_type"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
    </resultMap>

    <sql id="selectTrainingInstitutionApplicationVo">
        select tia.application_id, tia.order_id, tia.user_id, tia.institution_name, tia.institution_code,
               tia.legal_person, tia.contact_person, tia.contact_phone, tia.contact_email, tia.institution_address,
               tia.institution_type, tia.established_date, tia.registered_capital, tia.business_scope,
               tia.training_experience, tia.training_capacity, tia.training_plan, tia.teacher_info, tia.facility_info,
               tia.qualification_files, tia.training_plan_file, tia.teacher_cert_files, tia.facility_files, tia.other_files,
               tia.application_status, tia.application_time, tia.review_time, tia.reviewer, tia.review_comment,
               tia.application_note, tia.create_id, tia.create_time, tia.update_id, tia.update_time, tia.remark, tia.del_flag,
               tor.order_title, tor.training_type, tor.start_date, tor.end_date
        from training_institution_application tia
        left join training_order tor on tia.order_id = tor.order_id
    </sql>

    <select id="selectTrainingInstitutionApplicationList" parameterType="TrainingInstitutionApplication" resultMap="TrainingInstitutionApplicationResult">
        <include refid="selectTrainingInstitutionApplicationVo"/>
        <where>  
            tia.del_flag = '0'
            <if test="orderId != null "> and tia.order_id = #{orderId}</if>
            <if test="userId != null "> and tia.user_id = #{userId}</if>
            <if test="institutionName != null  and institutionName != ''"> and tia.institution_name like concat('%', #{institutionName}, '%')</if>
            <if test="institutionCode != null  and institutionCode != ''"> and tia.institution_code = #{institutionCode}</if>
            <if test="legalPerson != null  and legalPerson != ''"> and tia.legal_person like concat('%', #{legalPerson}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and tia.contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and tia.contact_phone = #{contactPhone}</if>
            <if test="contactEmail != null  and contactEmail != ''"> and tia.contact_email = #{contactEmail}</if>
            <if test="institutionType != null  and institutionType != ''"> and tia.institution_type = #{institutionType}</if>
            <if test="applicationStatus != null  and applicationStatus != ''"> and tia.application_status = #{applicationStatus}</if>
            <if test="reviewer != null  and reviewer != ''"> and tia.reviewer like concat('%', #{reviewer}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(tia.application_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(tia.application_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by tia.application_time desc
    </select>
    
    <select id="selectTrainingInstitutionApplicationByApplicationId" parameterType="Long" resultMap="TrainingInstitutionApplicationResult">
        <include refid="selectTrainingInstitutionApplicationVo"/>
        where tia.application_id = #{applicationId} and tia.del_flag = '0'
    </select>

    <select id="selectApplicationByOrderIdAndUserId" resultMap="TrainingInstitutionApplicationResult">
        <include refid="selectTrainingInstitutionApplicationVo"/>
        where tia.order_id = #{orderId} and tia.user_id = #{userId} and tia.del_flag = '0'
    </select>

    <select id="selectApplicationByOrderIdAndInstitutionName" resultMap="TrainingInstitutionApplicationResult">
        <include refid="selectTrainingInstitutionApplicationVo"/>
        where tia.order_id = #{orderId} and tia.institution_name = #{institutionName} and tia.del_flag = '0'
    </select>

    <select id="selectApplicationByOrderIdAndPhone" resultMap="TrainingInstitutionApplicationResult">
        <include refid="selectTrainingInstitutionApplicationVo"/>
        where tia.order_id = #{orderId} and tia.contact_phone = #{contactPhone} and tia.del_flag = '0'
    </select>

    <select id="countApplicationsByOrderId" parameterType="Long" resultType="int">
        select count(*) from training_institution_application 
        where order_id = #{orderId} and del_flag = '0'
    </select>

    <select id="countApprovedApplicationsByOrderId" parameterType="Long" resultType="int">
        select count(*) from training_institution_application 
        where order_id = #{orderId} and application_status = '1' and del_flag = '0'
    </select>

    <select id="getApplicationsByOrderId" parameterType="Long" resultMap="TrainingInstitutionApplicationResult">
        <include refid="selectTrainingInstitutionApplicationVo"/>
        where tia.order_id = #{orderId} and tia.del_flag = '0'
        order by tia.application_time desc
    </select>

    <select id="countByStatus" parameterType="String" resultType="int">
        select count(*) from training_institution_application 
        where application_status = #{status} and del_flag = '0'
    </select>

    <select id="selectPendingApplications" resultMap="TrainingInstitutionApplicationResult">
        <include refid="selectTrainingInstitutionApplicationVo"/>
        where tia.application_status = '0' and tia.del_flag = '0'
        order by tia.application_time asc
    </select>
        
    <insert id="insertTrainingInstitutionApplication" parameterType="TrainingInstitutionApplication" useGeneratedKeys="true" keyProperty="applicationId">
        insert into training_institution_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="institutionName != null and institutionName != ''">institution_name,</if>
            <if test="institutionCode != null">institution_code,</if>
            <if test="legalPerson != null and legalPerson != ''">legal_person,</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="institutionAddress != null and institutionAddress != ''">institution_address,</if>
            <if test="institutionType != null">institution_type,</if>
            <if test="establishedDate != null">established_date,</if>
            <if test="registeredCapital != null">registered_capital,</if>
            <if test="businessScope != null">business_scope,</if>
            <if test="trainingExperience != null">training_experience,</if>
            <if test="trainingCapacity != null">training_capacity,</if>
            <if test="trainingPlan != null">training_plan,</if>
            <if test="teacherInfo != null">teacher_info,</if>
            <if test="facilityInfo != null">facility_info,</if>
            <if test="qualificationFiles != null">qualification_files,</if>
            <if test="trainingPlanFile != null">training_plan_file,</if>
            <if test="teacherCertFiles != null">teacher_cert_files,</if>
            <if test="facilityFiles != null">facility_files,</if>
            <if test="otherFiles != null">other_files,</if>
            <if test="applicationStatus != null">application_status,</if>
            <if test="applicationTime != null">application_time,</if>
            <if test="reviewTime != null">review_time,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="reviewComment != null">review_comment,</if>
            <if test="applicationNote != null">application_note,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="institutionName != null and institutionName != ''">#{institutionName},</if>
            <if test="institutionCode != null">#{institutionCode},</if>
            <if test="legalPerson != null and legalPerson != ''">#{legalPerson},</if>
            <if test="contactPerson != null and contactPerson != ''">#{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="institutionAddress != null and institutionAddress != ''">#{institutionAddress},</if>
            <if test="institutionType != null">#{institutionType},</if>
            <if test="establishedDate != null">#{establishedDate},</if>
            <if test="registeredCapital != null">#{registeredCapital},</if>
            <if test="businessScope != null">#{businessScope},</if>
            <if test="trainingExperience != null">#{trainingExperience},</if>
            <if test="trainingCapacity != null">#{trainingCapacity},</if>
            <if test="trainingPlan != null">#{trainingPlan},</if>
            <if test="teacherInfo != null">#{teacherInfo},</if>
            <if test="facilityInfo != null">#{facilityInfo},</if>
            <if test="qualificationFiles != null">#{qualificationFiles},</if>
            <if test="trainingPlanFile != null">#{trainingPlanFile},</if>
            <if test="teacherCertFiles != null">#{teacherCertFiles},</if>
            <if test="facilityFiles != null">#{facilityFiles},</if>
            <if test="otherFiles != null">#{otherFiles},</if>
            <if test="applicationStatus != null">#{applicationStatus},</if>
            <if test="applicationTime != null">#{applicationTime},</if>
            <if test="reviewTime != null">#{reviewTime},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="reviewComment != null">#{reviewComment},</if>
            <if test="applicationNote != null">#{applicationNote},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTrainingInstitutionApplication" parameterType="TrainingInstitutionApplication">
        update training_institution_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="institutionName != null and institutionName != ''">institution_name = #{institutionName},</if>
            <if test="institutionCode != null">institution_code = #{institutionCode},</if>
            <if test="legalPerson != null and legalPerson != ''">legal_person = #{legalPerson},</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="institutionAddress != null and institutionAddress != ''">institution_address = #{institutionAddress},</if>
            <if test="institutionType != null">institution_type = #{institutionType},</if>
            <if test="establishedDate != null">established_date = #{establishedDate},</if>
            <if test="registeredCapital != null">registered_capital = #{registeredCapital},</if>
            <if test="businessScope != null">business_scope = #{businessScope},</if>
            <if test="trainingExperience != null">training_experience = #{trainingExperience},</if>
            <if test="trainingCapacity != null">training_capacity = #{trainingCapacity},</if>
            <if test="trainingPlan != null">training_plan = #{trainingPlan},</if>
            <if test="teacherInfo != null">teacher_info = #{teacherInfo},</if>
            <if test="facilityInfo != null">facility_info = #{facilityInfo},</if>
            <if test="qualificationFiles != null">qualification_files = #{qualificationFiles},</if>
            <if test="trainingPlanFile != null">training_plan_file = #{trainingPlanFile},</if>
            <if test="teacherCertFiles != null">teacher_cert_files = #{teacherCertFiles},</if>
            <if test="facilityFiles != null">facility_files = #{facilityFiles},</if>
            <if test="otherFiles != null">other_files = #{otherFiles},</if>
            <if test="applicationStatus != null">application_status = #{applicationStatus},</if>
            <if test="applicationTime != null">application_time = #{applicationTime},</if>
            <if test="reviewTime != null">review_time = #{reviewTime},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="reviewComment != null">review_comment = #{reviewComment},</if>
            <if test="applicationNote != null">application_note = #{applicationNote},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <delete id="deleteTrainingInstitutionApplicationByApplicationId" parameterType="Long">
        update training_institution_application set del_flag = '2' where application_id = #{applicationId}
    </delete>

    <delete id="deleteTrainingInstitutionApplicationByApplicationIds" parameterType="String">
        update training_institution_application set del_flag = '2' where application_id in
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>

    <update id="batchUpdateStatus">
        update training_institution_application
        set application_status = #{status},
            reviewer = #{reviewer},
            review_comment = #{reviewComment},
            review_time = now(),
            update_time = now()
        where application_id in
        <foreach item="applicationId" collection="applicationIds" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </update>

</mapper>
