<template>
  <div class="multi-file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="headers"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :limit="limit"
      :accept="accept"
      :multiple="multiple"
      :disabled="disabled"
      :show-file-list="true"
      :auto-upload="true"
      drag
    >
      <div class="upload-area">
        <el-icon class="upload-icon"><UploadFilled /></el-icon>
        <div class="upload-text">
          <p>将文件拖到此处，或<em>点击上传</em></p>
          <p class="upload-tip">{{ uploadTip }}</p>
        </div>
      </div>
    </el-upload>

    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      :title="previewTitle"
      width="80%"
      append-to-body
      destroy-on-close
    >
      <div class="preview-container">
        <!-- PDF预览 -->
        <iframe
          v-if="previewType === 'pdf'"
          :src="previewUrl"
          width="100%"
          height="600px"
          frameborder="0"
        ></iframe>
        
        <!-- 图片预览 -->
        <img
          v-else-if="previewType === 'image'"
          :src="previewUrl"
          style="width: 100%; max-height: 600px; object-fit: contain;"
          alt="预览图片"
        />
        
        <!-- 其他文件类型 -->
        <div v-else class="unsupported-preview">
          <el-icon size="64"><Document /></el-icon>
          <p>{{ previewTitle }}</p>
          <p>此文件类型不支持在线预览</p>
          <el-button type="primary" @click="downloadFile">下载文件</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Document } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'

const props = defineProps({
  // 已上传的文件列表（JSON字符串或数组）
  modelValue: {
    type: [String, Array],
    default: () => []
  },
  // 上传限制数量
  limit: {
    type: Number,
    default: 5
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: '.pdf,.doc,.docx,.jpg,.jpeg,.png,.gif'
  },
  // 文件大小限制（MB）
  maxSize: {
    type: Number,
    default: 10
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: true
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 上传提示文本
  uploadTip: {
    type: String,
    default: '支持 PDF、Word、图片格式，单个文件不超过10MB'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 上传相关
const uploadRef = ref()
const uploadUrl = computed(() => import.meta.env.VITE_APP_BASE_API + '/common/upload')
const headers = computed(() => ({ Authorization: 'Bearer ' + getToken() }))

// 文件列表
const fileList = ref([])

// 预览相关
const previewVisible = ref(false)
const previewUrl = ref('')
const previewTitle = ref('')
const previewType = ref('')

// 监听modelValue变化，更新文件列表
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    try {
      let files = []
      if (typeof newVal === 'string') {
        files = newVal ? JSON.parse(newVal) : []
      } else if (Array.isArray(newVal)) {
        files = newVal
      }
      
      fileList.value = files.map((file, index) => ({
        uid: file.uid || index,
        name: file.name || file.fileName || `文件${index + 1}`,
        url: file.url || file.filePath,
        status: 'success'
      }))
    } catch (error) {
      console.error('解析文件列表失败:', error)
      fileList.value = []
    }
  } else {
    fileList.value = []
  }
}, { immediate: true })

// 上传前检查
const beforeUpload = (file) => {
  // 检查文件类型
  const allowedTypes = props.accept.split(',').map(type => type.trim())
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
  
  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error(`不支持的文件类型: ${fileExtension}`)
    return false
  }
  
  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }
  
  return true
}

// 上传成功
const handleSuccess = (response, file) => {
  if (response.code === 200) {
    const newFile = {
      uid: file.uid,
      name: file.name,
      url: response.url,
      fileName: response.fileName,
      filePath: response.url
    }
    
    // 更新文件列表
    const currentFiles = getCurrentFiles()
    currentFiles.push(newFile)
    updateModelValue(currentFiles)
    
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.msg || '文件上传失败')
    // 移除失败的文件
    const index = fileList.value.findIndex(item => item.uid === file.uid)
    if (index > -1) {
      fileList.value.splice(index, 1)
    }
  }
}

// 上传失败
const handleError = (error, file) => {
  ElMessage.error('文件上传失败')
  console.error('上传错误:', error)
  
  // 移除失败的文件
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 移除文件
const handleRemove = (file) => {
  const currentFiles = getCurrentFiles()
  const index = currentFiles.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    currentFiles.splice(index, 1)
    updateModelValue(currentFiles)
  }
}

// 预览文件
const handlePreview = (file) => {
  previewTitle.value = file.name
  previewUrl.value = file.url
  
  // 判断文件类型
  const fileName = file.name.toLowerCase()
  if (fileName.endsWith('.pdf')) {
    previewType.value = 'pdf'
  } else if (fileName.match(/\.(jpg|jpeg|png|gif)$/)) {
    previewType.value = 'image'
  } else {
    previewType.value = 'other'
  }
  
  previewVisible.value = true
}

// 下载文件
const downloadFile = () => {
  if (previewUrl.value) {
    const link = document.createElement('a')
    link.href = previewUrl.value
    link.download = previewTitle.value
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 获取当前文件列表
const getCurrentFiles = () => {
  return fileList.value.map(file => ({
    uid: file.uid,
    name: file.name,
    url: file.url,
    fileName: file.name,
    filePath: file.url
  }))
}

// 更新modelValue
const updateModelValue = (files) => {
  const value = JSON.stringify(files)
  emit('update:modelValue', value)
  emit('change', files, value)
}

// 清空文件列表
const clearFiles = () => {
  fileList.value = []
  updateModelValue([])
}

// 暴露方法
defineExpose({
  clearFiles,
  getCurrentFiles
})
</script>

<style lang="scss" scoped>
.multi-file-upload {
  width: 100%;

  :deep(.el-upload) {
    width: 100%;
  }

  :deep(.el-upload-dragger) {
    width: 100%;
    height: 120px;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
      background-color: #f5f7fa;
    }
  }

  .upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px;

    .upload-icon {
      font-size: 32px;
      color: #c0c4cc;
      margin-bottom: 10px;
    }

    .upload-text {
      text-align: center;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;

        em {
          color: #409eff;
          font-style: normal;
        }
      }

      .upload-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
      }
    }
  }

  :deep(.el-upload-list) {
    margin-top: 10px;
  }

  :deep(.el-upload-list__item) {
    transition: all 0.3s;
    border-radius: 4px;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.preview-container {
  text-align: center;

  .unsupported-preview {
    padding: 40px;
    color: #909399;

    .el-icon {
      margin-bottom: 20px;
      color: #c0c4cc;
    }

    p {
      margin: 10px 0;
      font-size: 14px;
    }
  }
}
</style>
