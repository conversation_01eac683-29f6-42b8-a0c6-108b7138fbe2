package com.sux.system.service;

import com.sux.system.domain.TrainingInstitutionApplication;

import java.util.List;

/**
 * 培训机构申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ITrainingInstitutionApplicationService 
{
    /**
     * 查询培训机构申请
     * 
     * @param applicationId 培训机构申请主键
     * @return 培训机构申请
     */
    public TrainingInstitutionApplication selectTrainingInstitutionApplicationByApplicationId(Long applicationId);

    /**
     * 查询培训机构申请列表
     * 
     * @param trainingInstitutionApplication 培训机构申请
     * @return 培训机构申请集合
     */
    public List<TrainingInstitutionApplication> selectTrainingInstitutionApplicationList(TrainingInstitutionApplication trainingInstitutionApplication);

    /**
     * 新增培训机构申请
     * 
     * @param trainingInstitutionApplication 培训机构申请
     * @return 结果
     */
    public int insertTrainingInstitutionApplication(TrainingInstitutionApplication trainingInstitutionApplication);

    /**
     * 修改培训机构申请
     * 
     * @param trainingInstitutionApplication 培训机构申请
     * @return 结果
     */
    public int updateTrainingInstitutionApplication(TrainingInstitutionApplication trainingInstitutionApplication);

    /**
     * 批量删除培训机构申请
     * 
     * @param applicationIds 需要删除的培训机构申请主键集合
     * @return 结果
     */
    public int deleteTrainingInstitutionApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 删除培训机构申请信息
     * 
     * @param applicationId 培训机构申请主键
     * @return 结果
     */
    public int deleteTrainingInstitutionApplicationByApplicationId(Long applicationId);

    /**
     * 检查机构是否已申请某个培训订单
     * 
     * @param orderId 培训订单ID
     * @param userId 用户ID
     * @return 申请记录
     */
    public TrainingInstitutionApplication checkInstitutionApplication(Long orderId, Long userId);

    /**
     * 检查机构名称是否已申请某个培训订单
     * 
     * @param orderId 培训订单ID
     * @param institutionName 机构名称
     * @return 申请记录
     */
    public TrainingInstitutionApplication checkInstitutionNameApplication(Long orderId, String institutionName);

    /**
     * 检查联系电话是否已申请某个培训订单
     * 
     * @param orderId 培训订单ID
     * @param contactPhone 联系电话
     * @return 申请记录
     */
    public TrainingInstitutionApplication checkPhoneApplication(Long orderId, String contactPhone);

    /**
     * 统计某个培训订单的机构申请数量
     * 
     * @param orderId 培训订单ID
     * @return 申请数量
     */
    public int countApplicationsByOrderId(Long orderId);

    /**
     * 统计某个培训订单的已通过机构申请数量
     * 
     * @param orderId 培训订单ID
     * @return 已通过申请数量
     */
    public int countApprovedApplicationsByOrderId(Long orderId);

    /**
     * 获取某个培训订单的机构申请列表
     * 
     * @param orderId 培训订单ID
     * @return 申请列表
     */
    public List<TrainingInstitutionApplication> getApplicationsByOrderId(Long orderId);

    /**
     * 审核机构申请
     * 
     * @param applicationId 申请ID
     * @param status 审核状态
     * @param reviewer 审核人
     * @param reviewComment 审核意见
     * @return 结果
     */
    public int reviewApplication(Long applicationId, String status, String reviewer, String reviewComment);

    /**
     * 批量审核机构申请
     * 
     * @param applicationIds 申请ID数组
     * @param status 审核状态
     * @param reviewer 审核人
     * @param reviewComment 审核意见
     * @return 结果
     */
    public int batchReviewApplications(Long[] applicationIds, String status, String reviewer, String reviewComment);

    /**
     * 提交机构申请
     * 
     * @param trainingInstitutionApplication 申请信息
     * @return 结果
     */
    public int submitApplication(TrainingInstitutionApplication trainingInstitutionApplication);

    /**
     * 取消机构申请
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    public int cancelApplication(Long applicationId);

    /**
     * 检查申请唯一性
     * 
     * @param trainingInstitutionApplication 申请信息
     * @return 结果
     */
    public boolean checkApplicationUnique(TrainingInstitutionApplication trainingInstitutionApplication);

    /**
     * 根据申请状态统计数量
     * 
     * @param status 申请状态
     * @return 数量
     */
    public int countByStatus(String status);

    /**
     * 获取待审核的申请列表
     * 
     * @return 待审核申请列表
     */
    public List<TrainingInstitutionApplication> selectPendingApplications();

    /**
     * 获取当前用户的申请列表
     * 
     * @param userId 用户ID
     * @return 申请列表
     */
    public List<TrainingInstitutionApplication> getMyApplications(Long userId);
}
