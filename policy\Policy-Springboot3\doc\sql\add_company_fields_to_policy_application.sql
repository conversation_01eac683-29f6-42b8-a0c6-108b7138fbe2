-- 为 policy_application 表添加企业信息字段
-- 执行时间：2025-07-24

-- 添加企业基本信息字段
ALTER TABLE `policy_application` 
ADD COLUMN `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业名称' AFTER `required_materials`,
ADD COLUMN `company_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业统一社会信用代码' AFTER `company_name`,
ADD COLUMN `company_legal_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业法定代表人' AFTER `company_code`,
ADD COLUMN `company_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业注册地址' AFTER `company_legal_person`,
ADD COLUMN `company_contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业联系人' AFTER `company_address`,
ADD COLUMN `company_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业联系电话' AFTER `company_contact_person`;

-- 添加银行对公户信息字段
ALTER TABLE `policy_application` 
ADD COLUMN `bank_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '开户银行名称' AFTER `company_contact_phone`,
ADD COLUMN `bank_account_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '银行账户名称' AFTER `bank_name`,
ADD COLUMN `bank_account_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '银行账号' AFTER `bank_account_name`;

-- 添加索引以提高查询性能
ALTER TABLE `policy_application` 
ADD INDEX `idx_company_name`(`company_name`) USING BTREE,
ADD INDEX `idx_company_code`(`company_code`) USING BTREE;
