-- 更新场地管理菜单组件路径
-- 将复杂的 place/xxx/index 路径简化为 xxx/index

-- ==================== 更新菜单组件路径 ====================

-- 1. 更新用工信息管理菜单组件路径
-- 从 place/employment/index 改为 employment/index
UPDATE `sys_menu` 
SET `component` = 'employment/index' 
WHERE `menu_id` = 4001 AND `component` = 'place/employment/index';

-- 2. 更新场地信息管理菜单组件路径  
-- 从 place/info/index 改为 placeInfo/index
UPDATE `sys_menu` 
SET `component` = 'placeInfo/index' 
WHERE `menu_id` = 4002 AND `component` = 'place/info/index';

-- 3. 更新劳务市场管理菜单组件路径
-- 从 place/market/index 改为 market/index
UPDATE `sys_menu` 
SET `component` = 'market/index' 
WHERE `menu_id` = 4003 AND `component` = 'place/market/index';

-- ==================== 验证更新结果 ====================

-- 查询更新后的菜单配置
SELECT 
    menu_id, 
    menu_name, 
    parent_id, 
    path, 
    component, 
    perms, 
    icon,
    order_num
FROM `sys_menu` 
WHERE `menu_id` IN (4001, 4002, 4003)
ORDER BY menu_id;

-- ==================== 备份恢复SQL（如需回滚） ====================
/*
-- 如果需要回滚到原来的路径，可以执行以下SQL：

-- 恢复用工信息管理菜单组件路径
UPDATE `sys_menu` 
SET `component` = 'place/employment/index' 
WHERE `menu_id` = 4001 AND `component` = 'employment/index';

-- 恢复场地信息管理菜单组件路径
UPDATE `sys_menu` 
SET `component` = 'place/info/index' 
WHERE `menu_id` = 4002 AND `component` = 'placeInfo/index';

-- 恢复劳务市场管理菜单组件路径
UPDATE `sys_menu` 
SET `component` = 'place/market/index' 
WHERE `menu_id` = 4003 AND `component` = 'market/index';
*/

-- ==================== 说明 ====================
/*
更新说明：
1. 用工信息：place/employment/index → employment/index
2. 场地信息：place/info/index → placeInfo/index  
3. 劳务市场：place/market/index → market/index

对应的前端组件文件已移动到：
- Policy-Vue3/src/views/employment/index.vue
- Policy-Vue3/src/views/placeInfo/index.vue
- Policy-Vue3/src/views/market/index.vue

这样简化了目录结构，使其与 policy/policyPlan 保持一致的简单格式。
*/
