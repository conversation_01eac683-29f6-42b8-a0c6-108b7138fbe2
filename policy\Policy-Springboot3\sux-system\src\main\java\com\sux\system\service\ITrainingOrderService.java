package com.sux.system.service;

import com.sux.system.domain.TrainingOrder;

import java.util.List;

/**
 * 培训订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface ITrainingOrderService 
{
    /**
     * 查询培训订单
     * 
     * @param orderId 培训订单主键
     * @return 培训订单
     */
    public TrainingOrder selectTrainingOrderByOrderId(Long orderId);

    /**
     * 查询培训订单列表
     * 
     * @param trainingOrder 培训订单
     * @return 培训订单集合
     */
    public List<TrainingOrder> selectTrainingOrderList(TrainingOrder trainingOrder);

    /**
     * 新增培训订单
     * 
     * @param trainingOrder 培训订单
     * @return 结果
     */
    public int insertTrainingOrder(TrainingOrder trainingOrder);

    /**
     * 修改培训订单
     * 
     * @param trainingOrder 培训订单
     * @return 结果
     */
    public int updateTrainingOrder(TrainingOrder trainingOrder);

    /**
     * 批量删除培训订单
     * 
     * @param orderIds 需要删除的培训订单主键集合
     * @return 结果
     */
    public int deleteTrainingOrderByOrderIds(Long[] orderIds);

    /**
     * 删除培训订单信息
     * 
     * @param orderId 培训订单主键
     * @return 结果
     */
    public int deleteTrainingOrderByOrderId(Long orderId);

    /**
     * 校验订单标题是否唯一
     * 
     * @param trainingOrder 培训订单信息
     * @return 结果
     */
    public boolean checkOrderTitleUnique(TrainingOrder trainingOrder);

    /**
     * 发布培训订单
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int publishTrainingOrder(Long orderId);

    /**
     * 取消培训订单
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int cancelTrainingOrder(Long orderId);

    /**
     * 更新订单报名人数
     *
     * @param orderId 订单ID
     * @param increment 增量（正数为增加，负数为减少）
     * @return 结果
     */
    public int updateCurrentParticipants(Long orderId, int increment);

    /**
     * 重新计算并更新订单报名人数
     *
     * @param orderId 订单ID
     * @return 结果
     */
    public int updateCurrentParticipants(Long orderId);

    /**
     * 查询即将开始的培训订单（用于提醒）
     * 
     * @param days 提前天数
     * @return 培训订单集合
     */
    public List<TrainingOrder> selectUpcomingTrainingOrders(int days);

    /**
     * 查询已过期的培训订单（报名截止时间已过）
     * 
     * @return 培训订单集合
     */
    public List<TrainingOrder> selectExpiredTrainingOrders();

    /**
     * 统计各状态订单数量
     * 
     * @return 统计结果
     */
    public List<TrainingOrder> selectOrderStatusStatistics();

    /**
     * 自动更新订单状态（定时任务调用）
     * 
     * @return 更新的订单数量
     */
    public int autoUpdateOrderStatus();
}
