﻿.pager{ text-align:center; width:100%; padding:30px 0;}
.pagination{display: inline-block; border-radius: 4px; text-align:center;}
.pagination > .first{ margin-left: 0; border-bottom-left-radius: 4px; border-top-left-radius: 4px;}
.pagination > .last{ border-bottom-right-radius: 4px; border-top-right-radius: 4px;}
.pagination > a{ color:#1371E6;}
.pagination > a,.pagination > span{position: relative; float: left; padding: 6px 12px; line-height: 1.42857; text-decoration: none; background-color: white; border: 1px solid #dddddd; margin-left: -1px;}
.pagination > a:hover{ cursor:pointer;background-color: #eeeeee;}
.pagination > span:hover{ cursor:default;}
.pagination .current{  z-index: 2; color: white; background-color: #1371E6; border-color: #1371E6; cursor: default; }
.pagination .current.prev,.pagination .current.first, .pagination .current.next,.pagination .current.last { color: #999999; background-color: white; border-color: #dddddd; cursor: not-allowed;}
/*made By sunpengcheng for ie7 issues */
/* for ie 7*/
*+html .pagination{
	float: left;
	position:relative;
	left: 50%;
}
*+html .pagination > span,*+html .pagination > a{
	float: left;
	position: relative;
	left: -50%;
}