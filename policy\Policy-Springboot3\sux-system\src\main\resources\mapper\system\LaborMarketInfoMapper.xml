<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.LaborMarketInfoMapper">

    <resultMap type="LaborMarketInfo" id="LaborMarketInfoResult">
        <result property="marketId"                 column="market_id"                 />
        <result property="marketName"               column="market_name"               />
        <result property="marketCode"               column="market_code"               />
        <result property="marketType"               column="market_type"               />
        <result property="address"                  column="address"                   />
        <result property="regionCode"               column="region_code"               />
        <result property="regionName"               column="region_name"               />
        <result property="longitude"                column="longitude"                 />
        <result property="latitude"                 column="latitude"                  />
        <result property="contactPerson"            column="contact_person"            />
        <result property="contactPhone"             column="contact_phone"             />
        <result property="contactEmail"             column="contact_email"             />
        <result property="operatingHours"           column="operating_hours"           />
        <result property="serviceCategories"        column="service_categories"        />
        <result property="workerCapacity"           column="worker_capacity"           />
        <result property="currentWorkerCount"       column="current_worker_count"      />
        <result property="dailyAvgDemand"           column="daily_avg_demand"          />
        <result property="peakDemandTime"           column="peak_demand_time"          />
        <result property="managementFee"            column="management_fee"            />
        <result property="serviceFeeRate"           column="service_fee_rate"          />
        <result property="facilities"               column="facilities"                />
        <result property="safetyMeasures"           column="safety_measures"           />
        <result property="imageUrl"                 column="image_url"                 />
        <result property="imageGallery"             column="image_gallery"             />
        <result property="description"              column="description"               />
        <result property="status"                   column="status"                    />
        <result property="isFeatured"               column="is_featured"               />
        <result property="sortOrder"                column="sort_order"                />
        <result property="viewCount"                column="view_count"                />
        <result property="createId"                 column="create_id"                 />
        <result property="createTime"               column="create_time"               />
        <result property="updateId"                 column="update_id"                 />
        <result property="updateTime"               column="update_time"               />
        <result property="delFlag"                  column="del_flag"                  />
        <result property="remark"                   column="remark"                    />
    </resultMap>

    <sql id="selectLaborMarketInfoVo">
        select market_id, market_name, market_code, market_type, address, region_code, region_name,
               longitude, latitude, contact_person, contact_phone, contact_email, operating_hours,
               service_categories, worker_capacity, current_worker_count, daily_avg_demand, peak_demand_time,
               management_fee, service_fee_rate, facilities, safety_measures, image_url, image_gallery,
               description, status, is_featured, sort_order, view_count,
               create_id, create_time, update_id, update_time, del_flag, remark
        from labor_market_info
    </sql>

    <select id="selectLaborMarketInfoList" parameterType="LaborMarketInfo" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        <where>
            del_flag = '0'
            <if test="marketName != null and marketName != ''"> and market_name like concat('%', #{marketName}, '%')</if>
            <if test="marketCode != null and marketCode != ''"> and market_code = #{marketCode}</if>
            <if test="marketType != null and marketType != ''"> and market_type = #{marketType}</if>
            <if test="regionCode != null and regionCode != ''"> and region_code = #{regionCode}</if>
            <if test="regionName != null and regionName != ''"> and region_name like concat('%', #{regionName}, '%')</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="isFeatured != null"> and is_featured = #{isFeatured}</if>
        </where>
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectLaborMarketInfoByMarketId" parameterType="Long" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        where market_id = #{marketId} and del_flag = '0'
    </select>

    <insert id="insertLaborMarketInfo" parameterType="LaborMarketInfo" useGeneratedKeys="true" keyProperty="marketId">
        insert into labor_market_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="marketName != null and marketName != ''">market_name,</if>
            <if test="marketCode != null and marketCode != ''">market_code,</if>
            <if test="marketType != null and marketType != ''">market_type,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="regionCode != null and regionCode != ''">region_code,</if>
            <if test="regionName != null and regionName != ''">region_name,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email,</if>
            <if test="operatingHours != null and operatingHours != ''">operating_hours,</if>
            <if test="serviceCategories != null">service_categories,</if>
            <if test="workerCapacity != null">worker_capacity,</if>
            <if test="currentWorkerCount != null">current_worker_count,</if>
            <if test="dailyAvgDemand != null">daily_avg_demand,</if>
            <if test="peakDemandTime != null and peakDemandTime != ''">peak_demand_time,</if>
            <if test="managementFee != null">management_fee,</if>
            <if test="serviceFeeRate != null">service_fee_rate,</if>
            <if test="facilities != null">facilities,</if>
            <if test="safetyMeasures != null">safety_measures,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="imageGallery != null">image_gallery,</if>
            <if test="description != null">description,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="isFeatured != null">is_featured,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="marketName != null and marketName != ''">#{marketName},</if>
            <if test="marketCode != null and marketCode != ''">#{marketCode},</if>
            <if test="marketType != null and marketType != ''">#{marketType},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="regionCode != null and regionCode != ''">#{regionCode},</if>
            <if test="regionName != null and regionName != ''">#{regionName},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="contactPerson != null and contactPerson != ''">#{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">#{contactEmail},</if>
            <if test="operatingHours != null and operatingHours != ''">#{operatingHours},</if>
            <if test="serviceCategories != null">#{serviceCategories},</if>
            <if test="workerCapacity != null">#{workerCapacity},</if>
            <if test="currentWorkerCount != null">#{currentWorkerCount},</if>
            <if test="dailyAvgDemand != null">#{dailyAvgDemand},</if>
            <if test="peakDemandTime != null and peakDemandTime != ''">#{peakDemandTime},</if>
            <if test="managementFee != null">#{managementFee},</if>
            <if test="serviceFeeRate != null">#{serviceFeeRate},</if>
            <if test="facilities != null">#{facilities},</if>
            <if test="safetyMeasures != null">#{safetyMeasures},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="imageGallery != null">#{imageGallery},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="isFeatured != null">#{isFeatured},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="updateLaborMarketInfo" parameterType="LaborMarketInfo">
        update labor_market_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="marketName != null and marketName != ''">market_name = #{marketName},</if>
            <if test="marketCode != null and marketCode != ''">market_code = #{marketCode},</if>
            <if test="marketType != null and marketType != ''">market_type = #{marketType},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="regionCode != null and regionCode != ''">region_code = #{regionCode},</if>
            <if test="regionName != null and regionName != ''">region_name = #{regionName},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email = #{contactEmail},</if>
            <if test="operatingHours != null and operatingHours != ''">operating_hours = #{operatingHours},</if>
            <if test="serviceCategories != null">service_categories = #{serviceCategories},</if>
            <if test="workerCapacity != null">worker_capacity = #{workerCapacity},</if>
            <if test="currentWorkerCount != null">current_worker_count = #{currentWorkerCount},</if>
            <if test="dailyAvgDemand != null">daily_avg_demand = #{dailyAvgDemand},</if>
            <if test="peakDemandTime != null and peakDemandTime != ''">peak_demand_time = #{peakDemandTime},</if>
            <if test="managementFee != null">management_fee = #{managementFee},</if>
            <if test="serviceFeeRate != null">service_fee_rate = #{serviceFeeRate},</if>
            <if test="facilities != null">facilities = #{facilities},</if>
            <if test="safetyMeasures != null">safety_measures = #{safetyMeasures},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="imageGallery != null">image_gallery = #{imageGallery},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="isFeatured != null">is_featured = #{isFeatured},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where market_id = #{marketId}
    </update>

    <delete id="deleteLaborMarketInfoByMarketId" parameterType="Long">
        update labor_market_info set del_flag = '2' where market_id = #{marketId}
    </delete>

    <delete id="deleteLaborMarketInfoByMarketIds" parameterType="String">
        update labor_market_info set del_flag = '2' where market_id in
        <foreach item="marketId" collection="array" open="(" separator="," close=")">
            #{marketId}
        </foreach>
    </delete>

    <select id="selectFeaturedLaborMarketInfoList" parameterType="LaborMarketInfo" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        <where>
            del_flag = '0' and status = '0' and is_featured = 1
            <if test="marketType != null and marketType != ''"> and market_type = #{marketType}</if>
            <if test="regionCode != null and regionCode != ''"> and region_code = #{regionCode}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>

    <select id="selectActiveLaborMarketInfoList" parameterType="LaborMarketInfo" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        <where>
            del_flag = '0' and status = '0'
            <if test="marketType != null and marketType != ''"> and market_type = #{marketType}</if>
            <if test="regionCode != null and regionCode != ''"> and region_code = #{regionCode}</if>
        </where>
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectLaborMarketInfoByType" parameterType="String" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        where del_flag = '0' and status = '0' and market_type = #{marketType}
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectLaborMarketInfoByRegion" parameterType="String" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        where del_flag = '0' and status = '0' and region_code = #{regionCode}
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectLaborMarketInfoByServiceCategory" parameterType="String" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        where del_flag = '0' and status = '0' and service_categories like concat('%', #{serviceCategory}, '%')
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectLaborMarketInfoStatistics" resultType="Map">
        select
            count(*) as total_count,
            sum(case when status = '0' then 1 else 0 end) as active_count,
            sum(case when is_featured = 1 then 1 else 0 end) as featured_count,
            sum(worker_capacity) as total_worker_capacity,
            sum(current_worker_count) as total_current_worker_count,
            sum(daily_avg_demand) as total_daily_avg_demand,
            avg(management_fee) as avg_management_fee,
            avg(service_fee_rate) as avg_service_fee_rate
        from labor_market_info
        where del_flag = '0'
    </select>

    <select id="selectLaborMarketInfoByKeyword" parameterType="String" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        where del_flag = '0' and status = '0'
        and (market_name like concat('%', #{keyword}, '%')
             or address like concat('%', #{keyword}, '%')
             or service_categories like concat('%', #{keyword}, '%')
             or description like concat('%', #{keyword}, '%'))
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <update id="updateLaborMarketInfoViewCount" parameterType="Long">
        update labor_market_info set view_count = view_count + 1 where market_id = #{marketId}
    </update>

    <select id="selectLaborMarketInfoDetailByMarketId" parameterType="Long" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        where market_id = #{marketId} and del_flag = '0'
    </select>

    <select id="selectAllMarketTypes" resultType="String">
        select distinct market_type from labor_market_info
        where del_flag = '0' and market_type is not null and market_type != ''
        order by market_type
    </select>

    <select id="selectAllRegions" resultType="Map">
        select distinct region_code as regionCode, region_name as regionName from labor_market_info
        where del_flag = '0' and region_code is not null and region_code != ''
        order by region_code
    </select>

    <select id="selectAllServiceCategories" resultType="String">
        select distinct service_categories from labor_market_info
        where del_flag = '0' and service_categories is not null and service_categories != ''
        order by service_categories
    </select>

    <select id="selectLaborMarketInfoByCapacityRange" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        where del_flag = '0' and status = '0'
        <if test="param1 != null"> and worker_capacity >= #{param1}</if>
        <if test="param2 != null"> and worker_capacity &lt;= #{param2}</if>
        order by is_featured desc, worker_capacity desc, create_time desc
    </select>

    <select id="selectLaborMarketInfoByFeeRange" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        where del_flag = '0' and status = '0'
        <if test="param1 != null"> and management_fee >= #{param1}</if>
        <if test="param2 != null"> and management_fee &lt;= #{param2}</if>
        order by is_featured desc, management_fee asc, create_time desc
    </select>

    <select id="selectHighDemandLaborMarketInfo" parameterType="Integer" resultMap="LaborMarketInfoResult">
        <include refid="selectLaborMarketInfoVo"/>
        where del_flag = '0' and status = '0' and daily_avg_demand > 0
        order by daily_avg_demand desc, is_featured desc, create_time desc
        limit #{limit}
    </select>

</mapper>
