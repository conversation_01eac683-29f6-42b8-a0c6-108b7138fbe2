<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.PolicyApprovalRecordMapper">
    
    <resultMap type="PolicyApprovalRecord" id="PolicyApprovalRecordResult">
        <result property="recordId"             column="record_id"             />
        <result property="applicationId"        column="application_id"        />
        <result property="approvalLevel"        column="approval_level"        />
        <result property="approvalStatus"       column="approval_status"       />
        <result property="approverUserId"       column="approver_user_id"      />
        <result property="approvalTime"         column="approval_time"         />
        <result property="approvalComment"      column="approval_comment"      />
        <result property="approvalFiles"        column="approval_files"        />
        <result property="delFlag"              column="del_flag"              />
        <result property="createId"             column="create_id"             />
        <result property="createTime"           column="create_time"           />
        <result property="updateId"             column="update_id"             />
        <result property="updateTime"           column="update_time"           />
        <result property="remark"               column="remark"                />
        <result property="approverUserName"     column="approver_user_name"    />
        <result property="approverNickName"     column="approver_nick_name"    />
    </resultMap>

    <sql id="selectPolicyApprovalRecordVo">
        select par.record_id, par.application_id, par.approval_level, par.approval_status, 
               par.approver_user_id, par.approval_time, par.approval_comment, par.approval_files,
               par.del_flag, par.create_id, par.create_time, par.update_id, par.update_time, par.remark,
               su.user_name as approver_user_name, su.nick_name as approver_nick_name
        from policy_approval_record par
        left join sys_user su on par.approver_user_id = su.user_id
    </sql>

    <select id="selectPolicyApprovalRecordList" parameterType="PolicyApprovalRecord" resultMap="PolicyApprovalRecordResult">
        <include refid="selectPolicyApprovalRecordVo"/>
        <where>  
            par.del_flag = '0'
            <if test="applicationId != null "> and par.application_id = #{applicationId}</if>
            <if test="approvalLevel != null "> and par.approval_level = #{approvalLevel}</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and par.approval_status = #{approvalStatus}</if>
            <if test="approverUserId != null "> and par.approver_user_id = #{approverUserId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND par.approval_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND par.approval_time &lt;= #{params.endTime}
            </if>
        </where>
        order by par.approval_level asc, par.create_time asc
    </select>
    
    <select id="selectPolicyApprovalRecordByRecordId" parameterType="Long" resultMap="PolicyApprovalRecordResult">
        <include refid="selectPolicyApprovalRecordVo"/>
        where par.record_id = #{recordId} and par.del_flag = '0'
    </select>

    <select id="selectRecordsByApplicationId" parameterType="Long" resultMap="PolicyApprovalRecordResult">
        <include refid="selectPolicyApprovalRecordVo"/>
        where par.application_id = #{applicationId} and par.del_flag = '0'
        order by par.approval_level asc, par.create_time asc
    </select>

    <select id="selectRecordByApplicationIdAndLevel" resultMap="PolicyApprovalRecordResult">
        <include refid="selectPolicyApprovalRecordVo"/>
        where par.application_id = #{applicationId} and par.approval_level = #{approvalLevel} and par.del_flag = '0'
        limit 1
    </select>
        
    <insert id="insertPolicyApprovalRecord" parameterType="PolicyApprovalRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into policy_approval_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationId != null">application_id,</if>
            <if test="approvalLevel != null">approval_level,</if>
            <if test="approvalStatus != null">approval_status,</if>
            <if test="approverUserId != null">approver_user_id,</if>
            <if test="approvalTime != null">approval_time,</if>
            <if test="approvalComment != null">approval_comment,</if>
            <if test="approvalFiles != null">approval_files,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationId != null">#{applicationId},</if>
            <if test="approvalLevel != null">#{approvalLevel},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
            <if test="approverUserId != null">#{approverUserId},</if>
            <if test="approvalTime != null">#{approvalTime},</if>
            <if test="approvalComment != null">#{approvalComment},</if>
            <if test="approvalFiles != null">#{approvalFiles},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePolicyApprovalRecord" parameterType="PolicyApprovalRecord">
        update policy_approval_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationId != null">application_id = #{applicationId},</if>
            <if test="approvalLevel != null">approval_level = #{approvalLevel},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="approverUserId != null">approver_user_id = #{approverUserId},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="approvalComment != null">approval_comment = #{approvalComment},</if>
            <if test="approvalFiles != null">approval_files = #{approvalFiles},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <update id="updateApprovalRecord">
        update policy_approval_record 
        set approval_status = #{approvalStatus},
            approver_user_id = #{approverUserId},
            approval_time = now(),
            approval_comment = #{approvalComment},
            approval_files = #{approvalFiles}
        where record_id = #{recordId}
    </update>

    <delete id="deletePolicyApprovalRecordByRecordId" parameterType="Long">
        update policy_approval_record set del_flag = '2' where record_id = #{recordId}
    </delete>

    <delete id="deletePolicyApprovalRecordByRecordIds" parameterType="String">
        update policy_approval_record set del_flag = '2' where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>
