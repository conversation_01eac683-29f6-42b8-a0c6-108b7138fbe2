package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训订单对象 training_order
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@TableName("training_order")
public class TrainingOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    @TableId(type = IdType.AUTO)
    private Long orderId;

    /** 订单标题 */
    @Excel(name = "订单标题")
    @NotBlank(message = "订单标题不能为空")
    @Size(min = 0, max = 200, message = "订单标题不能超过200个字符")
    private String orderTitle;

    /** 订单描述 */
    @Excel(name = "订单描述")
    private String orderDescription;

    /** 培训类型 */
    @Excel(name = "培训类型")
    @Size(min = 0, max = 50, message = "培训类型不能超过50个字符")
    private String trainingType;

    /** 培训分类 */
    @Excel(name = "培训分类")
    @Size(min = 0, max = 50, message = "培训分类不能超过50个字符")
    private String trainingCategory;

    /** 培训级别 */
    @Excel(name = "培训级别")
    @Size(min = 0, max = 20, message = "培训级别不能超过20个字符")
    private String trainingLevel;

    /** 培训时长(小时) */
    @Excel(name = "培训时长(小时)")
    private Integer trainingDuration;

    /** 最大参与人数 */
    @Excel(name = "最大参与人数")
    private Integer maxParticipants;

    /** 当前报名人数 */
    @Excel(name = "当前报名人数")
    private Integer currentParticipants;

    /** 培训费用 */
    @Excel(name = "培训费用")
    private BigDecimal trainingFee;

    /** 培训地址 */
    @Excel(name = "培训地址")
    @Size(min = 0, max = 500, message = "培训地址不能超过500个字符")
    private String trainingAddress;

    /** 联系人 */
    @Excel(name = "联系人")
    @Size(min = 0, max = 50, message = "联系人不能超过50个字符")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @Size(min = 0, max = 20, message = "联系电话不能超过20个字符")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    @Size(min = 0, max = 100, message = "联系邮箱不能超过100个字符")
    private String contactEmail;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /** 报名截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registrationDeadline;

    /** 订单状态（0草稿 1发布 2进行中 3已完成 4已取消） */
    @Excel(name = "订单状态", readConverterExp = "0=草稿,1=发布,2=进行中,3=已完成,4=已取消")
    private String orderStatus;

    /** 是否推荐（0否 1是） */
    @Excel(name = "是否推荐", readConverterExp = "0=否,1=是")
    private String isFeatured;

    /** 报名要求 */
    @Excel(name = "报名要求")
    private String requirements;

    /** 证书信息 */
    @Excel(name = "证书信息")
    @Size(min = 0, max = 500, message = "证书信息不能超过500个字符")
    private String certificateInfo;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField("del_flag")
    private String delFlag;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderTitle() {
        return orderTitle;
    }

    public void setOrderTitle(String orderTitle) {
        this.orderTitle = orderTitle;
    }

    public String getOrderDescription() {
        return orderDescription;
    }

    public void setOrderDescription(String orderDescription) {
        this.orderDescription = orderDescription;
    }

    public String getTrainingType() {
        return trainingType;
    }

    public void setTrainingType(String trainingType) {
        this.trainingType = trainingType;
    }

    public String getTrainingCategory() {
        return trainingCategory;
    }

    public void setTrainingCategory(String trainingCategory) {
        this.trainingCategory = trainingCategory;
    }

    public String getTrainingLevel() {
        return trainingLevel;
    }

    public void setTrainingLevel(String trainingLevel) {
        this.trainingLevel = trainingLevel;
    }

    public Integer getTrainingDuration() {
        return trainingDuration;
    }

    public void setTrainingDuration(Integer trainingDuration) {
        this.trainingDuration = trainingDuration;
    }

    public Integer getMaxParticipants() {
        return maxParticipants;
    }

    public void setMaxParticipants(Integer maxParticipants) {
        this.maxParticipants = maxParticipants;
    }

    public Integer getCurrentParticipants() {
        return currentParticipants;
    }

    public void setCurrentParticipants(Integer currentParticipants) {
        this.currentParticipants = currentParticipants;
    }

    public BigDecimal getTrainingFee() {
        return trainingFee;
    }

    public void setTrainingFee(BigDecimal trainingFee) {
        this.trainingFee = trainingFee;
    }

    public String getTrainingAddress() {
        return trainingAddress;
    }

    public void setTrainingAddress(String trainingAddress) {
        this.trainingAddress = trainingAddress;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getRegistrationDeadline() {
        return registrationDeadline;
    }

    public void setRegistrationDeadline(Date registrationDeadline) {
        this.registrationDeadline = registrationDeadline;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getIsFeatured() {
        return isFeatured;
    }

    public void setIsFeatured(String isFeatured) {
        this.isFeatured = isFeatured;
    }

    public String getRequirements() {
        return requirements;
    }

    public void setRequirements(String requirements) {
        this.requirements = requirements;
    }

    public String getCertificateInfo() {
        return certificateInfo;
    }

    public void setCertificateInfo(String certificateInfo) {
        this.certificateInfo = certificateInfo;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "TrainingOrder{" +
                "orderId=" + orderId +
                ", orderTitle='" + orderTitle + '\'' +
                ", orderDescription='" + orderDescription + '\'' +
                ", trainingType='" + trainingType + '\'' +
                ", trainingCategory='" + trainingCategory + '\'' +
                ", trainingLevel='" + trainingLevel + '\'' +
                ", trainingDuration=" + trainingDuration +
                ", maxParticipants=" + maxParticipants +
                ", currentParticipants=" + currentParticipants +
                ", trainingFee=" + trainingFee +
                ", trainingAddress='" + trainingAddress + '\'' +
                ", contactPerson='" + contactPerson + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", registrationDeadline=" + registrationDeadline +
                ", orderStatus='" + orderStatus + '\'' +
                ", isFeatured='" + isFeatured + '\'' +
                ", requirements='" + requirements + '\'' +
                ", certificateInfo='" + certificateInfo + '\'' +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }
}
