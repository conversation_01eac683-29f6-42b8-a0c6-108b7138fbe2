/**
 * Cron表达式解释工具类
 */

/**
 * 解释cron表达式
 * @param {string} cronExpression - cron表达式
 * @returns {string} 解释文本
 */
export function parseCronExpression(cronExpression) {
  if (!cronExpression || typeof cronExpression !== 'string') {
    return '无效的cron表达式';
  }

  try {
    const parts = cronExpression.trim().split(/\s+/);
    
    // 标准6位cron表达式: 秒 分 时 日 月 周
    // 标准7位cron表达式: 秒 分 时 日 月 周 年
    if (parts.length < 6 || parts.length > 7) {
      return '无效的cron表达式格式';
    }

    const [second, minute, hour, day, month, week, year] = parts;

    const secondDesc = parseTimeUnit(second, 'second');
    const minuteDesc = parseTimeUnit(minute, 'minute');
    const hourDesc = parseTimeUnit(hour, 'hour');
    const dayDesc = parseDayUnit(day);
    const monthDesc = parseMonthUnit(month);
    const weekDesc = parseWeekUnit(week);
    const yearDesc = year ? parseYearUnit(year) : '';

    let description = '';

    // 构建描述
    if (yearDesc) {
      description += yearDesc + '，';
    }
    
    if (monthDesc !== '每月') {
      description += monthDesc + '，';
    }
    
    if (weekDesc !== '?' && weekDesc !== '*') {
      description += weekDesc + '，';
    } else if (dayDesc !== '每天' && dayDesc !== '?') {
      description += dayDesc + '，';
    }
    
    if (hourDesc !== '每小时') {
      description += hourDesc + '，';
    }
    
    if (minuteDesc !== '每分钟') {
      description += minuteDesc + '，';
    }
    
    if (secondDesc !== '每秒') {
      description += secondDesc;
    }

    // 清理末尾的逗号
    description = description.replace(/，$/, '');
    
    // 如果描述为空，则给出基本描述
    if (!description) {
      description = '每秒执行';
    } else {
      description += '执行';
    }

    return description;
  } catch (error) {
    return '无法解析cron表达式';
  }
}

/**
 * 解析时间单位（秒、分、时）
 * @param {string} value 
 * @param {string} unit 
 * @returns {string}
 */
function parseTimeUnit(value, unit) {
  const unitMap = {
    'second': { name: '秒', max: 59 },
    'minute': { name: '分钟', max: 59 },
    'hour': { name: '小时', max: 23 }
  };

  const unitInfo = unitMap[unit];
  if (!unitInfo) return value;

  if (value === '*') {
    return `每${unitInfo.name}`;
  }

  if (value.includes('-')) {
    const [start, end] = value.split('-');
    return `${start}-${end}${unitInfo.name}`;
  }

  if (value.includes('/')) {
    const [start, interval] = value.split('/');
    if (start === '*') {
      return `每${interval}${unitInfo.name}`;
    } else {
      return `从${start}${unitInfo.name}开始，每${interval}${unitInfo.name}`;
    }
  }

  if (value.includes(',')) {
    const values = value.split(',');
    return `${values.join('、')}${unitInfo.name}`;
  }

  // 具体数值
  if (/^\d+$/.test(value)) {
    return `第${value}${unitInfo.name}`;
  }

  return value;
}

/**
 * 解析日期单位
 * @param {string} value 
 * @returns {string}
 */
function parseDayUnit(value) {
  if (value === '*') {
    return '每天';
  }

  if (value === '?') {
    return '?';
  }

  if (value.includes('-')) {
    const [start, end] = value.split('-');
    return `${start}-${end}号`;
  }

  if (value.includes('/')) {
    const [start, interval] = value.split('/');
    if (start === '*') {
      return `每${interval}天`;
    } else {
      return `从${start}号开始，每${interval}天`;
    }
  }

  if (value.includes(',')) {
    const values = value.split(',');
    return `${values.join('、')}号`;
  }

  if (value.includes('W')) {
    const day = value.replace('W', '');
    return `${day}号最近的工作日`;
  }

  if (value === 'L') {
    return '本月最后一天';
  }

  // 具体数值
  if (/^\d+$/.test(value)) {
    return `${value}号`;
  }

  return value;
}

/**
 * 解析月份单位
 * @param {string} value 
 * @returns {string}
 */
function parseMonthUnit(value) {
  if (value === '*') {
    return '每月';
  }

  const monthNames = ['', '一月', '二月', '三月', '四月', '五月', '六月',
                     '七月', '八月', '九月', '十月', '十一月', '十二月'];

  if (value.includes('-')) {
    const [start, end] = value.split('-');
    const startName = monthNames[parseInt(start)] || start;
    const endName = monthNames[parseInt(end)] || end;
    return `${startName}-${endName}`;
  }

  if (value.includes('/')) {
    const [start, interval] = value.split('/');
    if (start === '*') {
      return `每${interval}个月`;
    } else {
      const startName = monthNames[parseInt(start)] || start;
      return `从${startName}开始，每${interval}个月`;
    }
  }

  if (value.includes(',')) {
    const values = value.split(',');
    const names = values.map(v => monthNames[parseInt(v)] || v);
    return names.join('、');
  }

  // 具体数值
  if (/^\d+$/.test(value)) {
    return monthNames[parseInt(value)] || `第${value}月`;
  }

  return value;
}

/**
 * 解析星期单位
 * @param {string} value 
 * @returns {string}
 */
function parseWeekUnit(value) {
  if (value === '*') {
    return '*';
  }

  if (value === '?') {
    return '?';
  }

  const weekNames = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

  if (value.includes('-')) {
    const [start, end] = value.split('-');
    const startName = weekNames[parseInt(start) - 1] || start;
    const endName = weekNames[parseInt(end) - 1] || end;
    return `${startName}-${endName}`;
  }

  if (value.includes('/')) {
    const [start, interval] = value.split('/');
    if (start === '*') {
      return `每${interval}周`;
    } else {
      const startName = weekNames[parseInt(start) - 1] || start;
      return `从${startName}开始，每${interval}周`;
    }
  }

  if (value.includes(',')) {
    const values = value.split(',');
    const names = values.map(v => weekNames[parseInt(v) - 1] || v);
    return names.join('、');
  }

  if (value.includes('#')) {
    const [week, nth] = value.split('#');
    const weekName = weekNames[parseInt(week) - 1] || week;
    return `第${nth}周的${weekName}`;
  }

  if (value.includes('L')) {
    const week = value.replace('L', '');
    const weekName = weekNames[parseInt(week) - 1] || week;
    return `本月最后一个${weekName}`;
  }

  // 具体数值
  if (/^\d+$/.test(value)) {
    return weekNames[parseInt(value) - 1] || `第${value}周`;
  }

  return value;
}

/**
 * 解析年份单位
 * @param {string} value 
 * @returns {string}
 */
function parseYearUnit(value) {
  if (value === '*') {
    return '每年';
  }

  if (value.includes('-')) {
    const [start, end] = value.split('-');
    return `${start}-${end}年`;
  }

  if (value.includes('/')) {
    const [start, interval] = value.split('/');
    if (start === '*') {
      return `每${interval}年`;
    } else {
      return `从${start}年开始，每${interval}年`;
    }
  }

  if (value.includes(',')) {
    const values = value.split(',');
    return `${values.join('、')}年`;
  }

  // 具体数值
  if (/^\d+$/.test(value)) {
    return `${value}年`;
  }

  return value;
}

/**
 * 获取cron表达式的下次执行时间描述（简化版）
 * @param {string} cronExpression 
 * @returns {string}
 */
export function getNextRunTimeDescription(cronExpression) {
  try {
    // 这里可以接入更复杂的cron解析库
    // 目前提供基本的解释
    return '根据表达式计算下次执行时间';
  } catch (error) {
    return '无法计算下次执行时间';
  }
}

/**
 * 检查cron表达式是否有效
 * @param {string} cronExpression 
 * @returns {boolean}
 */
export function isValidCronExpression(cronExpression) {
  if (!cronExpression || typeof cronExpression !== 'string') {
    return false;
  }

  const parts = cronExpression.trim().split(/\s+/);
  return parts.length >= 6 && parts.length <= 7;
} 