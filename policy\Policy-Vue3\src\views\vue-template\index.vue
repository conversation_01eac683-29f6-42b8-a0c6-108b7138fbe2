<template>
   <div class="template-container app-container">
      <!-- 使用 TableList 组件 -->
      <TableList v-if="isTableReady && tableColumns.length > 0" :columns="tableColumns" :data="tableData"
         :loading="tableLoading" :showIndex="true" :searchColumns="searchableColumns" :showOperation="true"
         operationLabel="操作" operationWidth="220" :fixedOperation="true" ref="tableListRef" @search="handleSearch"
         @reset="resetSearch" :defaultPage="{ pageSize: pageSize, currentPage: currentPage, total: total }"
         @current-change="handleCurrentChange" @size-change="handleSizeChange">

         <!-- 左侧按钮插槽 -->
         <template #menu-left>
            <el-button type="primary" class="common-btn" @click="handleAdd" v-hasPermi="['template:add']">
               新 增
            </el-button>
            <el-button type="warning" class="common-btn" @click="handleExport" v-hasPermi="['template:export']">
               导 出
            </el-button>
            <el-button type="info" class="common-btn" @click="handleImport" v-hasPermi="['template:import']">
               导 入
            </el-button>
         </template>

         <!-- 状态列插槽 -->
         <template #status="{ row }">
            <el-switch v-if="row && row.id && row.userName" v-model="row.status" active-value="0" inactive-value="1"
               @change="(val) => handleStatusChange(row, val)"></el-switch>
            <span v-else>-</span>
         </template>

         <!-- 价格列插槽 -->
         <template #price="{ row }">
            <div class="price-cell">
               <span v-if="row.price" class="price-text">
                  ¥{{ parseFloat(row.price).toFixed(2) }}
               </span>
               <span v-else class="no-price">-</span>
            </div>
         </template>

         <!-- 操作列插槽 -->
         <template #menu="{ row }">
            <div>
               <el-button type="primary" link @click="handleEdit(row)" v-hasPermi="['template:edit']">
                  修改
               </el-button>
               <el-button type="danger" link @click="handleDelete(row)" v-hasPermi="['template:remove']">
                  删除
               </el-button>
               <el-button type="info" link @click="handleView(row)" v-hasPermi="['template:query']">
                  详细
               </el-button>
               <el-button type="success" link @click="handleCopy(row)" v-hasPermi="['template:add']">
                  复制
               </el-button>
            </div>
         </template>
      </TableList>
      <div v-else class="loading-placeholder">
         <el-empty description="正在加载表格配置..."></el-empty>
      </div>

      <!-- 自定义内容插槽 -->
      <slot name="custom-content"></slot>

      <!-- 表单弹窗组件 -->
      <TemplateFormDialog ref="templateFormDialogRef" :formFields="formFields" :formOption="formOption"
         @submit="handleFormSubmit" @cancel="handleFormCancel" />
   </div>
</template>

<script setup name="Template">
import { ref, reactive, onMounted, getCurrentInstance, computed, nextTick } from 'vue';
import { createTemplateTableOption } from "@/const/vue-template/index";
// import { listTemplate, getTemplate, delTemplate, addTemplate, updateTemplate, changeTemplateStatus } from "@/api/vue-template/template";
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils";
import TableList from '@/components/TableList/index.vue';
import TemplateFormDialog from './TemplateFormDialog.vue';

const router = useRouter();
const { proxy } = getCurrentInstance();
const { sys_normal_disable, sys_yes_no } = proxy.useDict("sys_normal_disable", "sys_yes_no");

const tableColumns = ref([]);
const searchableColumns = ref([]); // 可搜索的字段列表
const tableLoading = ref(false);
const isTableReady = ref(false);
const formOption = ref({
   dialogWidth: '800px',
   dialogHeight: '70vh'
});
const tableListRef = ref(null);
const templateFormDialogRef = ref(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref({});

// 表格数据
const tableData = ref([]);

// 表单字段配置
const formFields = ref([]);

// 模拟假数据 - 与配置文件字段对应
const mockData = ref([
   {
      id: 1,
      userName: "张三",
      password: "123456",
      remark: "这是一个用于演示的用户信息记录，包含完整的个人资料和偏好设置",
      age: 28,
      price: 299.99,
      status: "0",
      orgId: "1-1",
      sex: "1",
      type: "2",
      hobbies: ["reading", "sports"],
      isEnabled: true,
      birthday: "1995-03-15",
      createTime: "2024-01-01 09:00:00",
      workTime: "09:00:00",
      validPeriod: ["2024-01-01", "2024-12-31"],
      themeColor: "#409eff",
      rating: 4.5,
      region: ["beijing", "chaoyang"],
      customField: "自定义内容示例",
      showExtendInfo: "Y",
      extendInfo: "这是扩展信息内容，用于展示更多详细信息"
   },
   {
      id: 2,
      userName: "李四",
      password: "123456",
      remark: "资深开发工程师，负责系统架构设计和核心功能开发",
      age: 32,
      price: 599.00,
      status: "0",
      orgId: "1-1-1",
      sex: "1",
      type: "3",
      hobbies: ["music", "travel"],
      isEnabled: true,
      birthday: "1991-07-22",
      createTime: "2024-01-05 10:15:00",
      workTime: "08:30:00",
      validPeriod: ["2024-01-05", "2024-12-31"],
      themeColor: "#67c23a",
      rating: 5.0,
      region: ["shanghai", "huangpu"],
      customField: "高级用户配置",
      showExtendInfo: "Y",
      extendInfo: "具有丰富的项目经验，擅长前后端开发"
   },
   {
      id: 3,
      userName: "王五",
      password: "123456",
      remark: "产品经理，负责产品规划和需求分析，有着敏锐的市场洞察力",
      age: 30,
      price: 899.50,
      status: "0",
      orgId: "2-1",
      sex: "0",
      type: "3",
      hobbies: ["reading", "music", "travel"],
      isEnabled: false,
      birthday: "1993-11-08",
      createTime: "2024-01-10 11:20:00",
      workTime: "09:30:00",
      validPeriod: ["2024-01-10", "2024-06-30"],
      themeColor: "#e6a23c",
      rating: 4.2,
      region: ["beijing", "haidian"],
      customField: "产品专家",
      showExtendInfo: "N",
      extendInfo: ""
   },
   {
      id: 4,
      userName: "赵六",
      password: "123456",
      remark: "UI设计师，专注于用户体验设计和界面优化",
      age: 26,
      price: 399.00,
      status: "1",
      orgId: "2-2",
      sex: "0",
      type: "2",
      hobbies: ["sports"],
      isEnabled: true,
      birthday: "1997-05-18",
      createTime: "2024-01-12 08:30:00",
      workTime: "10:00:00",
      validPeriod: ["2024-01-12", "2024-11-30"],
      themeColor: "#f56c6c",
      rating: 3.8,
      region: ["shanghai", "xuhui"],
      customField: "设计师配置",
      showExtendInfo: "Y",
      extendInfo: "熟练掌握各种设计工具，有着独特的审美观念"
   },
   {
      id: 5,
      userName: "孙七",
      password: "123456",
      remark: "测试工程师，负责系统测试和质量保证工作",
      age: 25,
      price: 199.99,
      status: "0",
      orgId: "1-1",
      sex: "1",
      type: "1",
      hobbies: ["reading"],
      isEnabled: true,
      birthday: "1998-09-25",
      createTime: "2024-01-15 14:45:00",
      workTime: "09:00:00",
      validPeriod: ["2024-01-15", "2024-07-31"],
      themeColor: "#909399",
      rating: 4.0,
      region: ["beijing", "dongcheng"],
      customField: "测试专员",
      showExtendInfo: "N",
      extendInfo: ""
   },
   {
      id: 6,
      userName: "周八",
      password: "123456",
      remark: "运维工程师，负责服务器维护和系统部署，保障系统稳定运行",
      age: 29,
      price: 159.00,
      status: "0",
      orgId: "2-1",
      sex: "1",
      type: "1",
      hobbies: ["sports", "music"],
      isEnabled: true,
      birthday: "1994-12-03",
      createTime: "2024-01-18 09:10:00",
      workTime: "24:00:00",
      validPeriod: ["2024-01-18", "2025-01-18"],
      themeColor: "#606266",
      rating: 4.7,
      region: ["shanghai", "changning"],
      customField: "运维专家",
      showExtendInfo: "Y",
      extendInfo: "具备丰富的运维经验，能够处理各种突发情况"
   },
   {
      id: 7,
      userName: "吴九",
      password: "123456",
      remark: "项目经理，统筹项目进度和资源分配，确保项目按时交付",
      age: 35,
      price: 1299.00,
      status: "0",
      orgId: "1",
      sex: "0",
      type: "3",
      hobbies: ["travel", "reading"],
      isEnabled: true,
      birthday: "1988-04-12",
      createTime: "2024-01-20 16:00:00",
      workTime: "08:00:00",
      validPeriod: ["2024-01-20", "2024-12-31"],
      themeColor: "#409eff",
      rating: 4.9,
      region: ["beijing", "chaoyang"],
      customField: "高级管理",
      showExtendInfo: "Y",
      extendInfo: "拥有多年的项目管理经验，善于团队协作和沟通"
   },
   {
      id: 8,
      userName: "郑十",
      password: "123456",
      remark: "数据分析师，负责业务数据分析和报表制作",
      age: 27,
      price: 459.99,
      status: "1",
      orgId: "2-2",
      sex: "0",
      type: "2",
      hobbies: ["reading", "music"],
      isEnabled: false,
      birthday: "1996-08-30",
      createTime: "2024-01-22 13:25:00",
      workTime: "09:30:00",
      validPeriod: ["2024-01-22", "2024-08-31"],
      themeColor: "#67c23a",
      rating: 3.5,
      region: ["shanghai", "huangpu"],
      customField: "数据专家",
      showExtendInfo: "N",
      extendInfo: ""
   }
]);

// 下一个ID计数器
let nextId = 9;

// 模拟API接口函数
const mockListTemplate = (queryParams) => {
   return new Promise((resolve) => {
      setTimeout(() => {
         let filteredData = [...mockData.value];

         // 模拟搜索过滤 - 根据新的字段结构
         if (queryParams.userName) {
            filteredData = filteredData.filter(item =>
               item.userName.toLowerCase().includes(queryParams.userName.toLowerCase())
            );
         }
         if (queryParams.sex) {
            filteredData = filteredData.filter(item => item.sex === queryParams.sex);
         }
         if (queryParams.type) {
            filteredData = filteredData.filter(item => item.type === queryParams.type);
         }
         if (queryParams.status) {
            filteredData = filteredData.filter(item => item.status === queryParams.status);
         }
         if (queryParams.showExtendInfo) {
            filteredData = filteredData.filter(item => item.showExtendInfo === queryParams.showExtendInfo);
         }
         // 年龄范围搜索
         if (queryParams.ageMin) {
            filteredData = filteredData.filter(item => item.age >= queryParams.ageMin);
         }
         if (queryParams.ageMax) {
            filteredData = filteredData.filter(item => item.age <= queryParams.ageMax);
         }
         // 价格范围搜索
         if (queryParams.priceMin) {
            filteredData = filteredData.filter(item => item.price >= queryParams.priceMin);
         }
         if (queryParams.priceMax) {
            filteredData = filteredData.filter(item => item.price <= queryParams.priceMax);
         }
         // 创建时间范围搜索
         if (queryParams.createTimeStart && queryParams.createTimeEnd) {
            filteredData = filteredData.filter(item => {
               const itemTime = new Date(item.createTime);
               const startTime = new Date(queryParams.createTimeStart);
               const endTime = new Date(queryParams.createTimeEnd);
               return itemTime >= startTime && itemTime <= endTime;
            });
         }

         // 模拟分页
         const pageNum = queryParams.pageNum || 1;
         const pageSize = queryParams.pageSize || 10;
         const start = (pageNum - 1) * pageSize;
         const end = start + pageSize;
         const pageData = filteredData.slice(start, end);

         resolve({
            rows: pageData,
            total: filteredData.length
         });
      }, 300); // 模拟网络延迟
   });
};

const mockGetTemplate = (id) => {
   return new Promise((resolve) => {
      setTimeout(() => {
         const item = mockData.value.find(item => item.id == id);
         resolve({
            data: item || {}
         });
      }, 200);
   });
};

const mockAddTemplate = (data) => {
   return new Promise((resolve) => {
      setTimeout(() => {
         const newItem = {
            ...data,
            id: nextId++,
            createTime: new Date().toLocaleString('zh-CN')
         };
         mockData.value.unshift(newItem);
         resolve({ data: newItem });
      }, 500);
   });
};

const mockUpdateTemplate = (data) => {
   return new Promise((resolve) => {
      setTimeout(() => {
         const index = mockData.value.findIndex(item => item.id == data.id);
         if (index > -1) {
            mockData.value[index] = {
               ...mockData.value[index],
               ...data,
               updateTime: new Date().toLocaleString('zh-CN')
            };
         }
         resolve({ data: mockData.value[index] });
      }, 500);
   });
};

const mockDelTemplate = (id) => {
   return new Promise((resolve) => {
      setTimeout(() => {
         const index = mockData.value.findIndex(item => item.id == id);
         if (index > -1) {
            mockData.value.splice(index, 1);
         }
         resolve({ success: true });
      }, 300);
   });
};

const mockChangeTemplateStatus = (id, status) => {
   return new Promise((resolve) => {
      setTimeout(() => {
         const item = mockData.value.find(item => item.id == id);
         if (item) {
            item.status = status;
            item.updateTime = new Date().toLocaleString('zh-CN');
         }
         resolve({ success: true });
      }, 300);
   });
};

// 初始化时获取数据
onMounted(() => {
   // 确保表格数据为空数组，避免undefined问题
   tableData.value = [];
   initializeConfig();
});

// 初始化配置
const initializeConfig = async () => {
   try {
      // 获取基础配置
      const baseOption = createTemplateTableOption(proxy);

      // 使用工具类获取合并后的配置
      const mergedConfig = await getCoSyncColumn({
         baseOption,
         proxy
      });

      // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
      const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

      // 设置表格和搜索配置
      tableColumns.value = extractedTableColumns;
      searchableColumns.value = searchColumns;

      // 设置表单字段配置
      formFields.value = extractedFormFields;

      // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
      formOption.value = {
         ...formOption.value, // 保留默认配置
         ...formOptions       // 使用从配置文件中提取的完整选项
      };

      isTableReady.value = true;

      // 加载表格数据
      loadData();
   } catch (error) {
      isTableReady.value = false;
      console.error('初始化配置失败:', error);
   }
};

// 加载表格数据
const loadData = () => {
   tableLoading.value = true;

   const queryParams = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...searchParams.value
   };

   mockListTemplate(queryParams).then(response => {
      // 确保数据完整性，过滤掉无效数据
      const validData = (response.rows || []).filter(item =>
         item && item.id && item.userName
      );

      tableData.value = validData;
      total.value = response.total || 0;

      // 更新TableList组件的分页信息
      updateTablePagination();

      tableLoading.value = false;
   }).catch(error => {
      console.error('加载用户列表失败:', error);
      tableData.value = [];
      total.value = 0;
      tableLoading.value = false;
   });
};

// 更新表格分页信息
const updateTablePagination = () => {
   if (tableListRef.value && tableListRef.value.page) {
      tableListRef.value.page.total = total.value;
      tableListRef.value.page.currentPage = currentPage.value;
      tableListRef.value.page.pageSize = pageSize.value;
   }
};

// 搜索处理
const handleSearch = (params) => {
   searchParams.value = params;
   currentPage.value = 1;
   loadData();
};

// 重置搜索
const resetSearch = () => {
   searchParams.value = {};
   currentPage.value = 1;
   loadData();
};

// 分页处理
const handleCurrentChange = (page) => {
   currentPage.value = page;
   loadData();
};

const handleSizeChange = (size) => {
   pageSize.value = size;
   currentPage.value = 1;
   loadData();
};

// 状态修改
const handleStatusChange = (row, newStatus) => {
   // 数据验证 - 确保必要的数据存在
   if (!row || !row.id || !row.userName) {
      return;
   }

   // 如果状态没有实际改变，不处理
   if (row.status === newStatus) {
      return;
   }

   let text = newStatus === "0" ? "启用" : "禁用";
   proxy.$modal.confirm('确认要"' + text + '""' + row.userName + '"吗?').then(function () {
      return mockChangeTemplateStatus(row.id, newStatus);
   }).then(() => {
      proxy.$modal.msgSuccess(text + "成功");
      // 确保状态更新成功
      row.status = newStatus;
   }).catch(function () {
      // 恢复原状态
      row.status = row.status === "0" ? "1" : "0";
   });
};

// 新增
const handleAdd = () => {
   templateFormDialogRef.value.openDialog('add', '新增用户');
};

// 编辑
const handleEdit = (row) => {
   mockGetTemplate(row.id).then(response => {
      templateFormDialogRef.value.openDialog('edit', '修改用户', response.data);
   });
};

// 查看
const handleView = (row) => {
   mockGetTemplate(row.id).then(response => {
      templateFormDialogRef.value.openDialog('view', '用户详细', response.data);
   });
};

// 复制
const handleCopy = (row) => {
   mockGetTemplate(row.id).then(response => {
      const copyData = { ...response.data };
      delete copyData.id;
      copyData.userName = copyData.userName + '(副本)';
      copyData.password = '123456'; // 重置密码
      copyData.createTime = ''; // 清空创建时间，让系统自动生成
      templateFormDialogRef.value.openDialog('add', '复制用户', copyData);
   });
};

// 删除
const handleDelete = (row) => {
   const ids = row.id;
   proxy.$modal.confirm('是否确认删除编号为"' + ids + '"的数据项?').then(function () {
      return mockDelTemplate(ids);
   }).then(() => {
      loadData();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
};

// 导出
const handleExport = () => {
   proxy.$modal.msgSuccess("导出功能演示 - 已生成模拟数据文件");
};

// 导入
const handleImport = () => {
   proxy.$modal.msgInfo("导入功能演示中...");
};

// 处理表单提交
const handleFormSubmit = ({ type, data }) => {
   if (type === 'add') {
      mockAddTemplate(data).then(response => {
         proxy.$modal.msgSuccess("新增成功");
         templateFormDialogRef.value.onSubmitSuccess();
         loadData();
      }).catch(() => {
         templateFormDialogRef.value.onSubmitError();
      });
   } else if (type === 'edit') {
      mockUpdateTemplate(data).then(response => {
         proxy.$modal.msgSuccess("修改成功");
         templateFormDialogRef.value.onSubmitSuccess();
         loadData();
      }).catch(() => {
         templateFormDialogRef.value.onSubmitError();
      });
   }
};

// 处理表单取消
const handleFormCancel = () => {
   // 表单取消时的处理逻辑
};
</script>

<style lang="scss" scoped>
.template-container {
   width: 100%;
   overflow-x: hidden !important; // 防止页面出现横向滚动条
   box-sizing: border-box;

   // 公用按钮样式已移至 element-ui.scss

   .loading-placeholder {
      height: 400px;
      display: flex;
      align-items: center;
      justify-content: center;
   }


   // 价格样式已移至 element-ui.scss
}
</style>