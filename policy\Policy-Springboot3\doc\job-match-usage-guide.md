# 招聘匹配系统使用指南

## 系统概述

本系统提供了一个简化的招聘信息与零工匹配功能，通过智能算法为招聘方推荐合适的零工，为零工推荐合适的工作机会。

## 主要功能

### 1. 前端功能

#### 人才特长页面 (`talentSpecial.html`)
- **智能匹配模式**: 点击"开启智能匹配"按钮，系统会根据匹配度对招聘信息进行排序
- **招聘信息展示**: 以卡片形式展示招聘信息，包含职位、薪资、地点等关键信息
- **匹配零工**: 点击"匹配零工"按钮，为特定招聘信息寻找合适的零工
- **查看匹配**: 点击"查看匹配"按钮，查看详细的匹配原因和评分
- **筛选功能**: 支持按工作类型、地点、薪资范围筛选招聘信息

#### 页面特色
- **匹配度显示**: 开启智能匹配后，高匹配度的招聘信息会显示绿色边框和匹配度徽章
- **实时匹配**: 点击匹配按钮后，实时显示匹配的零工信息和匹配原因
- **响应式设计**: 支持不同屏幕尺寸的设备访问

### 2. 后端API

#### 公开匹配接口 (`PublicJobMatchController`)
```
GET /public/job/postings/{jobId}/match-workers
```
- 为指定招聘信息匹配零工
- 返回匹配度评分和匹配原因

```
GET /public/job/quick-match
```
- 快速匹配接口，支持按条件快速查找
- 参数: jobType, location, skills, limit

#### 简化匹配接口 (`SimpleJobMatchController`)
```
GET /public/job/simple/postings/{jobId}/match-workers
```
- 使用简化算法进行匹配
- 更快的响应速度，更直观的匹配结果

```
GET /public/job/simple/workers/{workerId}/match-jobs
```
- 为零工匹配合适的招聘信息

## 匹配算法说明

### 匹配因素及权重

1. **地理位置匹配 (40%)**
   - 完全匹配: 100%
   - 工作地点偏好匹配: 90%
   - 同城匹配: 70%
   - 同区匹配: 60%

2. **工作类型匹配 (25%)**
   - 完全匹配: 100%
   - 兼容匹配 (兼职↔临时工): 80%
   - 默认匹配: 30%

3. **技能匹配 (20%)**
   - 基于关键词匹配
   - 计算匹配技能数量占比

4. **薪资匹配 (15%)**
   - 基于薪资范围重叠度计算
   - 重叠范围越大，匹配度越高

### 匹配评分计算

最终匹配度 = (地理位置得分 × 0.4) + (工作类型得分 × 0.25) + (技能得分 × 0.2) + (薪资得分 × 0.15)

## 快速开始

### 1. 准备测试数据

执行SQL脚本插入测试数据：
```sql
-- 执行测试数据脚本
source Policy-Springboot3/doc/sql/job_match_test_data.sql
```

### 2. 启动后端服务

```bash
cd Policy-Springboot3
mvn spring-boot:run
```

### 3. 访问前端页面

打开浏览器访问：
```
http://localhost/web-site/web-html/telent/talent/talentSpecial.html
```

### 4. 测试功能

1. **查看招聘信息**: 页面加载后会显示所有已发布的招聘信息
2. **开启智能匹配**: 点击"开启智能匹配"按钮，查看匹配度排序
3. **匹配零工**: 点击任意招聘卡片的"匹配零工"按钮
4. **查看匹配详情**: 点击"查看匹配"按钮了解匹配原因

## API测试示例

### 1. 测试招聘信息匹配零工

```bash
curl -X GET "http://localhost:80/sux-admin/public/job/simple/postings/1/match-workers?limit=5"
```

响应示例：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "worker": {
        "workerId": 1,
        "realName": "张三",
        "skills": "Java,Spring Boot,MySQL",
        "currentLocation": "西宁市市南区"
      },
      "similarity": 0.85,
      "similarityPercentage": 85,
      "matchReasons": ["地点匹配度高", "技能匹配", "薪资期望匹配"]
    }
  ]
}
```

### 2. 快速匹配测试

```bash
curl -X GET "http://localhost:80/sux-admin/public/job/quick-match?jobType=兼职&location=青岛&skills=JavaScript&limit=3"
```

## 数据创建建议

### 1. 招聘信息数据

创建招聘信息时，请注意：
- **地点标准化**: 使用"西宁市XX区"格式
- **技能规范化**: 使用逗号分隔的标准技能名称
- **薪资合理化**: 确保最小值小于最大值
- **状态设置**: 设置为'published'才能被匹配

### 2. 零工信息数据

创建零工信息时，请注意：
- **状态设置**: 设置为'active'才能参与匹配
- **技能对应**: 技能名称要与招聘信息中的技能对应
- **地点一致**: 使用与招聘信息相同的地点格式
- **薪资期望**: 设置合理的薪资期望范围

## 常见问题

### Q1: 为什么没有匹配结果？
A: 检查以下几点：
- 零工状态是否为'active'
- 招聘信息状态是否为'published'
- 地点和技能数据格式是否标准化
- 薪资范围是否有重叠

### Q2: 匹配度为什么很低？
A: 匹配度低可能因为：
- 地理位置差距较大
- 技能不匹配
- 薪资期望差距过大
- 工作类型不符合

### Q3: 前端页面显示异常？
A: 请检查：
- 浏览器控制台是否有错误信息
- 后端API是否正常响应
- 网络连接是否正常

### Q4: 如何提高匹配精度？
A: 建议：
- 完善招聘信息和零工信息的详细描述
- 使用标准化的技能关键词
- 设置合理的薪资范围
- 准确填写地理位置信息

## 扩展功能

系统支持进一步扩展：
- 添加更多匹配因素（如工作经验、学历等）
- 实现机器学习算法优化匹配精度
- 添加用户反馈机制改进算法
- 支持更复杂的地理位置匹配（如距离计算）

## 技术支持

如遇到技术问题，请：
1. 查看浏览器控制台错误信息
2. 检查后端日志
3. 验证数据库连接和数据完整性
4. 确认API接口返回格式正确
