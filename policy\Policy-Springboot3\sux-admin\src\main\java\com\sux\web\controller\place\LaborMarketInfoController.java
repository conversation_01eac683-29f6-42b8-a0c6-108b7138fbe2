package com.sux.web.controller.place;

import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.sux.common.annotation.Log;
import com.sux.common.annotation.Anonymous;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.enums.BusinessType;
import com.sux.system.domain.LaborMarketInfo;
import com.sux.system.service.ILaborMarketInfoService;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.common.core.page.TableDataInfo;

/**
 * 零工市场基础信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/place/market")
public class LaborMarketInfoController extends BaseController
{
    @Autowired
    private ILaborMarketInfoService laborMarketInfoService;

    /**
     * 查询零工市场基础信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LaborMarketInfo laborMarketInfo)
    {
        startPage();
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoList(laborMarketInfo);
        return getDataTable(list);
    }

    /**
     * 查询推荐零工市场信息列表
     */
    @GetMapping("/featured")
    public TableDataInfo featuredList(LaborMarketInfo laborMarketInfo)
    {
        startPage();
        List<LaborMarketInfo> list = laborMarketInfoService.selectFeaturedLaborMarketInfoList(laborMarketInfo);
        return getDataTable(list);
    }

    /**
     * 查询活跃零工市场信息列表
     */
    @GetMapping("/active")
    public TableDataInfo activeList(LaborMarketInfo laborMarketInfo)
    {
        startPage();
        List<LaborMarketInfo> list = laborMarketInfoService.selectActiveLaborMarketInfoList(laborMarketInfo);
        return getDataTable(list);
    }

    /**
     * 导出零工市场基础信息列表
     */
    @Log(title = "零工市场基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LaborMarketInfo laborMarketInfo)
    {
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoList(laborMarketInfo);
        ExcelUtil<LaborMarketInfo> util = new ExcelUtil<LaborMarketInfo>(LaborMarketInfo.class);
        util.exportExcel(response, list, "零工市场基础信息数据");
    }

    /**
     * 获取零工市场基础信息详细信息
     */
    @GetMapping(value = "/{marketId}")
    public AjaxResult getInfo(@PathVariable("marketId") Long marketId)
    {
        return success(laborMarketInfoService.selectLaborMarketInfoByMarketId(marketId));
    }

    /**
     * 获取零工市场详细信息（包含关联信息）
     */
    @GetMapping(value = "/detail/{marketId}")
    public AjaxResult getDetail(@PathVariable("marketId") Long marketId)
    {
        LaborMarketInfo laborMarketInfo = laborMarketInfoService.selectLaborMarketInfoDetailByMarketId(marketId);
        if (laborMarketInfo != null) {
            // 更新浏览次数
            laborMarketInfoService.updateLaborMarketInfoViewCount(marketId);
        }
        return success(laborMarketInfo);
    }

    /**
     * 新增零工市场基础信息
     */
    @Log(title = "零工市场基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LaborMarketInfo laborMarketInfo)
    {
        return toAjax(laborMarketInfoService.insertLaborMarketInfo(laborMarketInfo));
    }

    /**
     * 修改零工市场基础信息
     */
    @Log(title = "零工市场基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LaborMarketInfo laborMarketInfo)
    {
        return toAjax(laborMarketInfoService.updateLaborMarketInfo(laborMarketInfo));
    }

    /**
     * 删除零工市场基础信息
     */
    @Log(title = "零工市场基础信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{marketIds}")
    public AjaxResult remove(@PathVariable Long[] marketIds)
    {
        return toAjax(laborMarketInfoService.deleteLaborMarketInfoByMarketIds(marketIds));
    }

    /**
     * 根据市场类型查询零工市场信息列表
     */
    @GetMapping("/type/{marketType}")
    public AjaxResult getByType(@PathVariable String marketType)
    {
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoByType(marketType);
        return success(list);
    }

    /**
     * 根据区域代码查询零工市场信息列表
     */
    @GetMapping("/region/{regionCode}")
    public AjaxResult getByRegion(@PathVariable String regionCode)
    {
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoByRegion(regionCode);
        return success(list);
    }

    /**
     * 根据服务类别查询零工市场信息列表
     */
    @GetMapping("/service/{serviceCategory}")
    public AjaxResult getByServiceCategory(@PathVariable String serviceCategory)
    {
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoByServiceCategory(serviceCategory);
        return success(list);
    }

    /**
     * 查询零工市场统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        Map<String, Object> statistics = laborMarketInfoService.selectLaborMarketInfoStatistics();
        return success(statistics);
    }

    /**
     * 根据关键词搜索零工市场信息
     */
    @GetMapping("/search")
    public TableDataInfo search(@RequestParam String keyword)
    {
        startPage();
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoByKeyword(keyword);
        return getDataTable(list);
    }

    /**
     * 获取所有市场类型列表
     */
    @GetMapping("/types")
    public AjaxResult getAllMarketTypes()
    {
        List<String> types = laborMarketInfoService.selectAllMarketTypes();
        return success(types);
    }

    /**
     * 获取所有区域列表
     */
    @GetMapping("/regions")
    public AjaxResult getAllRegions()
    {
        List<Map<String, String>> regions = laborMarketInfoService.selectAllRegions();
        return success(regions);
    }

    /**
     * 获取所有服务类别列表
     */
    @GetMapping("/services")
    public AjaxResult getAllServiceCategories()
    {
        List<String> services = laborMarketInfoService.selectAllServiceCategories();
        return success(services);
    }

    /**
     * 根据容量范围查询零工市场信息
     */
    @GetMapping("/capacity")
    public AjaxResult getByCapacityRange(@RequestParam(required = false) Integer minCapacity,
                                        @RequestParam(required = false) Integer maxCapacity)
    {
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoByCapacityRange(minCapacity, maxCapacity);
        return success(list);
    }

    /**
     * 根据费用范围查询零工市场信息
     */
    @GetMapping("/fee")
    public AjaxResult getByFeeRange(@RequestParam(required = false) java.math.BigDecimal minFee,
                                   @RequestParam(required = false) java.math.BigDecimal maxFee)
    {
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoByFeeRange(minFee, maxFee);
        return success(list);
    }

    /**
     * 查询高需求零工市场（根据日均用工需求排序）
     */
    @GetMapping("/high-demand")
    public AjaxResult getHighDemand(@RequestParam(defaultValue = "10") Integer limit)
    {
        List<LaborMarketInfo> list = laborMarketInfoService.selectHighDemandLaborMarketInfo(limit);
        return success(list);
    }

    // ==================== 公开接口（无需登录） ====================

    /**
     * 公开查询零工市场信息列表（无需登录）
     */
    @Anonymous
    @GetMapping("/public/list")
    public TableDataInfo publicList(LaborMarketInfo laborMarketInfo)
    {
        startPage();
        // 只查询活跃状态的零工市场
        laborMarketInfo.setStatus("active");
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoList(laborMarketInfo);
        return getDataTable(list);
    }

    /**
     * 公开查询推荐零工市场信息列表（无需登录）
     */
    @Anonymous
    @GetMapping("/public/featured")
    public AjaxResult publicFeaturedList(@RequestParam(defaultValue = "10") Integer limit)
    {
        LaborMarketInfo laborMarketInfo = new LaborMarketInfo();
        laborMarketInfo.setStatus("active");
        laborMarketInfo.setIsFeatured(1);
        List<LaborMarketInfo> list = laborMarketInfoService.selectFeaturedLaborMarketInfoList(laborMarketInfo);
        // 限制返回数量
        if (list.size() > limit) {
            list = list.subList(0, limit);
        }
        return success(list);
    }

    /**
     * 公开查询活跃零工市场信息列表（无需登录）
     */
    @Anonymous
    @GetMapping("/public/active")
    public AjaxResult publicActiveList(@RequestParam(defaultValue = "10") Integer limit)
    {
        LaborMarketInfo laborMarketInfo = new LaborMarketInfo();
        List<LaborMarketInfo> list = laborMarketInfoService.selectActiveLaborMarketInfoList(laborMarketInfo);
        // 限制返回数量
        if (list.size() > limit) {
            list = list.subList(0, limit);
        }
        return success(list);
    }

    /**
     * 公开根据关键词搜索零工市场信息（无需登录）
     */
    @Anonymous
    @GetMapping("/public/search")
    public TableDataInfo publicSearch(@RequestParam String keyword)
    {
        startPage();
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoByKeyword(keyword);
        // 过滤只返回活跃状态的市场
        list = list.stream()
                .filter(market -> "active".equals(market.getStatus()))
                .collect(java.util.stream.Collectors.toList());
        return getDataTable(list);
    }

    /**
     * 公开获取零工市场详细信息（无需登录）
     */
    @Anonymous
    @GetMapping("/public/{marketId}")
    public AjaxResult publicGetInfo(@PathVariable("marketId") Long marketId)
    {
        LaborMarketInfo laborMarketInfo = laborMarketInfoService.selectLaborMarketInfoDetailByMarketId(marketId);
        if (laborMarketInfo != null && "active".equals(laborMarketInfo.getStatus())) {
            // 增加浏览次数
            laborMarketInfoService.updateLaborMarketInfoViewCount(marketId);
            return success(laborMarketInfo);
        }
        return error("零工市场信息不存在或已下线");
    }

    /**
     * 公开获取零工市场统计信息（无需登录）
     */
    @Anonymous
    @GetMapping("/public/statistics")
    public AjaxResult publicGetStatistics()
    {
        Map<String, Object> statistics = laborMarketInfoService.selectLaborMarketInfoStatistics();
        return success(statistics);
    }
}
