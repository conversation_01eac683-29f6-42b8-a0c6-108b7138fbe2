package com.sux.framework.config;

import java.util.Properties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import static com.google.code.kaptcha.Constants.*;

/**
 * 验证码配置
 * 
 * <AUTHOR>
 */
@Configuration
public class CaptchaConfig
{
    @Bean(name = "captchaProducer")
    public DefaultKaptcha getKaptchaBean()
    {
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        Properties properties = new Properties();
        // 去掉边框，让验证码更清晰
        properties.setProperty(KAPTCHA_BORDER, "no");
        // 验证码文本字符颜色 使用深色增加对比度
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_COLOR, "black");
        // 验证码图片宽度 增加宽度以提高清晰度
        properties.setProperty(KAPTCHA_IMAGE_WIDTH, "200");
        // 验证码图片高度 增加高度以提高清晰度
        properties.setProperty(KAPTCHA_IMAGE_HEIGHT, "80");
        // 验证码文本字符大小 增大字体
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_SIZE, "50");
        // KAPTCHA_SESSION_KEY
        properties.setProperty(KAPTCHA_SESSION_CONFIG_KEY, "kaptchaCode");
        // 验证码文本字符长度
        properties.setProperty(KAPTCHA_TEXTPRODUCER_CHAR_LENGTH, "4");
        // 验证码文本字体样式 使用最清晰的字体
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_NAMES, "Arial");
        // 文字间距 增加间距避免重叠
        properties.setProperty(KAPTCHA_TEXTPRODUCER_CHAR_SPACE, "10");
        // 背景颜色 使用纯白色背景
        properties.setProperty(KAPTCHA_BACKGROUND_CLR_FROM, "white");
        properties.setProperty(KAPTCHA_BACKGROUND_CLR_TO, "white");
        // 完全去掉噪声线条
        properties.setProperty(KAPTCHA_NOISE_IMPL, "com.google.code.kaptcha.impl.NoNoise");
        // 字符串来源 使用简单易识别的字符
        properties.setProperty(KAPTCHA_TEXTPRODUCER_CHAR_STRING, "23456789ABCDEFGHJKLMNPQRSTUVWXYZ");
        Config config = new Config(properties);
        defaultKaptcha.setConfig(config);
        return defaultKaptcha;
    }

    @Bean(name = "captchaProducerMath")
    public DefaultKaptcha getKaptchaBeanMath()
    {
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        Properties properties = new Properties();
        // 去掉边框，让验证码更清晰
        properties.setProperty(KAPTCHA_BORDER, "no");
        // 验证码文本字符颜色 使用深蓝色增加对比度
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_COLOR, "darkBlue");
        // 验证码图片宽度 增加宽度以提高清晰度
        properties.setProperty(KAPTCHA_IMAGE_WIDTH, "240");
        // 验证码图片高度 增加高度以提高清晰度
        properties.setProperty(KAPTCHA_IMAGE_HEIGHT, "80");
        // 验证码文本字符大小 增大字体
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_SIZE, "45");
        // KAPTCHA_SESSION_KEY
        properties.setProperty(KAPTCHA_SESSION_CONFIG_KEY, "kaptchaCodeMath");
        // 验证码文本生成器
        properties.setProperty(KAPTCHA_TEXTPRODUCER_IMPL, "com.sux.framework.config.KaptchaTextCreator");
        // 验证码文本字符间距 增加间距
        properties.setProperty(KAPTCHA_TEXTPRODUCER_CHAR_SPACE, "8");
        // 验证码文本字符长度
        properties.setProperty(KAPTCHA_TEXTPRODUCER_CHAR_LENGTH, "6");
        // 验证码文本字体样式 使用最清晰的字体
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_NAMES, "Arial");
        // 背景颜色 使用纯白色背景
        properties.setProperty(KAPTCHA_BACKGROUND_CLR_FROM, "white");
        properties.setProperty(KAPTCHA_BACKGROUND_CLR_TO, "white");
        // 完全去掉噪声线条
        properties.setProperty(KAPTCHA_NOISE_IMPL, "com.google.code.kaptcha.impl.NoNoise");
        Config config = new Config(properties);
        defaultKaptcha.setConfig(config);
        return defaultKaptcha;
    }
}
