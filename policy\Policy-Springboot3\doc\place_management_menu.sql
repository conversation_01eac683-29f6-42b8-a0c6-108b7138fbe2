-- 场地管理菜单SQL
-- 创建场地管理主菜单和子菜单

-- 1. 创建场地管理主菜单
INSERT INTO `sys_menu` VALUES (4000, '场地管理', 0, 3, 'place', NULL, '', '', 1, 0, 'M', '0', '0', '', 'build', 1, NOW(), 1, NULL, '场地管理目录');

-- 2. 创建用工信息管理菜单
INSERT INTO `sys_menu` VALUES (4001, '用工信息', 4000, 1, 'employment', 'place/employment/index', '', '', 1, 0, 'C', '0', '0', 'place:employment:list', 'peoples', 1, NOW(), 1, NULL, '用工信息管理菜单');

-- 3. 创建场地信息管理菜单
INSERT INTO `sys_menu` VALUES (4002, '场地信息', 4000, 2, 'info', 'place/info/index', '', '', 1, 0, 'C', '0', '0', 'place:info:list', 'component', 1, NOW(), 1, NULL, '场地信息管理菜单');

-- 4. 创建劳务市场管理菜单
INSERT INTO `sys_menu` VALUES (4003, '劳务市场', 4000, 3, 'market', 'place/market/index', '', '', 1, 0, 'C', '0', '0', 'place:market:list', 'shopping', 1, NOW(), 1, NULL, '劳务市场管理菜单');

-- ==================== 用工信息管理权限按钮 ====================
-- 用工信息查询
INSERT INTO `sys_menu` VALUES (4101, '用工信息查询', 4001, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:query', '#', 1, NOW(), 1, NULL, '');

-- 用工信息新增
INSERT INTO `sys_menu` VALUES (4102, '用工信息新增', 4001, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:add', '#', 1, NOW(), 1, NULL, '');

-- 用工信息修改
INSERT INTO `sys_menu` VALUES (4103, '用工信息修改', 4001, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:edit', '#', 1, NOW(), 1, NULL, '');

-- 用工信息删除
INSERT INTO `sys_menu` VALUES (4104, '用工信息删除', 4001, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:remove', '#', 1, NOW(), 1, NULL, '');

-- 用工信息导出
INSERT INTO `sys_menu` VALUES (4105, '用工信息导出', 4001, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:export', '#', 1, NOW(), 1, NULL, '');

-- 用工信息审核
INSERT INTO `sys_menu` VALUES (4106, '用工信息审核', 4001, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:review', '#', 1, NOW(), 1, NULL, '');

-- 用工信息发布
INSERT INTO `sys_menu` VALUES (4107, '用工信息发布', 4001, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:publish', '#', 1, NOW(), 1, NULL, '');

-- 用工信息统计
INSERT INTO `sys_menu` VALUES (4108, '用工信息统计', 4001, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:statistics', '#', 1, NOW(), 1, NULL, '');

-- ==================== 场地信息管理权限按钮 ====================
-- 场地信息查询
INSERT INTO `sys_menu` VALUES (4201, '场地信息查询', 4002, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:query', '#', 1, NOW(), 1, NULL, '');

-- 场地信息新增
INSERT INTO `sys_menu` VALUES (4202, '场地信息新增', 4002, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:add', '#', 1, NOW(), 1, NULL, '');

-- 场地信息修改
INSERT INTO `sys_menu` VALUES (4203, '场地信息修改', 4002, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:edit', '#', 1, NOW(), 1, NULL, '');

-- 场地信息删除
INSERT INTO `sys_menu` VALUES (4204, '场地信息删除', 4002, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:remove', '#', 1, NOW(), 1, NULL, '');

-- 场地信息导出
INSERT INTO `sys_menu` VALUES (4205, '场地信息导出', 4002, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:export', '#', 1, NOW(), 1, NULL, '');

-- 场地信息审核
INSERT INTO `sys_menu` VALUES (4206, '场地信息审核', 4002, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:review', '#', 1, NOW(), 1, NULL, '');

-- 场地信息统计
INSERT INTO `sys_menu` VALUES (4207, '场地信息统计', 4002, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:statistics', '#', 1, NOW(), 1, NULL, '');

-- ==================== 劳务市场管理权限按钮 ====================
-- 劳务市场查询
INSERT INTO `sys_menu` VALUES (4301, '劳务市场查询', 4003, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:query', '#', 1, NOW(), 1, NULL, '');

-- 劳务市场新增
INSERT INTO `sys_menu` VALUES (4302, '劳务市场新增', 4003, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:add', '#', 1, NOW(), 1, NULL, '');

-- 劳务市场修改
INSERT INTO `sys_menu` VALUES (4303, '劳务市场修改', 4003, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:edit', '#', 1, NOW(), 1, NULL, '');

-- 劳务市场删除
INSERT INTO `sys_menu` VALUES (4304, '劳务市场删除', 4003, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:remove', '#', 1, NOW(), 1, NULL, '');

-- 劳务市场导出
INSERT INTO `sys_menu` VALUES (4305, '劳务市场导出', 4003, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:export', '#', 1, NOW(), 1, NULL, '');

-- 劳务市场审核
INSERT INTO `sys_menu` VALUES (4306, '劳务市场审核', 4003, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:review', '#', 1, NOW(), 1, NULL, '');

-- 劳务市场统计
INSERT INTO `sys_menu` VALUES (4307, '劳务市场统计', 4003, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'place:market:statistics', '#', 1, NOW(), 1, NULL, '');

-- ==================== 查询菜单验证SQL ====================
-- 查询场地管理相关菜单
-- SELECT menu_id, menu_name, parent_id, path, component, perms, icon, order_num 
-- FROM sys_menu 
-- WHERE menu_id BETWEEN 4000 AND 4999 
-- ORDER BY menu_id;

-- ==================== 菜单说明 ====================
-- 菜单类型说明：
-- M: 目录菜单
-- C: 菜单项
-- F: 按钮权限

-- 字段说明：
-- menu_id: 菜单ID
-- menu_name: 菜单名称
-- parent_id: 父菜单ID (0表示顶级菜单)
-- order_num: 显示顺序
-- path: 路由地址
-- component: 组件路径
-- is_frame: 是否外链 (0是 1否)
-- is_cache: 是否缓存 (0缓存 1不缓存)
-- menu_type: 菜单类型 (M目录 C菜单 F按钮)
-- visible: 显示状态 (0显示 1隐藏)
-- status: 菜单状态 (0正常 1停用)
-- perms: 权限标识
-- icon: 菜单图标
-- create_id: 创建者ID
-- create_time: 创建时间
-- update_id: 更新者ID
-- update_time: 更新时间
-- remark: 备注
