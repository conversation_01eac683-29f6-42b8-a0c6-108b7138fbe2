<template>
   <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="formOption.dialogWidth" destroy-on-close
      :close-on-click-modal="false" :fullscreen="isFullscreen" @closed="handleDialogClosed" @open="handleDialogOpened"
      class="custom-dialog">
      <div class="dialog-content view-mode" :style="dialogContentStyle">
         <ViewList v-model="formData" :fields="currentFormFields" :labelWidth="formOption.labelWidth || '120px'">
         </ViewList>
      </div>
      <template #footer>
         <span class="dialog-footer">
            <el-button class="custom-btn" @click="toggleFullscreen">
               {{ isFullscreen ? '退出全屏' : '全屏显示' }}
            </el-button>
            <el-button class="custom-btn" @click="handleClose">
               关 闭
            </el-button>
         </span>
      </template>
   </el-dialog>
</template>

<script setup name="JobLogFormDialog">
import { ref, reactive, computed, getCurrentInstance } from 'vue';
import ViewList from '@/components/ViewList/index.vue';

const { proxy } = getCurrentInstance();
const { sys_common_status, sys_job_group } = proxy.useDict("sys_common_status", "sys_job_group");

// Props
const props = defineProps({
   formFields: {
      type: Array,
      default: () => []
   },
   formOption: {
      type: Object,
      default: () => ({
         dialogWidth: '800px',
         dialogHeight: '70vh'
      })
   }
});

// Emits
const emit = defineEmits([
   'submit',
   'cancel'
]);

// 表单相关
const dialogVisible = ref(false);
const dialogType = ref('view'); // 只支持查看模式
const dialogTitle = ref('调度日志详细');
const formData = ref({});

// 控制弹窗全屏状态
const isFullscreen = ref(false);

// 根据表单类型动态筛选字段 - 只显示查看字段
const currentFormFields = computed(() => {
   if (!props.formFields.length) return [];
   return props.formFields.filter(field => field.viewDisplay !== false);
});

// 弹窗内容样式计算
const dialogContentStyle = computed(() => {
   const baseStyle = {
      overflow: 'visible',
      padding: '20px 10px',
      overflowX: 'hidden',
   };

   if (isFullscreen.value) {
      return {
         ...baseStyle,
         maxHeight: 'calc(100vh - 180px)',
         overflowY: 'auto',
         overflowX: 'hidden',
      };
   }

   return {
      ...baseStyle,
      maxHeight: props.formOption.dialogHeight || '70vh',
      overflowY: 'auto',
      overflowX: 'hidden',
      minHeight: 'auto',
   };
});

// 切换全屏状态
const toggleFullscreen = () => {
   isFullscreen.value = !isFullscreen.value;
};

// 对外暴露的方法
const openDialog = (type, title, data = {}) => {
   dialogType.value = 'view'; // 强制为查看模式
   dialogTitle.value = title || '调度日志详细';

   // 深拷贝数据以避免修改原始数据
   formData.value = JSON.parse(JSON.stringify(data));

   dialogVisible.value = true;
};

const closeDialog = () => {
   dialogVisible.value = false;
};

// 处理关闭
const handleClose = () => {
   emit('cancel');
   closeDialog();
};

// 处理表单对话框打开
const handleDialogOpened = () => {
   // 对话框打开时的处理
};

// 处理表单对话框关闭
const handleDialogClosed = () => {
   // 清空表单数据
   formData.value = {};
   dialogType.value = 'view';
   dialogTitle.value = '调度日志详细';
   isFullscreen.value = false;
};

// 提交成功后的回调
const onSubmitSuccess = () => {
   closeDialog();
};

// 提交失败后的回调
const onSubmitError = () => {
   // 日志详情页面不涉及提交操作
};

// 暴露方法给父组件
defineExpose({
   openDialog,
   closeDialog,
   onSubmitSuccess,
   onSubmitError
});
</script>

<style lang="scss" scoped>
// 弹窗内容区域样式优化
.dialog-content {
   max-height: v-bind("dialogContentStyle.maxHeight");
   min-height: v-bind("dialogContentStyle.minHeight || 'auto'");
   overflow-y: v-bind("dialogContentStyle.overflowY");
   overflow-x: hidden !important;
   padding: v-bind("dialogContentStyle.padding");
   width: 100%;
   box-sizing: border-box;

   &.view-mode {
      padding: 16px 10px;
      width: 100%;
      box-sizing: border-box;
      overflow-x: hidden !important;

      :deep(.view-list) {
         width: 100%;
         box-sizing: border-box;

         .el-descriptions {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            overflow: hidden;
            background: #fff;

            .el-descriptions__header {
               background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
               padding: 16px 20px;
               border-bottom: 1px solid #e4e7ed;

               .el-descriptions__title {
                  font-weight: 600;
                  color: #495057;
                  font-size: 16px;
               }
            }

            .el-descriptions__body {
               padding: 20px;

               .el-descriptions__table {
                  .el-descriptions-item {
                     &:not(:last-child) {
                        border-bottom: 1px solid #f0f2f5;
                     }

                     .el-descriptions-item__cell {
                        padding: 12px 16px;
                        vertical-align: top;

                        &.el-descriptions-item__label {
                           background: #fafbfc;
                           color: #606266;
                           font-weight: 600;
                           word-wrap: break-word;
                           word-break: break-all;
                           width: 120px;
                           min-width: 120px;
                        }

                        &.el-descriptions-item__content {
                           color: #303133;
                           word-wrap: break-word;
                           word-break: break-all;
                           max-width: 100%;
                           overflow-wrap: break-word;
                           font-family: 'Courier New', monospace;
                           font-size: 13px;
                           line-height: 1.6;

                                                       // 特殊字段样式
                            &[data-field="invokeTarget"],
                            &[data-field="jobMessage"],
                            &[data-field="exceptionInfo"] {
                               background: #f8f9fa;
                               border: 1px solid #e4e7ed;
                               border-radius: 6px;
                               padding: 12px 16px;
                               font-family: 'Courier New', monospace;
                               white-space: pre-wrap;
                               max-height: 300px;
                               overflow-y: auto;
                               word-break: break-all;
                               line-height: 1.6;
                               
                               &::-webkit-scrollbar {
                                  width: 6px;
                               }
                               
                               &::-webkit-scrollbar-track {
                                  background: #f1f1f1;
                                  border-radius: 3px;
                               }
                               
                               &::-webkit-scrollbar-thumb {
                                  background: #c1c1c1;
                                  border-radius: 3px;
                                  
                                  &:hover {
                                     background: #a8a8a8;
                                  }
                               }
                            }
                            
                            // 日期时间字段特殊样式
                            &[data-field="createTime"] {
                               font-family: 'Courier New', monospace;
                               font-weight: 600;
                               color: #409eff;
                            }

                           // 状态标签样式
                           .el-tag {
                              border-radius: 12px;
                              font-size: 11px;
                              font-weight: 500;
                              padding: 2px 8px;

                              &.el-tag--success {
                                 background: linear-gradient(135deg, #48bb78 0%, #68d391 100%);
                                 border-color: #48bb78;
                                 color: white;
                              }

                              &.el-tag--danger {
                                 background: linear-gradient(135deg, #f56565 0%, #fc8181 100%);
                                 border-color: #f56565;
                                 color: white;
                              }

                              &.el-tag--warning {
                                 background: linear-gradient(135deg, #ed8936 0%, #f6ad55 100%);
                                 border-color: #ed8936;
                                 color: white;
                              }

                              &.el-tag--info {
                                 background: linear-gradient(135deg, #4299e1 0%, #63b3ed 100%);
                                 border-color: #4299e1;
                                 color: white;
                              }
                           }
                        }
                     }
                  }
               }
            }
         }
      }
   }
}

// 弹窗底部样式优化
:deep(.el-dialog__footer) {
   padding: 16px 24px 24px;
   border-top: 1px solid #f0f2f5;
   background: linear-gradient(180deg, #fafbfc 0%, #f8f9fa 100%);
   border-radius: 0 0 12px 12px;

   .dialog-footer {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 12px;
   }
}

// 弹窗主体样式优化
:deep(.el-dialog) {
   border-radius: 12px;
   box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
   overflow: hidden;

   .el-dialog__header {
      padding: 20px 24px 16px;
      border-bottom: 1px solid #f0f2f5;
      background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
      border-radius: 12px 12px 0 0;
      overflow: hidden;

      .el-dialog__title {
         color: #303133;
         font-weight: 600;
         font-size: 16px;
         white-space: nowrap;
         overflow: hidden;
         text-overflow: ellipsis;
      }
   }

   .el-dialog__body {
      padding: 0;
      background: #ffffff;
      overflow-x: hidden !important;
      width: 100%;
      box-sizing: border-box;
   }
}
</style>