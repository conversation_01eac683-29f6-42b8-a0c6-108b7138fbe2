-- 培训机构申请管理菜单SQL
-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训机构申请管理', '2000', '5', 'institution-application', 'order/application/institution-management', 1, 0, 'C', '0', '0', 'training:institution:application:list', 'peoples', 1, sysdate(), 1, sysdate(), '培训机构申请管理菜单');

-- 获取刚插入的菜单ID
SET @menu_id = LAST_INSERT_ID();

-- 按钮父菜单ID
-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训机构申请查询', @menu_id, '1',  '#', '', 1, 0, 'F', '0', '0', 'training:institution:application:query',        '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训机构申请新增', @menu_id, '2',  '#', '', 1, 0, 'F', '0', '0', 'training:institution:application:add',          '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训机构申请修改', @menu_id, '3',  '#', '', 1, 0, 'F', '0', '0', 'training:institution:application:edit',         '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训机构申请删除', @menu_id, '4',  '#', '', 1, 0, 'F', '0', '0', 'training:institution:application:remove',       '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训机构申请导出', @menu_id, '5',  '#', '', 1, 0, 'F', '0', '0', 'training:institution:application:export',       '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训机构申请审核', @menu_id, '6',  '#', '', 1, 0, 'F', '0', '0', 'training:institution:application:review',       '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训机构申请取消', @menu_id, '7',  '#', '', 1, 0, 'F', '0', '0', 'training:institution:application:cancel',       '#', 1, sysdate(), 1, sysdate(), '');

-- 添加公开的机构申请页面菜单（无需权限，所有人可访问）
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训机构申请', '2000', '6', 'institution-apply', 'order/application/institution-apply', 1, 0, 'C', '0', '0', '', 'user', 1, sysdate(), 1, sysdate(), '培训机构申请页面（公开访问）');

-- 注意：这里的parent_id需要根据实际的招聘管理菜单ID进行调整
-- 如果招聘管理菜单ID不是2000，请查询sys_menu表获取正确的ID并替换上面的2000

-- 查询招聘管理菜单ID的SQL（供参考）：
-- SELECT menu_id FROM sys_menu WHERE menu_name = '招聘管理' AND parent_id = 0;

-- 查询所有相关菜单的SQL（供参考）：
-- SELECT menu_id, menu_name, parent_id, path, component, perms FROM sys_menu WHERE menu_name LIKE '%培训%' OR menu_name LIKE '%机构%' ORDER BY menu_id;
