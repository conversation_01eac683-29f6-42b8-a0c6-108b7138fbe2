import request from '@/utils/request'

// 查询场地信息列表
export function listPlaceInfo(query) {
  return request({
    url: '/place/info/list',
    method: 'get',
    params: query
  })
}

// 查询场地信息详细
export function getPlaceInfo(placeId) {
  return request({
    url: '/place/info/' + placeId,
    method: 'get'
  })
}

// 查询场地详细信息（包含关联信息）
export function getPlaceInfoDetail(placeId) {
  return request({
    url: '/place/info/detail/' + placeId,
    method: 'get'
  })
}

// 新增场地信息
export function addPlaceInfo(data) {
  return request({
    url: '/place/info',
    method: 'post',
    data: data
  })
}

// 修改场地信息
export function updatePlaceInfo(data) {
  return request({
    url: '/place/info',
    method: 'put',
    data: data
  })
}

// 删除场地信息
export function delPlaceInfo(placeId) {
  return request({
    url: '/place/info/' + placeId,
    method: 'delete'
  })
}

// 查询推荐场地信息列表
export function listFeaturedPlaceInfo(query) {
  return request({
    url: '/place/info/featured',
    method: 'get',
    params: query
  })
}

// 查询活跃场地信息列表
export function listActivePlaceInfo(query) {
  return request({
    url: '/place/info/active',
    method: 'get',
    params: query
  })
}

// 根据场地类型查询场地信息列表
export function getPlaceInfoByType(placeType) {
  return request({
    url: '/place/info/type/' + placeType,
    method: 'get'
  })
}

// 根据区域代码查询场地信息列表
export function getPlaceInfoByRegion(regionCode) {
  return request({
    url: '/place/info/region/' + regionCode,
    method: 'get'
  })
}

// 根据场地等级查询场地信息列表
export function getPlaceInfoByLevel(placeLevel) {
  return request({
    url: '/place/info/level/' + placeLevel,
    method: 'get'
  })
}

// 根据行业方向查询场地信息列表
export function getPlaceInfoByIndustry(industryDirection) {
  return request({
    url: '/place/info/industry/' + industryDirection,
    method: 'get'
  })
}

// 根据运营模式查询场地信息列表
export function getPlaceInfoByOperationMode(operationMode) {
  return request({
    url: '/place/info/operation/' + operationMode,
    method: 'get'
  })
}

// 查询场地统计信息
export function getPlaceInfoStatistics() {
  return request({
    url: '/place/info/statistics',
    method: 'get'
  })
}

// 根据关键词搜索场地信息
export function searchPlaceInfo(keyword) {
  return request({
    url: '/place/info/search',
    method: 'get',
    params: { keyword }
  })
}

// 查询即将到期的招商场地
export function getPlaceInfoExpiringSoon(days) {
  return request({
    url: '/place/info/expiring',
    method: 'get',
    params: { days }
  })
}

// 获取所有场地类型列表
export function getAllPlaceTypes() {
  return request({
    url: '/place/info/types',
    method: 'get'
  })
}

// 获取所有场地等级列表
export function getAllPlaceLevels() {
  return request({
    url: '/place/info/levels',
    method: 'get'
  })
}

// 获取所有区域列表
export function getAllRegions() {
  return request({
    url: '/place/info/regions',
    method: 'get'
  })
}

// 获取所有行业方向列表
export function getAllIndustryDirections() {
  return request({
    url: '/place/info/industries',
    method: 'get'
  })
}

// 获取所有运营模式列表
export function getAllOperationModes() {
  return request({
    url: '/place/info/operations',
    method: 'get'
  })
}
