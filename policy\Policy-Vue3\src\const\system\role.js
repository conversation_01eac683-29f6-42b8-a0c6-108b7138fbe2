import { parseTime } from "@/utils/ruoyi";

// 数据权限选项
const dataScopeOptions = [
    {
        label: "全部数据权限",
        value: "1",
    },
    {
        label: "自定数据权限",
        value: "2",
    },
    {
        label: "部门数据权限",
        value: "3",
    },
    {
        label: "部门及以下数据权限",
        value: "4",
    },
    {
        label: "仅本人数据权限",
        value: "5",
    },
];

export const createRoleTableOption = (proxy) => {
    const {
        sys_normal_disable
    } = proxy.useDict("sys_normal_disable");

    return {
        dialogWidth: '800px',  // 弹窗宽度
        dialogHeight: '70vh',  // 弹窗内容区最大高度
        labelWidth: '100px',
        column: [
            // ==================== 基础信息分组 ====================
            {
                label: "基础信息",
                prop: "divider_basic_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true // 分隔线标识
            },
            {
                label: "角色编号",
                prop: "roleId",
                minWidth: 120,
                editDisplay: false,
                addDisplay: false,
                viewDisplay: true,
            },
            {
                label: "角色名称",
                prop: "roleName",
                search: true,
                rules: [{
                    required: true,
                    message: "角色名称不能为空",
                    trigger: "blur"
                }],
                span: 12,
                minWidth: 150,
            },
            {
                label: "权限字符",
                prop: "roleKey",
                search: true,
                rules: [{
                    required: true,
                    message: "权限字符不能为空",
                    trigger: "blur"
                }],
                span: 12,
                minWidth: 150,
                placeholder: "请输入权限字符",
                tooltip: "控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)"
            },
            {
                label: "角色顺序",
                prop: "roleSort",
                type: 'number',
                span: 12,
                minWidth: 100,
                rules: [{
                    required: true,
                    message: "角色顺序不能为空",
                    trigger: "blur"
                }],
                min: 0,
                controlsPosition: "right"
            },
            {
                label: "状态",
                prop: "status",
                type: 'radio',
                span: 12,
                minWidth: 100,
                search: true,
                dicData: sys_normal_disable,
                slot: true, // 表格中使用开关组件
            },
            {
                label: "创建时间",
                prop: "createTime",
                editDisplay: false,
                addDisplay: false,
                type: 'datetime',
                minWidth: 160,
                search: true,
                searchRange: true,
                formatter: (val, value, label) => parseTime(value, '{y}-{m}-{d} {h}:{i}:{s}'),
            },

            // ==================== 权限配置分组 ====================
            {
                label: "权限配置",
                prop: "divider_permission_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true, // 分隔线标识
                addDisplay: true,
                editDisplay: true,
                viewDisplay: true
            },
            {
                label: "菜单权限",
                prop: "menuIds",
                span: 24,
                formSlot: true, // 使用表单插槽自定义菜单权限树
                showColumn: false,
                addDisplay: true,
                editDisplay: true,
                viewDisplay: true
            },
            {
                label: "数据权限",
                prop: "dataScope",
                type: 'select',
                span: 24,
                dicData: dataScopeOptions,
                showColumn: false,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false
            },
            {
                label: "数据权限范围",
                prop: "deptIds",
                span: 24,
                formSlot: true, // 使用表单插槽自定义部门权限树
                showColumn: false,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false
            },

            // ==================== 详细信息分组 ====================
            {
                label: "详细信息",
                prop: "divider_detail_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true // 分隔线标识
            },
            {
                label: "备注",
                prop: "remark",
                type: 'textarea',
                minRows: 3,
                maxRows: 6,
                span: 24,
                showColumn: false,
                placeholder: "请输入内容"
            }
        ],
    };
} 