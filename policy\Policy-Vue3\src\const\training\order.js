import { parseTime } from "@/utils/ruoyi";

export const createTrainingOrderTableOption = (proxy) => {
    const {
        sys_yes_no
    } = proxy.useDict("sys_yes_no");

    // 培训类型选项
    const trainingTypeOptions = [
        { label: "技术培训", value: "技术培训" },
        { label: "管理培训", value: "管理培训" },
        { label: "职业技能", value: "职业技能" },
        { label: "安全培训", value: "安全培训" },
        { label: "合规培训", value: "合规培训" },
        { label: "其他", value: "其他" }
    ];

    // 培训分类选项
    const trainingCategoryOptions = [
        { label: "IT技能", value: "IT技能" },
        { label: "语言培训", value: "语言培训" },
        { label: "财务管理", value: "财务管理" },
        { label: "人力资源", value: "人力资源" },
        { label: "市场营销", value: "市场营销" },
        { label: "项目管理", value: "项目管理" },
        { label: "领导力", value: "领导力" },
        { label: "沟通技巧", value: "沟通技巧" },
        { label: "其他", value: "其他" }
    ];

    // 培训级别选项
    const trainingLevelOptions = [
        { label: "初级", value: "初级" },
        { label: "中级", value: "中级" },
        { label: "高级", value: "高级" },
        { label: "专家级", value: "专家级" }
    ];

    // 订单状态选项
    const orderStatusOptions = [
        { label: "草稿", value: "0", color: "info" },
        { label: "发布", value: "1", color: "success" },
        { label: "进行中", value: "2", color: "warning" },
        { label: "已完成", value: "3", color: "primary" },
        { label: "已取消", value: "4", color: "danger" }
    ];

    return {
        dialogWidth: '1000px',  // 弹窗宽度
        dialogHeight: '70vh',   // 弹窗内容区最大高度
        labelWidth: '120px',
        column: [
            // ==================== 基础信息分组 ====================
            {
                label: "基础信息",
                prop: "divider_basic_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true // 分隔线标识
            },
            {
                label: "订单标题",
                prop: "orderTitle",
                search: true,
                searchSpan: 12,
                minWidth: 200,
                rules: [
                    { required: true, message: "订单标题不能为空", trigger: "blur" },
                    { min: 2, max: 200, message: "订单标题长度必须介于 2 和 200 之间", trigger: "blur" }
                ],
                span: 24
            },
            {
                label: "培训类型",
                prop: "trainingType",
                search: true,
                searchSpan: 8,
                width: 120,
                align: "center",
                type: "select",
                dicData: trainingTypeOptions,
                span: 8,
                rules: [
                    { required: true, message: "培训类型不能为空", trigger: "change" }
                ]
            },
            {
                label: "培训分类",
                prop: "trainingCategory",
                search: true,
                searchSpan: 8,
                width: 120,
                align: "center",
                type: "select",
                dicData: trainingCategoryOptions,
                span: 8,
                rules: [
                    { required: true, message: "培训分类不能为空", trigger: "change" }
                ]
            },
            {
                label: "培训级别",
                prop: "trainingLevel",
                search: true,
                searchSpan: 8,
                width: 100,
                align: "center",
                type: "select",
                dicData: trainingLevelOptions,
                span: 8
            },
            {
                label: "订单状态",
                prop: "orderStatus",
                search: true,
                searchSpan: 8,
                width: 100,
                align: "center",
                type: "select",
                dicData: orderStatusOptions,
                span: 8,
                slot: true,
                rules: [
                    { required: true, message: "订单状态不能为空", trigger: "change" }
                ]
            },
            {
                label: "是否推荐",
                prop: "isFeatured",
                search: true,
                searchSpan: 8,
                width: 100,
                align: "center",
                type: "select",
                dicData: sys_yes_no,
                span: 8,
                slot: true
            },
            {
                label: "订单描述",
                prop: "orderDescription",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 3,
                maxRows: 6,
                showWordLimit: true,
                maxlength: 2000,
                rules: [
                    { max: 2000, message: "订单描述不能超过2000个字符", trigger: "blur" }
                ]
            },
            // ==================== 培训详情分组 ====================
            {
                label: "培训详情",
                prop: "divider_training_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "培训时长(小时)",
                prop: "trainingDuration",
                search: false,
                width: 120,
                align: "center",
                type: "number",
                span: 8,
                min: 1,
                max: 9999,
                rules: [
                    { required: true, message: "培训时长不能为空", trigger: "blur" },
                    { type: 'number', min: 1, max: 9999, message: '培训时长必须在1-9999小时之间', trigger: 'blur' }
                ]
            },
            {
                label: "最大参与人数",
                prop: "maxParticipants",
                search: false,
                width: 120,
                align: "center",
                type: "number",
                span: 8,
                min: 1,
                max: 9999,
                rules: [
                    { required: true, message: "最大参与人数不能为空", trigger: "blur" },
                    { type: 'number', min: 1, max: 9999, message: '最大参与人数必须在1-9999之间', trigger: 'blur' }
                ]
            },
            {
                label: "当前报名人数",
                prop: "currentParticipants",
                search: false,
                width: 120,
                align: "center",
                type: "number",
                span: 8,
                disabled: true,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "培训费用(元)",
                prop: "trainingFee",
                search: false,
                width: 120,
                align: "center",
                type: "number",
                span: 8,
                min: 0,
                precision: 2,
                rules: [
                    { type: 'number', min: 0, message: '培训费用不能为负数', trigger: 'blur' }
                ]
            },
            {
                label: "培训地址",
                prop: "trainingAddress",
                search: false,
                type: "textarea",
                span: 16,
                minRows: 2,
                maxRows: 4,
                showWordLimit: true,
                maxlength: 500,
                rules: [
                    { max: 500, message: "培训地址不能超过500个字符", trigger: "blur" }
                ]
            },
            // ==================== 联系信息分组 ====================
            {
                label: "联系信息",
                prop: "divider_contact_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "联系人",
                prop: "contactPerson",
                search: true,
                searchSpan: 12,
                width: 120,
                span: 8,
                rules: [
                    { required: true, message: "联系人不能为空", trigger: "blur" },
                    { max: 50, message: "联系人不能超过50个字符", trigger: "blur" }
                ]
            },
            {
                label: "联系电话",
                prop: "contactPhone",
                search: false,
                width: 140,
                span: 8,
                rules: [
                    { required: true, message: "联系电话不能为空", trigger: "blur" },
                    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
                ]
            },
            {
                label: "联系邮箱",
                prop: "contactEmail",
                search: false,
                width: 180,
                span: 8,
                rules: [
                    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
                    { max: 100, message: "联系邮箱不能超过100个字符", trigger: "blur" }
                ]
            },
            // ==================== 时间信息分组 ====================
            {
                label: "时间信息",
                prop: "divider_time_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "开始时间",
                prop: "startDate",
                search: false,
                width: 180,
                align: "center",
                type: "datetime",
                format: "YYYY-MM-DD HH:mm:ss",
                valueFormat: "YYYY-MM-DD HH:mm:ss",
                span: 8,
                rules: [
                    { required: true, message: "开始时间不能为空", trigger: "change" }
                ]
            },
            {
                label: "结束时间",
                prop: "endDate",
                search: false,
                width: 180,
                align: "center",
                type: "datetime",
                format: "YYYY-MM-DD HH:mm:ss",
                valueFormat: "YYYY-MM-DD HH:mm:ss",
                span: 8,
                rules: [
                    { required: true, message: "结束时间不能为空", trigger: "change" }
                ]
            },
            {
                label: "报名截止时间",
                prop: "registrationDeadline",
                search: false,
                width: 180,
                align: "center",
                type: "datetime",
                format: "YYYY-MM-DD HH:mm:ss",
                valueFormat: "YYYY-MM-DD HH:mm:ss",
                span: 8,
                rules: [
                    { required: true, message: "报名截止时间不能为空", trigger: "change" }
                ]
            },
            // ==================== 其他信息分组 ====================
            {
                label: "其他信息",
                prop: "divider_other_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "报名要求",
                prop: "requirements",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 3,
                maxRows: 6,
                showWordLimit: true,
                maxlength: 2000,
                rules: [
                    { max: 2000, message: "报名要求不能超过2000个字符", trigger: "blur" }
                ]
            },
            {
                label: "证书信息",
                prop: "certificateInfo",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                showWordLimit: true,
                maxlength: 500,
                rules: [
                    { max: 500, message: "证书信息不能超过500个字符", trigger: "blur" }
                ]
            },
            // ==================== 系统信息分组 ====================
            {
                label: "系统信息",
                prop: "divider_system_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "备注",
                prop: "remark",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                showWordLimit: true,
                maxlength: 500,
                addDisplay: false,
                editDisplay: false
            }
        ]
    };
};
