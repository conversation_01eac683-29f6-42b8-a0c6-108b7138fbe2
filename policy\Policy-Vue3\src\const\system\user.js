import { parseTime } from "@/utils/ruoyi";

export const createUserTableOption = (proxy) => {
    const {
        sys_user_sex,
        sys_normal_disable
    } = proxy.useDict("sys_user_sex", "sys_normal_disable");

    // 用户类型选项
    const userTypeOptions = [
        { label: "系统用户", value: "00" },
        { label: "普通用户", value: "01" }
    ];

    return {
        dialogWidth: '800px',  // 弹窗宽度
        dialogHeight: '60vh',  // 弹窗内容区最大高度
        labelWidth: '100px',
        column: [
            // ==================== 基础信息分组 ====================
            {
                label: "基础信息",
                prop: "divider_basic_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true // 分隔线标识
            },
            {
                label: "用户编号",
                prop: "userId",
                width: 120,
                align: "center",
                addDisplay: false,
                editDisplay: false,
                search: false
            },
            {
                label: "用户名称",
                prop: "userName",
                search: true,
                searchSpan: 12,
                minWidth: 150,
                rules: [
                    { required: true, message: "用户名称不能为空", trigger: "blur" },
                    { min: 2, max: 20, message: "用户名称长度必须介于 2 和 20 之间", trigger: "blur" }
                ],
                span: 12,
                editDisplay: false  // 编辑时不显示用户名
            },
            {
                label: "用户昵称",
                prop: "nickName",
                search: false,
                width: 150,
                rules: [
                    { required: true, message: "用户昵称不能为空", trigger: "blur" }
                ],
                span: 12
            },
            {
                label: "部门",
                prop: "deptName",
                minWidth: 150,
                formSlot: true, // 使用插槽自定义表单控件
                search: true, // 启用部门搜索
                searchSlot: true, // 使用插槽自定义搜索控件
                addDisplay: true,
                editDisplay: true,
                viewDisplay: true,
                span: 12,
                formatter: (row, column, cellValue) => {
                    return row.dept?.deptName || '-';
                }
            },
            {
                label: "手机号码",
                prop: "phonenumber",
                search: true,
                width: 120,
                rules: [
                    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
                ],
                span: 12
            },
            {
                label: "邮箱",
                prop: "email",
                minWidth: 200,
                rules: [
                    { type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }
                ],
                span: 12,
                search: true // 启用邮箱搜索
            },
            {
                label: "用户密码",
                prop: "password",
                type: "password",
                span: 12,
                addDisplay: true,
                editDisplay: false,
                viewDisplay: false,
                showColumn: false,
                rules: [
                    { required: true, message: "用户密码不能为空", trigger: "blur" },
                    { min: 5, max: 20, message: "用户密码长度必须介于 5 和 20 之间", trigger: "blur" },
                    { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\\ |", trigger: "blur" }
                ]
            },
            {
                label: "用户性别",
                prop: "sex",
                type: 'select',
                span: 12,
                minWidth: 100,
                search: true, // 启用性别搜索
                dicData: sys_user_sex,
                showColumn: false
            },
            {
                label: "用户类型",
                prop: "userType",
                type: 'select',
                span: 12,
                minWidth: 120,
                search: true, // 启用用户类型搜索
                dicData: userTypeOptions,
                rules: [
                    { required: true, message: "用户类型不能为空", trigger: "change" }
                ],
                formatter: (row, column, cellValue) => {
                    const option = userTypeOptions.find(item => item.value === cellValue);
                    return option ? option.label : cellValue;
                },
                change: ({ value, column }) => {
                    // 当用户类型改变时的回调，可以用于控制其他字段的显示
                    return { userType: value };
                }
            },
            {
                label: "状态",
                prop: "status",
                type: 'select',
                span: 12,
                minWidth: 100,
                search: true,
                dicData: sys_normal_disable,
                formSlot: true, // 使用插槽自定义表单控件（单选按钮组）
                slot: true // 使用插槽自定义表格显示（开关）
            },
            {
                label: "岗位",
                prop: "postIds",
                type: 'select',
                multiple: true,
                span: 12,
                formSlot: true, // 使用插槽自定义表单控件
                showColumn: false,
                search: false,
                // 根据用户类型控制显示
                display: (form) => {
                    return form.userType === '00'; // 只有系统用户才显示岗位选择
                }
            },
            {
                label: "角色",
                prop: "roleIds",
                type: 'select',
                multiple: true,
                span: 12,
                formSlot: true, // 使用插槽自定义表单控件
                showColumn: false,
                search: false,
                // 根据用户类型控制显示
                display: (form) => {
                    return form.userType === '00'; // 只有系统用户才显示角色选择
                }
            },
            {
                label: "备注",
                prop: "remark",
                type: 'textarea',
                span: 24,
                showColumn: false,
                search: false
            },
            {
                label: "创建时间",
                prop: "createTime",
                type: 'datetime',
                width: 160,
                align: "center",
                addDisplay: false,
                editDisplay: false,
                search: true,
                searchRange: true, // 支持范围搜索
                formatter: (row, column, cellValue) => {
                    return parseTime(cellValue);
                },
            }
        ]
    };
}; 