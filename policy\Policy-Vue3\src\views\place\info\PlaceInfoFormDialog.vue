<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    :width="formOption.dialogWidth || '1200px'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="formOption.labelWidth || '120px'"
      :size="formOption.size || 'default'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="场地名称" prop="placeName">
            <el-input
              v-model="formData.placeName"
              placeholder="请输入场地名称"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="场地编码" prop="placeCode">
            <el-input
              v-model="formData.placeCode"
              placeholder="请输入场地编码"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="场地类型" prop="placeType">
            <el-select
              v-model="formData.placeType"
              placeholder="请选择场地类型"
              :disabled="mode === 'view'"
              style="width: 100%"
            >
              <el-option label="创业园区" value="创业园区" />
              <el-option label="孵化器" value="孵化器" />
              <el-option label="众创空间" value="众创空间" />
              <el-option label="产业园" value="产业园" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="场地等级" prop="placeLevel">
            <el-select
              v-model="formData.placeLevel"
              placeholder="请选择场地等级"
              :disabled="mode === 'view'"
              style="width: 100%"
            >
              <el-option label="国家级" value="国家级" />
              <el-option label="省级" value="省级" />
              <el-option label="市级" value="市级" />
              <el-option label="区级" value="区级" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="场地面积" prop="placeArea">
            <el-input-number
              v-model="formData.placeArea"
              placeholder="请输入场地面积（平方米）"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="可使用面积" prop="usableArea">
            <el-input-number
              v-model="formData.usableArea"
              placeholder="请输入可使用面积（平方米）"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="0"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="详细地址" prop="address">
            <el-input
              v-model="formData.address"
              placeholder="请输入详细地址"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="区域代码" prop="regionCode">
            <el-select
              v-model="formData.regionCode"
              placeholder="请选择区域"
              :disabled="mode === 'view'"
              style="width: 100%"
            >
              <el-option label="市南区" value="370202" />
              <el-option label="市北区" value="370203" />
              <el-option label="崂山区" value="370212" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="区域名称" prop="regionName">
            <el-input
              v-model="formData.regionName"
              placeholder="请输入区域名称"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input
              v-model="formData.contactPerson"
              placeholder="请输入联系人"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input
              v-model="formData.contactPhone"
              placeholder="请输入联系电话"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="联系邮箱" prop="contactEmail">
            <el-input
              v-model="formData.contactEmail"
              placeholder="请输入联系邮箱"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="入驻企业数" prop="companyCount">
            <el-input-number
              v-model="formData.companyCount"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="可提供工位" prop="availablePositions">
            <el-input-number
              v-model="formData.availablePositions"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="已占用工位" prop="occupiedPositions">
            <el-input-number
              v-model="formData.occupiedPositions"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="0"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最低租金" prop="rentPriceMin">
            <el-input-number
              v-model="formData.rentPriceMin"
              placeholder="元/月/平方米"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最高租金" prop="rentPriceMax">
            <el-input-number
              v-model="formData.rentPriceMax"
              placeholder="元/月/平方米"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="0"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="运营模式" prop="operationMode">
            <el-select
              v-model="formData.operationMode"
              placeholder="请选择运营模式"
              :disabled="mode === 'view'"
              style="width: 100%"
            >
              <el-option label="自营" value="自营" />
              <el-option label="委托运营" value="委托运营" />
              <el-option label="合作运营" value="合作运营" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="行业方向" prop="industryDirection">
            <el-input
              v-model="formData.industryDirection"
              placeholder="多个用逗号分隔"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="场地描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入场地详细描述"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="是否推荐" prop="isFeatured">
            <el-radio-group v-model="formData.isFeatured" :disabled="mode === 'view'">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开放入驻" prop="isOpenSettle">
            <el-radio-group v-model="formData.isOpenSettle" :disabled="mode === 'view'">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status" :disabled="mode === 'view'">
              <el-radio label="0">正常</el-radio>
              <el-radio label="1">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入内容"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer v-if="mode !== 'view'">
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'

const props = defineProps({
  formFields: {
    type: Array,
    default: () => []
  },
  formOption: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['submit', 'cancel'])

const dialogVisible = ref(false)
const mode = ref('add') // add, edit, view
const formRef = ref(null)

const formData = reactive({
  placeId: null,
  placeName: '',
  placeCode: '',
  placeType: '',
  placeLevel: '',
  placeArea: null,
  usableArea: null,
  address: '',
  regionCode: '',
  regionName: '',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  companyCount: 0,
  availablePositions: 0,
  occupiedPositions: 0,
  rentPriceMin: null,
  rentPriceMax: null,
  operationMode: '',
  industryDirection: '',
  description: '',
  status: '0',
  isFeatured: 0,
  isOpenSettle: 1,
  remark: ''
})

const formRules = {
  placeName: [
    { required: true, message: '场地名称不能为空', trigger: 'blur' }
  ],
  placeType: [
    { required: true, message: '场地类型不能为空', trigger: 'change' }
  ],
  address: [
    { required: true, message: '详细地址不能为空', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '联系人不能为空', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '联系电话不能为空', trigger: 'blur' }
  ]
}

const dialogTitle = computed(() => {
  const titleMap = {
    add: '新增场地信息',
    edit: '编辑场地信息',
    view: '查看场地信息'
  }
  return titleMap[mode.value] || '场地信息'
})

const openDialog = (dialogMode, data = {}) => {
  mode.value = dialogMode
  dialogVisible.value = true
  
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (data[key] !== undefined) {
      formData[key] = data[key]
    } else {
      // 设置默认值
      if (key === 'companyCount' || key === 'availablePositions' || key === 'occupiedPositions') {
        formData[key] = 0
      } else if (key === 'status') {
        formData[key] = '0'
      } else if (key === 'isFeatured') {
        formData[key] = 0
      } else if (key === 'isOpenSettle') {
        formData[key] = 1
      } else {
        formData[key] = null
      }
    }
  })
  
  // 清除验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSubmit = () => {
  if (mode.value === 'view') return
  
  formRef.value.validate((valid) => {
    if (valid) {
      emit('submit', { ...formData }, mode.value)
      dialogVisible.value = false
    }
  })
}

const handleCancel = () => {
  dialogVisible.value = false
  emit('cancel')
}

defineExpose({
  openDialog
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
