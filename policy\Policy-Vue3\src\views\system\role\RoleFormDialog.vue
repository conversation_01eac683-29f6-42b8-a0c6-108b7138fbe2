<template>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="formOption.dialogWidth" destroy-on-close
        :close-on-click-modal="false" :fullscreen="isFullscreen" @closed="handleDialogClosed" @open="handleDialogOpened"
        class="custom-dialog">
        <div class="dialog-content" :class="{ 'view-mode': dialogType === 'view' }" :style="dialogContentStyle">
            <FormList ref="formListRef" v-model="formData" :fields="currentFormFields" :is-view="dialogType === 'view'"
                :showActions="false" :labelWidth="formOption.labelWidth" :inline="false"
                @field-change="handleFieldChange" v-if="dialogType !== 'view'">

                <!-- 菜单权限插槽 -->
                <template #menuIds="{ row }">
                    <div class="tree-controls">
                        <el-checkbox v-model="menuExpand"
                            @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
                        <el-checkbox v-model="menuNodeAll"
                            @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
                        <el-checkbox v-model="formData.menuCheckStrictly"
                            @change="handleCheckedTreeConnect($event, 'menu')">父子联动</el-checkbox>
                    </div>
                    <el-tree class="tree-border" :data="menuOptions" show-checkbox ref="menuRef" node-key="id"
                        :check-strictly="!formData.menuCheckStrictly" empty-text="加载中，请稍候"
                        :props="{ label: 'label', children: 'children' }"></el-tree>
                </template>

                <!-- 数据权限范围插槽 -->
                <template #deptIds="{ row }" v-if="formData.dataScope === '2'">
                    <div class="tree-controls">
                        <el-checkbox v-model="deptExpand"
                            @change="handleCheckedTreeExpand($event, 'dept')">展开/折叠</el-checkbox>
                        <el-checkbox v-model="deptNodeAll"
                            @change="handleCheckedTreeNodeAll($event, 'dept')">全选/全不选</el-checkbox>
                        <el-checkbox v-model="formData.deptCheckStrictly"
                            @change="handleCheckedTreeConnect($event, 'dept')">父子联动</el-checkbox>
                    </div>
                    <el-tree class="tree-border" :data="deptOptions" show-checkbox default-expand-all ref="deptRef"
                        node-key="id" :check-strictly="!formData.deptCheckStrictly" empty-text="加载中，请稍候"
                        :props="{ label: 'label', children: 'children' }"></el-tree>
                </template>
            </FormList>

            <!-- 查看模式使用ViewList组件 -->
            <ViewList v-else v-model="formData" :fields="currentFormFields" :labelWidth="formOption.labelWidth">
                <!-- 查看模式下的菜单权限展示 -->
                <template #menuIds="{ row }">
                    <div class="view-menu-tree">
                        <el-tree class="tree-border view-tree" :data="menuOptions" show-checkbox ref="menuRef"
                            node-key="id" :check-strictly="false" :default-expand-all="true"
                            :props="{ label: 'label', children: 'children' }" disabled></el-tree>
                    </div>
                </template>
            </ViewList>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button class="custom-btn" @click="toggleFullscreen">
                    {{ isFullscreen ? '退出全屏' : '全屏显示' }}
                </el-button>
                <el-button class="custom-btn" @click="handleCancel">
                    {{ dialogType === 'view' ? '关闭' : '取消' }}
                </el-button>
                <el-button v-if="dialogType !== 'view'" type="primary" class="custom-btn" @click="handleSubmitForm"
                    :loading="submitLoading" :disabled="submitDisabled">
                    确 认
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup name="RoleFormDialog">
import { ref, reactive, computed, getCurrentInstance, nextTick } from 'vue';
import FormList from '@/components/FormList/index.vue';
import ViewList from '@/components/ViewList/index.vue';
import { roleMenuTreeselect, treeselect as menuTreeselect } from "@/api/system/menu"
import { deptTreeSelect } from "@/api/system/role"

const { proxy } = getCurrentInstance();

// Props
const props = defineProps({
    formFields: {
        type: Array,
        default: () => []
    },
    formOption: {
        type: Object,
        default: () => ({
            dialogWidth: '600px',
            dialogHeight: '70vh'
        })
    }
});

// Emits
const emit = defineEmits([
    'submit',
    'cancel'
]);

// 表单相关
const dialogVisible = ref(false);
const dialogType = ref('add'); // add, edit, view
const dialogTitle = ref('新增角色');
const formListRef = ref(null);
const formData = ref({});
const submitLoading = ref(false);
const submitDisabled = ref(false);

// 树组件相关
const menuRef = ref(null);
const deptRef = ref(null);
const menuOptions = ref([]);
const deptOptions = ref([]);
const menuExpand = ref(false);
const menuNodeAll = ref(false);
const deptExpand = ref(true);
const deptNodeAll = ref(false);

// 控制弹窗全屏状态
const isFullscreen = ref(false);

// 根据表单类型动态筛选字段
const currentFormFields = computed(() => {
    if (!props.formFields.length) return [];

    if (dialogType.value === 'add') {
        return props.formFields.filter(field => field.addDisplay !== false);
    }
    else if (dialogType.value === 'edit') {
        return props.formFields.filter(field => field.editDisplay !== false);
    }
    else { // view模式
        return props.formFields.filter(field => field.viewDisplay !== false);
    }
});

// 弹窗内容样式计算
const dialogContentStyle = computed(() => {
    const baseStyle = {
        overflow: 'visible',
        padding: '20px 10px',
        overflowX: 'hidden',
    }

    if (isFullscreen.value) {
        return {
            ...baseStyle,
            maxHeight: 'calc(100vh - 180px)',
            overflowY: 'auto',
            overflowX: 'hidden',
        }
    }

    return {
        ...baseStyle,
        maxHeight: props.formOption.dialogHeight || '70vh', // 只设置最大高度
        overflowY: 'auto',
        overflowX: 'hidden',
        minHeight: 'auto', // 允许自适应最小高度
    }
});

// 切换全屏状态
const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
};

// 对外暴露的方法
const openDialog = async (type, title, data = {}) => {
    dialogType.value = type;
    dialogTitle.value = title;
    formData.value = {
        roleId: undefined,
        roleName: undefined,
        roleKey: undefined,
        roleSort: 0,
        status: "0",
        menuIds: [],
        deptIds: [],
        menuCheckStrictly: true,
        deptCheckStrictly: true,
        remark: undefined,
        dataScope: "1",
        ...data
    };

    // 重置树状态
    menuExpand.value = false;
    menuNodeAll.value = false;
    deptExpand.value = true;
    deptNodeAll.value = false;

    // 先清空树选择
    if (menuRef.value) {
        menuRef.value.setCheckedKeys([]);
    }

    dialogVisible.value = true;

    // 等待DOM更新后再加载数据
    await nextTick();

    // 加载菜单树
    await getMenuTreeselect();

    // 如果是编辑或查看模式，需要加载角色菜单权限
    if ((type === 'edit' || type === 'view') && data.roleId) {
        await getRoleMenuTreeselect(data.roleId);
    }
};

const closeDialog = () => {
    dialogVisible.value = false;
};

// 查询菜单树结构
const getMenuTreeselect = async () => {
    try {
        const response = await menuTreeselect();
        menuOptions.value = response.data;
    } catch (error) {
        console.error('获取菜单树失败:', error);
    }
};

// 根据角色ID查询菜单树结构
const getRoleMenuTreeselect = async (roleId) => {
    try {
        const response = await roleMenuTreeselect(roleId);
        menuOptions.value = response.menus;

        // 等待一下确保树组件已经渲染
        await nextTick();

        // 设置已选中的菜单
        if (response.checkedKeys && response.checkedKeys.length > 0) {
            setTimeout(() => {
                if (menuRef.value) {
                    // 先清空所有选择
                    menuRef.value.setCheckedKeys([]);

                    // 设置选中的菜单
                    response.checkedKeys.forEach((key) => {
                        if (menuRef.value) {
                            menuRef.value.setChecked(key, true, false);
                        }
                    });
                }
            }, 100); // 延迟一点确保树组件完全渲染
        }

        return response;
    } catch (error) {
        console.error('获取角色菜单树失败:', error);
    }
};

// 根据角色ID查询部门树结构
const getDeptTree = async (roleId) => {
    try {
        const response = await deptTreeSelect(roleId);
        deptOptions.value = response.depts;
        return response;
    } catch (error) {
        console.error('获取部门树失败:', error);
    }
};

// 树权限（展开/折叠）
const handleCheckedTreeExpand = (value, type) => {
    if (type === "menu") {
        let treeList = menuOptions.value;
        for (let i = 0; i < treeList.length; i++) {
            if (menuRef.value?.store.nodesMap[treeList[i].id]) {
                menuRef.value.store.nodesMap[treeList[i].id].expanded = value;
            }
        }
    } else if (type === "dept") {
        let treeList = deptOptions.value;
        for (let i = 0; i < treeList.length; i++) {
            if (deptRef.value?.store.nodesMap[treeList[i].id]) {
                deptRef.value.store.nodesMap[treeList[i].id].expanded = value;
            }
        }
    }
};

// 树权限（全选/全不选）
const handleCheckedTreeNodeAll = (value, type) => {
    if (type === "menu") {
        menuRef.value?.setCheckedNodes(value ? menuOptions.value : []);
    } else if (type === "dept") {
        deptRef.value?.setCheckedNodes(value ? deptOptions.value : []);
    }
};

// 树权限（父子联动）
const handleCheckedTreeConnect = (value, type) => {
    if (type === "menu") {
        formData.value.menuCheckStrictly = value ? true : false;
    } else if (type === "dept") {
        formData.value.deptCheckStrictly = value ? true : false;
    }
};

// 所有菜单节点数据
const getMenuAllCheckedKeys = () => {
    // 目前被选中的菜单节点
    let checkedKeys = menuRef.value?.getCheckedKeys() || [];
    // 半选中的菜单节点
    let halfCheckedKeys = menuRef.value?.getHalfCheckedKeys() || [];
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    return checkedKeys;
};

// 所有部门节点数据
const getDeptAllCheckedKeys = () => {
    // 目前被选中的部门节点
    let checkedKeys = deptRef.value?.getCheckedKeys() || [];
    // 半选中的部门节点
    let halfCheckedKeys = deptRef.value?.getHalfCheckedKeys() || [];
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    return checkedKeys;
};

// 处理表单字段变更
const handleFieldChange = (field, value) => {
    // 如果数据权限变更，需要加载对应的部门树
    if (field === 'dataScope' && value === '2') {
        getDeptTree(formData.value.roleId);
    }
};

// 处理表单提交
const handleSubmitForm = async () => {
    // 防止重复提交
    if (submitLoading.value || submitDisabled.value) {
        return;
    }

    // 确保formListRef存在
    if (!formListRef.value) {
        return;
    }

    try {
        // 设置按钮状态
        submitLoading.value = true;
        submitDisabled.value = true;

        // 表单验证
        await formListRef.value.validate();

        // 获取菜单权限
        formData.value.menuIds = getMenuAllCheckedKeys();

        // 如果是自定义数据权限，获取部门权限
        if (formData.value.dataScope === '2') {
            formData.value.deptIds = getDeptAllCheckedKeys();
        }

        // 发送提交事件给父组件
        emit('submit', {
            type: dialogType.value,
            data: formData.value
        });

    } catch (error) {
        // 验证失败不显示错误消息，因为表单会自动显示错误
        submitLoading.value = false;
        submitDisabled.value = false;
    }
};

// 处理取消
const handleCancel = () => {
    emit('cancel');
    closeDialog();
};

// 处理表单对话框打开
const handleDialogOpened = () => {
    // 设置表单状态
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 处理表单对话框关闭
const handleDialogClosed = () => {
    // 清空表单数据
    formData.value = {};

    // 清空菜单选项和树选择
    menuOptions.value = [];
    deptOptions.value = [];

    if (menuRef.value) {
        menuRef.value.setCheckedKeys([]);
    }
    if (deptRef.value) {
        deptRef.value.setCheckedKeys([]);
    }

    // 重置树状态
    menuExpand.value = false;
    menuNodeAll.value = false;
    deptExpand.value = true;
    deptNodeAll.value = false;

    // 重置提交状态
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 提交成功后的回调
const onSubmitSuccess = () => {
    submitLoading.value = false;
    submitDisabled.value = false;
    closeDialog();
};

// 提交失败后的回调
const onSubmitError = () => {
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 暴露给父组件的方法
defineExpose({
    openDialog,
    closeDialog,
    onSubmitSuccess,
    onSubmitError
});
</script>

<style lang="scss" scoped>
.custom-btn {
    padding: 8px 20px;
}

// 弹窗内容区域样式优化
.dialog-content {
    max-height: v-bind("dialogContentStyle.maxHeight");
    min-height: v-bind("dialogContentStyle.minHeight || 'auto'");
    overflow-y: v-bind("dialogContentStyle.overflowY");
    overflow-x: hidden !important;
    padding: v-bind("dialogContentStyle.padding");
    width: 100%;
    box-sizing: border-box;

    &:not(.view-mode) {
        // 表单模式下的优化
        :deep(.el-form) {
            width: 100%;

            .el-form-item {
                margin-bottom: 18px;
                width: 100%;
                box-sizing: border-box;

                // 统一控件样式
                .el-input,
                .el-select,
                .el-date-editor,
                .el-cascader,
                .el-input-number {
                    width: 100%;
                    max-width: 100%;
                    box-sizing: border-box;
                }

                .el-textarea {
                    width: 100%;
                    max-width: 100%;
                    box-sizing: border-box;

                    .el-textarea__inner {
                        border-radius: 6px;
                        min-height: 80px;
                        width: 100%;
                        box-sizing: border-box;
                        resize: vertical;
                    }
                }

                // 复选框和单选框样式
                .el-checkbox-group,
                .el-radio-group {
                    width: 100%;

                    .el-checkbox,
                    .el-radio {
                        margin-right: 16px;
                        margin-bottom: 8px;
                    }
                }
            }
        }
    }

    &.view-mode {
        padding: 16px 10px;
        width: 100%;
        box-sizing: border-box;
        overflow-x: hidden !important;

        :deep(.view-list) {
            width: 100%;
            box-sizing: border-box;

            .el-descriptions-item__label {
                color: #606266;
                font-weight: 500;
                word-wrap: break-word;
                word-break: break-all;
            }

            .el-descriptions-item__content {
                color: #303133;
                word-wrap: break-word;
                word-break: break-all;
                max-width: 100%;
                overflow-wrap: break-word;
            }
        }
    }
}

.tree-controls {
    margin-bottom: 10px;

    .el-checkbox {
        margin-right: 15px;
    }
}

.tree-border {
    margin-top: 5px;
    border: 1px solid #e5e6e7;
    background: #ffffff none;
    border-radius: 4px;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
}

.view-menu-tree {
    .view-tree {
        border: 1px solid #e5e6e7;
        background: #f5f7fa;
        border-radius: 4px;
        width: 100%;
        max-height: 400px;
        overflow-y: auto;
        padding: 10px;

        :deep(.el-tree-node__content) {
            background-color: transparent;

            &:hover {
                background-color: #f0f9ff;
            }
        }

        :deep(.el-checkbox) {
            pointer-events: none;
        }
    }
}
</style>