package com.sux.system.service.impl;

import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.StringUtils;
import com.sux.system.domain.TrainingOrder;
import com.sux.system.mapper.TrainingOrderMapper;
import com.sux.system.mapper.TrainingApplicationMapper;
import com.sux.system.service.ITrainingOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 培训订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
public class TrainingOrderServiceImpl implements ITrainingOrderService {
    @Autowired
    private TrainingOrderMapper trainingOrderMapper;

    @Autowired
    private TrainingApplicationMapper trainingApplicationMapper;

    /**
     * 查询培训订单
     *
     * @param orderId 培训订单主键
     * @return 培训订单
     */
    @Override
    public TrainingOrder selectTrainingOrderByOrderId(Long orderId) {
        return trainingOrderMapper.selectTrainingOrderByOrderId(orderId);
    }

    /**
     * 查询培训订单列表
     *
     * @param trainingOrder 培训订单
     * @return 培训订单
     */
    @Override
    public List<TrainingOrder> selectTrainingOrderList(TrainingOrder trainingOrder) {
        return trainingOrderMapper.selectTrainingOrderList(trainingOrder);
    }

    /**
     * 新增培训订单
     *
     * @param trainingOrder 培训订单
     * @return 结果
     */
    @Override
    public int insertTrainingOrder(TrainingOrder trainingOrder) {
        // 设置默认值
        if (StringUtils.isEmpty(trainingOrder.getOrderStatus())) {
            trainingOrder.setOrderStatus("0"); // 默认草稿状态
        }
        if (StringUtils.isEmpty(trainingOrder.getIsFeatured())) {
            trainingOrder.setIsFeatured("0"); // 默认不推荐
        }
        if (trainingOrder.getCurrentParticipants() == null) {
            trainingOrder.setCurrentParticipants(0); // 默认报名人数为0
        }

        trainingOrder.setCreateId(SecurityUtils.getUserId());
        trainingOrder.setCreateTime(DateUtils.getNowDate());
        return trainingOrderMapper.insertTrainingOrder(trainingOrder);
    }

    /**
     * 修改培训订单
     *
     * @param trainingOrder 培训订单
     * @return 结果
     */
    @Override
    public int updateTrainingOrder(TrainingOrder trainingOrder) {
        trainingOrder.setUpdateId(SecurityUtils.getUserId());
        trainingOrder.setUpdateTime(DateUtils.getNowDate());
        return trainingOrderMapper.updateTrainingOrder(trainingOrder);
    }

    /**
     * 批量删除培训订单
     *
     * @param orderIds 需要删除的培训订单主键
     * @return 结果
     */
    @Override
    public int deleteTrainingOrderByOrderIds(Long[] orderIds) {
        return trainingOrderMapper.deleteTrainingOrderByOrderIds(orderIds);
    }

    /**
     * 删除培训订单信息
     *
     * @param orderId 培训订单主键
     * @return 结果
     */
    @Override
    public int deleteTrainingOrderByOrderId(Long orderId) {
        return trainingOrderMapper.deleteTrainingOrderByOrderId(orderId);
    }

    /**
     * 校验订单标题是否唯一
     *
     * @param trainingOrder 培训订单信息
     * @return 结果
     */
    @Override
    public boolean checkOrderTitleUnique(TrainingOrder trainingOrder) {
        Long orderId = StringUtils.isNull(trainingOrder.getOrderId()) ? -1L : trainingOrder.getOrderId();
        TrainingOrder info = trainingOrderMapper.checkOrderTitleUnique(trainingOrder);
        if (StringUtils.isNotNull(info) && info.getOrderId().longValue() != orderId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 发布培训订单
     *
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    public int publishTrainingOrder(Long orderId) {
        TrainingOrder trainingOrder = new TrainingOrder();
        trainingOrder.setOrderId(orderId);
        trainingOrder.setOrderStatus("1"); // 设置为发布状态
        trainingOrder.setUpdateId(SecurityUtils.getUserId());
        trainingOrder.setUpdateTime(DateUtils.getNowDate());
        return trainingOrderMapper.updateTrainingOrder(trainingOrder);
    }

    /**
     * 取消培训订单
     *
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    public int cancelTrainingOrder(Long orderId) {
        TrainingOrder trainingOrder = new TrainingOrder();
        trainingOrder.setOrderId(orderId);
        trainingOrder.setOrderStatus("4"); // 设置为已取消状态
        trainingOrder.setUpdateId(SecurityUtils.getUserId());
        trainingOrder.setUpdateTime(DateUtils.getNowDate());
        return trainingOrderMapper.updateTrainingOrder(trainingOrder);
    }

    /**
     * 更新订单报名人数
     *
     * @param orderId   订单ID
     * @param increment 增量（正数为增加，负数为减少）
     * @return 结果
     */
    @Override
    public int updateCurrentParticipants(Long orderId, int increment) {
        return trainingOrderMapper.updateCurrentParticipants(orderId, increment);
    }

    @Override
    public int updateCurrentParticipants(Long orderId) {
        // 重新计算当前报名人数（只统计待审核和已通过的报名）
        int currentCount = trainingApplicationMapper.countApplicationsByOrderId(orderId);
        return trainingOrderMapper.updateCurrentParticipantsCount(orderId, currentCount);
    }

    /**
     * 查询即将开始的培训订单（用于提醒）
     *
     * @param days 提前天数
     * @return 培训订单集合
     */
    @Override
    public List<TrainingOrder> selectUpcomingTrainingOrders(int days) {
        return trainingOrderMapper.selectUpcomingTrainingOrders(days);
    }

    /**
     * 查询已过期的培训订单（报名截止时间已过）
     *
     * @return 培训订单集合
     */
    @Override
    public List<TrainingOrder> selectExpiredTrainingOrders() {
        return trainingOrderMapper.selectExpiredTrainingOrders();
    }

    /**
     * 统计各状态订单数量
     *
     * @return 统计结果
     */
    @Override
    public List<TrainingOrder> selectOrderStatusStatistics() {
        return trainingOrderMapper.selectOrderStatusStatistics();
    }

    /**
     * 自动更新订单状态（定时任务调用）
     *
     * @return 更新的订单数量
     */
    @Override
    public int autoUpdateOrderStatus() {
        int updateCount = 0;
        Date now = new Date();

        // 查询所有发布状态的订单
        TrainingOrder queryOrder = new TrainingOrder();
        queryOrder.setOrderStatus("1");
        List<TrainingOrder> publishedOrders = trainingOrderMapper.selectTrainingOrderList(queryOrder);

        for (TrainingOrder order : publishedOrders) {
            TrainingOrder updateOrder = new TrainingOrder();
            updateOrder.setOrderId(order.getOrderId());
            updateOrder.setUpdateTime(now);

            // 如果开始时间已到，更新为进行中
            if (order.getStartDate() != null && now.after(order.getStartDate()) &&
                    (order.getEndDate() == null || now.before(order.getEndDate()))) {
                updateOrder.setOrderStatus("2"); // 进行中
                trainingOrderMapper.updateTrainingOrder(updateOrder);
                updateCount++;
            }
            // 如果结束时间已到，更新为已完成
            else if (order.getEndDate() != null && now.after(order.getEndDate())) {
                updateOrder.setOrderStatus("3"); // 已完成
                trainingOrderMapper.updateTrainingOrder(updateOrder);
                updateCount++;
            }
        }

        return updateCount;
    }
}
