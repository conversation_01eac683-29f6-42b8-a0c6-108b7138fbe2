import { parseTime } from "@/utils/ruoyi";

// 状态选项
export const statusOptions = [
    {
        label: "启用",
        value: "0",
    },
    {
        label: "禁用",
        value: "1",
    },
];

// 类型选项
export const typeOptions = [
    {
        label: "普通",
        value: "1",
    },
    {
        label: "高级",
        value: "2",
    },
    {
        label: "专业",
        value: "3",
    },
];

// 分类选项
export const categoryOptions = [
    {
        label: "技术类",
        value: "tech",
    },
    {
        label: "管理类",
        value: "manage",
    },
    {
        label: "业务类",
        value: "business",
    },
];

// 地区选项（级联数据示例）
export const regionOptions = [
    {
        value: 'beijing',
        label: '北京',
        children: [
            { value: 'chaoyang', label: '朝阳区' },
            { value: 'haidian', label: '海淀区' },
            { value: 'dongcheng', label: '东城区' }
        ]
    },
    {
        value: 'shanghai',
        label: '上海',
        children: [
            { value: 'huangpu', label: '黄浦区' },
            { value: 'xuhui', label: '徐汇区' },
            { value: 'changning', label: '长宁区' }
        ]
    }
];

// 树形数据示例
export const treeOptions = [
    {
        id: '1',
        value: '1',
        label: '一级 1',
        children: [
            {
                id: '1-1',
                value: '1-1',
                label: '二级 1-1',
                children: [
                    { id: '1-1-1', value: '1-1-1', label: '三级 1-1-1' }
                ]
            }
        ]
    },
    {
        id: '2',
        value: '2',
        label: '一级 2',
        children: [
            { id: '2-1', value: '2-1', label: '二级 2-1' },
            { id: '2-2', value: '2-2', label: '二级 2-2' }
        ]
    }
];

/**
 * 创建完整的表格配置示例
 * 展示所有支持的字段类型和配置选项
 */
export const createTemplateTableOption = (proxy) => {
    const {
        sys_normal_disable,
        sys_yes_no,
        sys_user_sex
    } = proxy.useDict("sys_normal_disable", "sys_yes_no", "sys_user_sex");

    return {
        // ==================== 弹窗配置 ====================
        dialogWidth: '1000px',  // 弹窗宽度
        dialogHeight: '70vh',   // 弹窗内容区最大高度
        labelWidth: '120px',    // 表单标签宽度
        
        // ==================== 表格列配置 ====================
        column: [
            // 1. 基础输入框
            {
                label: "用户名称",
                prop: "userName",
                type: 'input',              // 输入框类型
                span: 12,                   // 表单栅格占用列数（24栅格系统）
                search: true,               // 是否在搜索栏显示
                searchWidth: '200px',       // 搜索框宽度
                placeholder: "请输入用户名称", // 占位符文本
                maxlength: 30,              // 最大输入长度
                showWordLimit: true,        // 是否显示字数统计
                clearable: true,            // 是否可清空
                minWidth: 120,              // 表格列最小宽度
                sortable: true,             // 表格列是否可排序
                showOverflowTooltip: true,  // 内容过长时显示 tooltip
                rules: [{                   // 验证规则
                    required: true,
                    message: "请输入用户名称",
                    trigger: "blur"
                }, {
                    min: 2,
                    max: 20,
                    message: "用户名长度在 2 到 20 个字符",
                    trigger: "blur"
                }],
                tip: "用户名用于登录，不可重复" // 表单项提示文本
            },

            // 2. 密码框
            {
                label: "密码",
                prop: "password",
                type: 'password',           // 密码框类型
                span: 12,
                addDisplay: true,           // 新增时显示
                editDisplay: false,         // 编辑时隐藏
                viewDisplay: false,         // 查看时隐藏
                rules: [{
                    required: true,
                    message: "请输入密码",
                    trigger: "blur"
                }]
            },

            // 3. 文本域
            {
                label: "备注",
                prop: "remark",
                type: 'textarea',           // 文本域类型
                span: 24,                   // 占满整行
                rows: 4,                    // 文本域行数
                autosize: { minRows: 2, maxRows: 6 }, // 自适应高度
                maxlength: 500,
                showWordLimit: true,
                placeholder: "请输入备注信息",
                minWidth: 200
            },

            // 4. 数字输入框
            {
                label: "年龄",
                prop: "age",
                type: 'number',             // 数字输入框
                span: 12,
                min: 0,                     // 最小值
                max: 150,                   // 最大值
                step: 1,                    // 步长
                precision: 0,               // 精度（小数位数）
                controls: true,             // 是否显示控制按钮
                defaultValue: 18,           // 默认值
                minWidth: 100
            },

            // 5. 带后缀的数字输入框
            {
                label: "价格",
                prop: "price",
                type: 'number-suffix',      // 带后缀数字输入框
                span: 12,
                min: 0,
                precision: 2,
                suffix: "元",               // 后缀文本
                minWidth: 120
            },

            // 6. 下拉选择框
            {
                label: "状态",
                prop: "status",
                type: 'select',             // 选择框类型
                span: 12,
                search: true,
                dicData: sys_normal_disable, // 字典数据
                clearable: true,
                filterable: true,           // 是否可搜索
                multiple: false,            // 是否多选
                searchMultiple: true,       // 搜索时是否支持多选
                collapseTags: true,         // 多选时是否折叠标签
                minWidth: 100,
                rules: [{
                    required: true,
                    message: "请选择状态",
                    trigger: "change"
                }]
            },


            // 9. 树选择器
            {
                label: "组织架构",
                prop: "orgId",
                type: 'tree-select',        // 树选择器
                span: 12,
                dicData: treeOptions,       // 树形数据
                checkStrictly: false,       // 是否严格的遵守父子节点不互相关联
                multiple: false,            // 是否多选
                minWidth: 150
            },

            // 11. 单选框组
            {
                label: "性别",
                prop: "sex",
                type: 'radio',              // 单选框组
                span: 12,
                search: true,
                dicData: sys_user_sex,
                button: false,              // 是否使用按钮样式
                size: 'default',           // 尺寸
                minWidth: 100
            },

            // 12. 单选框组（按钮样式）
            {
                label: "类型",
                prop: "type",
                type: 'radio',
                span: 12,
                dicData: typeOptions,
                button: true,               // 按钮样式
                minWidth: 120
            },

            // 13. 多选框组
            {
                label: "兴趣爱好",
                prop: "hobbies",
                type: 'checkbox',           // 多选框组
                span: 12,
                dicData: [
                    { label: "阅读", value: "reading" },
                    { label: "运动", value: "sports" },
                    { label: "音乐", value: "music" },
                    { label: "旅行", value: "travel" }
                ],
                button: false,
                minWidth: 150
            },

            // 14. 开关
            {
                label: "是否启用",
                prop: "isEnabled",
                type: 'switch',             // 开关
                span: 12,
                activeValue: true,          // 激活时的值
                inactiveValue: false,       // 非激活时的值
                activeText: "启用",         // 激活时的文本
                inactiveText: "禁用",       // 非激活时的文本
                minWidth: 100
            },


            // 16. 日期选择器
            {
                label: "出生日期",
                prop: "birthday",
                type: 'date',               // 日期选择器
                span: 12,
                search: true,
                format: 'YYYY-MM-DD',       // 显示格式
                valueFormat: 'YYYY-MM-DD',  // 值格式
                clearable: true,
                minWidth: 150
            },

            // 17. 日期时间选择器
            {
                label: "创建时间",
                prop: "createTime",
                type: 'datetime',           // 日期时间选择器
                span: 12,
                search: true,
                format: 'YYYY-MM-DD HH:mm:ss',
                valueFormat: 'YYYY-MM-DD HH:mm:ss',
                addDisplay: false,          // 新增时不显示
                editDisplay: false,         // 编辑时不显示
                viewDisplay: true,          // 查看时显示
                minWidth: 180
            },

            // 18. 时间选择器
            {
                label: "工作时间",
                prop: "workTime",
                type: 'time',               // 时间选择器
                span: 12,
                format: 'HH:mm:ss',
                valueFormat: 'HH:mm:ss',
                minWidth: 150
            },

            // 19. 日期范围选择器
            {
                label: "有效期",
                prop: "validPeriod",
                type: 'daterange',          // 日期范围选择器
                span: 12,
                search: true,
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD',
                minWidth: 250
            },

            // 21. 颜色选择器
            {
                label: "主题色",
                prop: "themeColor",
                type: 'color',              // 颜色选择器
                span: 12,
                showAlpha: true,            // 是否支持透明度选择
                minWidth: 120
            },

            // 22. 评分
            {
                label: "评分",
                prop: "rating",
                type: 'rate',               // 评分组件
                span: 12,
                max: 5,                     // 最大分值
                allowHalf: true,            // 是否允许半选
                defaultValue: 0,            // 默认值设为数字0
                minWidth: 150
            },

            // 23. 级联选择器
            {
                label: "地区",
                prop: "region",
                type: 'cascader',           // 级联选择器
                span: 12,
                dicData: regionOptions,     // 级联数据
                props: {                    // 配置选项
                    checkStrictly: false,   // 是否严格的遵守父子节点不互相关联
                    expandTrigger: 'click'  // 次级菜单的展开方式
                },
                filterable: true,           // 是否可搜索选项
                clearable: true,
                minWidth: 200
            },

            // 27. 自定义插槽字段
            {
                label: "自定义内容",
                prop: "customField",
                formSlot: true,             // 使用表单插槽
                tableSlot: true,            // 使用表格插槽
                span: 24,
                minWidth: 200
            },

            // 28. 分隔线
            {
                divider: true,              // 分隔线
                label: "基础信息",          // 分隔线标题
                prop: "divider1"
            },

            // 29. 带联动控制的字段
            {
                label: "是否展示扩展信息",
                prop: "showExtendInfo",
                type: 'select',
                span: 12,
                search: true,
                dicData: sys_yes_no,
                rules: [{
                    required: true,
                    message: "请选择是否展示",
                    trigger: "blur"
                }],
                minWidth: 150,
                // 联动控制函数
                control: (val, formData) => {
                    if (val !== "Y") {
                        return {
                            extendInfo: {       // 控制扩展信息字段
                                viewDisplay: false,
                                addDisplay: false,
                                editDisplay: false
                            }
                        }
                    } else {
                        return {
                            extendInfo: {
                                viewDisplay: true,
                                addDisplay: true,
                                editDisplay: true
                            }
                        }
                    }
                }
            },

            // 30. 被联动控制的字段
            {
                label: "扩展信息",
                prop: "extendInfo",
                type: 'textarea',
                span: 24,
                placeholder: "请输入扩展信息",
                rows: 3,
                minWidth: 200
            },
        ]
    };
};