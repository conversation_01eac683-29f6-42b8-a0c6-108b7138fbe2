import request from '@/utils/request'

// 查询招聘信息列表
export function listJobPosting(query) {
  return request({
    url: '/job/posting/list',
    method: 'get',
    params: query
  })
}

// 查询我发布的招聘信息列表
export function listMyJobPosting(query) {
  return request({
    url: '/job/posting/my-list',
    method: 'get',
    params: query
  })
}

// 查询已发布的招聘信息列表（公开接口）
export function listPublishedJobPosting(query) {
  return request({
    url: '/job/posting/published',
    method: 'get',
    params: query
  })
}

// 查询热门招聘信息
export function listHotJobPosting(limit = 10) {
  return request({
    url: '/job/posting/hot',
    method: 'get',
    params: { limit }
  })
}

// 查询推荐招聘信息
export function listFeaturedJobPosting(limit = 10) {
  return request({
    url: '/job/posting/featured',
    method: 'get',
    params: { limit }
  })
}

// 查询紧急招聘信息
export function listUrgentJobPosting(limit = 10) {
  return request({
    url: '/job/posting/urgent',
    method: 'get',
    params: { limit }
  })
}

// 根据关键词搜索招聘信息
export function searchJobPosting(keyword) {
  return request({
    url: '/job/posting/search',
    method: 'get',
    params: { keyword }
  })
}

// ==================== 核心匹配优化接口 ====================

// 基于核心字段搜索招聘信息
export function searchJobPostingByCore(params) {
  return request({
    url: '/job/posting/search-by-core',
    method: 'get',
    params: params
  })
}

// 根据工作类型查询招聘信息
export function listJobPostingByJobType(jobType) {
  return request({
    url: '/job/posting/by-job-type',
    method: 'get',
    params: { jobType }
  })
}

// 根据工作类别查询招聘信息
export function listJobPostingByJobCategory(jobCategory) {
  return request({
    url: '/job/posting/by-job-category',
    method: 'get',
    params: { jobCategory }
  })
}

// 根据薪资类型查询招聘信息
export function listJobPostingBySalaryType(salaryType) {
  return request({
    url: '/job/posting/by-salary-type',
    method: 'get',
    params: { salaryType }
  })
}

// 根据学历要求查询招聘信息
export function listJobPostingByEducation(educationRequired) {
  return request({
    url: '/job/posting/by-education',
    method: 'get',
    params: { educationRequired }
  })
}

// 查询相似的招聘信息
export function listSimilarJobPosting(jobId, limit = 5) {
  return request({
    url: `/job/posting/similar/${jobId}`,
    method: 'get',
    params: { limit }
  })
}

// 智能推荐招聘信息
export function recommendJobPosting(limit = 10) {
  return request({
    url: '/job/posting/recommend',
    method: 'get',
    params: { limit }
  })
}

// 查询招聘信息详细
export function getJobPosting(jobId) {
  return request({
    url: '/job/posting/' + jobId,
    method: 'get'
  })
}

// 新增招聘信息
export function addJobPosting(data) {
  return request({
    url: '/job/posting',
    method: 'post',
    data: data
  })
}

// 修改招聘信息
export function updateJobPosting(data) {
  return request({
    url: '/job/posting',
    method: 'put',
    data: data
  })
}

// 发布招聘信息
export function publishJobPosting(jobId) {
  return request({
    url: `/job/posting/publish/${jobId}`,
    method: 'put'
  })
}

// 暂停招聘信息
export function pauseJobPosting(jobId) {
  return request({
    url: `/job/posting/pause/${jobId}`,
    method: 'put'
  })
}

// 关闭招聘信息
export function closeJobPosting(jobId) {
  return request({
    url: `/job/posting/close/${jobId}`,
    method: 'put'
  })
}

// 完成招聘信息
export function completeJobPosting(jobId) {
  return request({
    url: `/job/posting/complete/${jobId}`,
    method: 'put'
  })
}

// 删除招聘信息
export function delJobPosting(jobId) {
  return request({
    url: '/job/posting/' + jobId,
    method: 'delete'
  })
}

// 批量删除招聘信息
export function delJobPostings(jobIds) {
  return request({
    url: '/job/posting/' + jobIds,
    method: 'delete'
  })
}

// 批量更新招聘信息状态
export function batchUpdateJobPostingStatus(jobIds, status) {
  return request({
    url: '/job/posting/batch-status',
    method: 'put',
    data: { jobIds, status }
  })
}

// 导出招聘信息
export function exportJobPosting(query) {
  return request({
    url: '/job/posting/export',
    method: 'post',
    params: query
  })
}

// 查询招聘信息统计数据
export function getJobPostingStatistics(publisherUserId) {
  return request({
    url: '/job/posting/statistics',
    method: 'get',
    params: { publisherUserId }
  })
}

// 根据工作类别统计招聘信息数量
export function getJobPostingCountByCategory() {
  return request({
    url: '/job/posting/statistics/category',
    method: 'get'
  })
}

// 根据工作地点统计招聘信息数量
export function getJobPostingCountByLocation() {
  return request({
    url: '/job/posting/statistics/location',
    method: 'get'
  })
}

// 根据薪资范围统计招聘信息数量
export function getJobPostingCountBySalary() {
  return request({
    url: '/job/posting/statistics/salary',
    method: 'get'
  })
}

// 查询即将截止的招聘信息
export function listExpiringJobPosting(days = 7) {
  return request({
    url: '/job/posting/expiring',
    method: 'get',
    params: { days }
  })
}

// 增加招聘信息浏览次数（内部调用，通过getJobPosting自动触发）
export function increaseJobPostingViewCount(jobId) {
  return request({
    url: `/job/posting/view/${jobId}`,
    method: 'put'
  })
}

// 增加招聘信息申请次数
export function increaseJobPostingApplicationCount(jobId) {
  return request({
    url: `/job/posting/apply/${jobId}`,
    method: 'put'
  })
}

// 更新招聘信息已招聘人数
export function updateJobPostingPositionsFilled(jobId, positionsFilled) {
  return request({
    url: `/job/posting/positions/${jobId}`,
    method: 'put',
    data: { positionsFilled }
  })
}

// ==================== 选项列表接口 ====================

// 获取所有工作类型列表
export function getJobTypeOptions() {
  return request({
    url: '/job/posting/options/job-types',
    method: 'get'
  })
}

// 获取所有工作类别列表
export function getJobCategoryOptions() {
  return request({
    url: '/job/posting/options/job-categories',
    method: 'get'
  })
}

// 获取所有薪资类型列表
export function getSalaryTypeOptions() {
  return request({
    url: '/job/posting/options/salary-types',
    method: 'get'
  })
}

// 获取所有学历要求列表
export function getEducationRequirementOptions() {
  return request({
    url: '/job/posting/options/education-requirements',
    method: 'get'
  })
}
