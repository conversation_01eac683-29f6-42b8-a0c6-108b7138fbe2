<template>
  <div class="employment-container app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="employmentList" :loading="tableLoading" :showIndex="true"
      :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作" operationWidth="280"
      :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['place:employment:add']">新 增</el-button>
        <el-button type="warning" plain class="custom-btn" @click="handleExport" v-hasPermi="['place:employment:export']">导 出</el-button>
      </template>

      <!-- 用工类型列插槽 -->
      <template #employmentType="{ row }">
        <el-tag :type="getEmploymentTypeTagType(row.employmentType)">
          {{ row.employmentType }}
        </el-tag>
      </template>

      <!-- 薪资范围列插槽 -->
      <template #salaryRange="{ row }">
        <span v-if="row.salaryMin && row.salaryMax" class="salary-range">
          ¥{{ row.salaryMin }}-{{ row.salaryMax }}
        </span>
        <span v-else-if="row.salaryMin" class="salary-range">
          ¥{{ row.salaryMin }}起
        </span>
        <span v-else class="salary-range">面议</span>
      </template>

      <!-- 紧急程度列插槽 -->
      <template #urgencyLevel="{ row }">
        <el-tag :type="getUrgencyTagType(row.urgencyLevel)">
          {{ getUrgencyText(row.urgencyLevel) }}
        </el-tag>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button type="primary" link @click="handleUpdate(row)" v-hasPermi="['place:employment:edit']">编辑</el-button>
          <el-button v-if="row.status === 'draft'" type="success" link @click="handlePublish(row)" v-hasPermi="['place:employment:publish']">发布</el-button>
          <el-button v-if="['draft','published'].includes(row.status)" type="warning" link @click="handlePause(row)" v-hasPermi="['place:employment:pause']">暂停</el-button>
          <el-button type="danger" link @click="handleDelete(row)" v-hasPermi="['place:employment:remove']">删除</el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="Loading table configuration..."></el-empty>
    </div>

    <!-- 表单弹窗组件 -->
    <EmploymentFormDialog ref="employmentFormDialogRef" :formFields="formFields" :formOption="formOption"
      @submit="handleFormSubmit" @cancel="handleFormCancel" />
  </div>
</template>

<script setup name="EmploymentInfo">
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from 'vue'
import { listEmploymentInfo, getEmploymentInfo, delEmploymentInfo, addEmploymentInfo, updateEmploymentInfo } from "@/api/place/employment"
import { createEmploymentTableOption } from "@/const/place/employment"
import { extractTableColumns } from "@/utils/columnUtils"
import TableList from '@/components/TableList/index.vue'
import EmploymentFormDialog from './EmploymentFormDialog.vue'

const { proxy } = getCurrentInstance()

const employmentList = ref([])
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([]) // 可搜索的字段列表
const tableLoading = ref(false)
const isTableReady = ref(false)
const formOption = ref({
  dialogWidth: '1200px',
  dialogHeight: '80vh'
})
const tableListRef = ref(null)
const employmentFormDialogRef = ref(null)
const formFields = ref([])
const searchParams = ref({})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    employmentType: undefined,
    workCategory: undefined,
    salaryType: undefined,
    regionCode: undefined,
    urgencyLevel: undefined,
    status: undefined
  }
})

const { queryParams } = toRefs(data)

onMounted(async () => {
  await initTableConfig()
  getList()
})

async function initTableConfig() {
    try {
        const tableOption = await createEmploymentTableOption()
        const { tableColumns: cols, searchColumns, formFields: fields } = extractTableColumns(tableOption)

        tableColumns.value = cols
        searchableColumns.value = searchColumns
        formFields.value = fields

        isTableReady.value = true
    } catch (error) {
        console.error('初始化表格配置失败:', error)
        proxy.$modal.msgError('表格配置加载失败')
    }
}

/** 查询用工信息列表 */
function getList() {
  tableLoading.value = true
  loading.value = true
  // 处理日期范围搜索参数
  let params = { ...queryParams.value }
  if (searchParams.value.createTime && Array.isArray(searchParams.value.createTime) && searchParams.value.createTime.length === 2) {
    params = proxy.addDateRange(params, searchParams.value.createTime)
  }

  listEmploymentInfo(params).then(res => {
    tableLoading.value = false
    loading.value = false
    if (res.code === 200) {
      employmentList.value = res.rows || []
      total.value = res.total || 0
    } else {
      employmentList.value = []
      total.value = 0
      proxy.$modal.msgError(res.msg || '获取用工信息列表失败')
    }
  }).catch(error => {
    tableLoading.value = false
    loading.value = false
    employmentList.value = []
    total.value = 0
    console.error('获取用工信息列表失败:', error)
    proxy.$modal.msgError('获取用工信息列表失败')
  })
}

// TableList 组件事件处理
const handleSearch = (searchData) => {
  searchParams.value = searchData
  queryParams.value.pageNum = 1
  getList()
}

const resetSearch = () => {
  searchParams.value = {}
  queryParams.value.pageNum = 1
  getList()
}

const handleCurrentChange = (page) => {
  queryParams.value.pageNum = page
  getList()
}

const handleSizeChange = (size) => {
  queryParams.value.pageSize = size
  queryParams.value.pageNum = 1
  getList()
}

const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.employmentId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

// 操作方法
const handleAdd = () => {
  employmentFormDialogRef.value.openDialog('add', {})
}

const handleView = (row) => {
  employmentFormDialogRef.value.openDialog('view', row)
}

const handleUpdate = (row) => {
  employmentFormDialogRef.value.openDialog('edit', row)
}

const handlePublish = async (row) => {
  try {
    await proxy.$modal.confirm('确认要发布该用工信息吗？')
    // 这里需要添加发布API
    // await publishEmploymentInfo(row.employmentId)
    proxy.$modal.msgSuccess('发布成功')
    getList()
  } catch (error) {
    console.error('发布失败:', error)
  }
}

const handlePause = async (row) => {
  try {
    await proxy.$modal.confirm('确认要暂停该用工信息吗？')
    // 这里需要添加暂停API
    // await pauseEmploymentInfo(row.employmentId)
    proxy.$modal.msgSuccess('暂停成功')
    getList()
  } catch (error) {
    console.error('暂停失败:', error)
  }
}

const handleDelete = async (row) => {
  try {
    await proxy.$modal.confirm('确认要删除该用工信息吗？')
    await delEmploymentInfo(row.employmentId)
    proxy.$modal.msgSuccess('删除成功')
    getList()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

const handleExport = () => {
  proxy.download('place/employment/export', {
    ...queryParams.value
  }, `employment_${new Date().getTime()}.xlsx`)
}

// 表单处理方法
const handleFormSubmit = async (formData, mode) => {
  try {
    if (mode === 'add') {
      await addEmploymentInfo(formData)
      proxy.$modal.msgSuccess('新增成功')
    } else if (mode === 'edit') {
      await updateEmploymentInfo(formData)
      proxy.$modal.msgSuccess('修改成功')
    }
    getList()
  } catch (error) {
    console.error('操作失败:', error)
    proxy.$modal.msgError('操作失败')
  }
}

const handleFormCancel = () => {
  // 表单取消处理
}

// 辅助方法
const getEmploymentTypeTagType = (type) => {
  const typeMap = {
    '日结': 'success',
    '周结': 'primary',
    '月结': 'warning',
    '计件': 'info'
  }
  return typeMap[type] || 'default'
}

const getUrgencyTagType = (level) => {
  const levelMap = {
    'urgent': 'danger',
    'high': 'warning',
    'normal': 'info',
    'low': 'success'
  }
  return levelMap[level] || 'info'
}

const getUrgencyText = (level) => {
  const textMap = {
    'urgent': '紧急',
    'high': '高',
    'normal': '普通',
    'low': '低'
  }
  return textMap[level] || '普通'
}

const getStatusTagType = (status) => {
  const statusMap = {
    'draft': 'info',
    'published': 'success',
    'paused': 'warning',
    'closed': 'danger',
    'completed': 'primary'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'draft': '草稿',
    'published': '已发布',
    'paused': '已暂停',
    'closed': '已关闭',
    'completed': '已完成'
  }
  return textMap[status] || '未知'
}
</script>

<style scoped>
.employment-container {
  padding: 20px;
}

.operation-btns {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.salary-range {
  font-weight: 500;
  color: #e6a23c;
}

.custom-btn {
  margin-right: 10px;
}

.loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}
</style>