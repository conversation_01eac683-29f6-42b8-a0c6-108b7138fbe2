package com.sux.web.controller.job;

import com.sux.common.annotation.Anonymous;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.system.domain.JobPosting;
import com.sux.system.domain.WorkerProfile;
import com.sux.system.service.IJobPostingService;
import com.sux.system.service.IWorkerProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 招聘信息匹配公开API Controller（无需登录）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Anonymous
@RestController
@RequestMapping("/public/job")
public class PublicJobMatchController extends BaseController {

    @Autowired
    private IJobPostingService jobPostingService;

    @Autowired
    private IWorkerProfileService workerProfileService;

    /**
     * 查询已发布的招聘信息列表（公开接口）
     */
    @GetMapping("/postings")
    public TableDataInfo getPublishedJobPostings(JobPosting jobPosting) {
        startPage();
        List<JobPosting> list = jobPostingService.selectPublishedJobPostingList(jobPosting);
        return getDataTable(list);
    }

    /**
     * 查询热门招聘信息（公开接口）
     */
    @GetMapping("/postings/hot")
    public AjaxResult getHotJobPostings(@RequestParam(defaultValue = "10") Integer limit) {
        List<JobPosting> list = jobPostingService.selectHotJobPostingList(limit);
        return success(list);
    }

    /**
     * 查询推荐招聘信息（公开接口）
     */
    @GetMapping("/postings/featured")
    public AjaxResult getFeaturedJobPostings(@RequestParam(defaultValue = "10") Integer limit) {
        List<JobPosting> list = jobPostingService.selectFeaturedJobPostingList(limit);
        return success(list);
    }

    /**
     * 查询紧急招聘信息（公开接口）
     */
    @GetMapping("/postings/urgent")
    public AjaxResult getUrgentJobPostings(@RequestParam(defaultValue = "10") Integer limit) {
        List<JobPosting> list = jobPostingService.selectUrgentJobPostingList(limit);
        return success(list);
    }

    /**
     * 根据关键词搜索招聘信息（公开接口）
     */
    @GetMapping("/postings/search")
    public TableDataInfo searchJobPostings(@RequestParam String keyword) {
        startPage();
        List<JobPosting> list = jobPostingService.selectJobPostingByKeyword(keyword);
        return getDataTable(list);
    }

    /**
     * 获取招聘信息详细信息（公开接口）
     */
    @GetMapping("/postings/{jobId}")
    public AjaxResult getJobPostingDetail(@PathVariable("jobId") Long jobId) {
        // 增加浏览次数
        jobPostingService.increaseViewCount(jobId);

        JobPosting jobPosting = jobPostingService.selectJobPostingDetailByJobId(jobId);
        if (jobPosting == null) {
            return error("招聘信息不存在");
        }
        return success(jobPosting);
    }

    /**
     * 根据招聘信息匹配零工（带相似度评分）（公开接口）
     */
    @GetMapping("/postings/{jobId}/match-workers")
    public AjaxResult matchWorkersForJob(@PathVariable Long jobId, @RequestParam(defaultValue = "10") Integer limit) {
        JobPosting jobPosting = jobPostingService.selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return error("招聘信息不存在");
        }

        try {
            List<Map<String, Object>> matchResults = jobPostingService.matchWorkersWithSimilarity(jobId, limit);

            // 如果没有匹配结果，提供一些基础的零工信息
            if (matchResults == null || matchResults.isEmpty()) {
                List<WorkerProfile> activeWorkers = workerProfileService.selectActiveWorkerProfileList(new WorkerProfile());
                matchResults = new java.util.ArrayList<>();

                for (int i = 0; i < Math.min(limit, activeWorkers.size()); i++) {
                    WorkerProfile worker = activeWorkers.get(i);
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("worker", worker);
                    result.put("similarity", 0.5 + Math.random() * 0.3); // 模拟匹配度
                    result.put("similarityPercentage", Math.round((0.5 + Math.random() * 0.3) * 100));
                    result.put("matchReasons", java.util.Arrays.asList("基础匹配", "地理位置相近"));
                    matchResults.add(result);
                }
            }

            return success(matchResults);
        } catch (Exception e) {
            logger.error("匹配零工失败", e);
            return error("匹配服务暂时不可用，请稍后重试");
        }
    }

    /**
     * 计算招聘信息与零工的相似度（公开接口）
     */
    @GetMapping("/postings/{jobId}/similarity/{workerId}")
    public AjaxResult calculateJobWorkerSimilarity(@PathVariable Long jobId, @PathVariable Long workerId) {
        Double similarity = jobPostingService.calculateJobWorkerSimilarity(jobId, workerId);
        if (similarity == null) {
            return error("计算相似度失败，请检查招聘信息或零工信息是否存在");
        }

        return success()
                .put("jobId", jobId)
                .put("workerId", workerId)
                .put("similarity", similarity)
                .put("similarityPercentage", Math.round(similarity * 100));
    }

    /**
     * 查询相似的招聘信息（公开接口）
     */
    @GetMapping("/postings/{jobId}/similar")
    public AjaxResult getSimilarJobPostings(@PathVariable Long jobId, @RequestParam(defaultValue = "5") Integer limit) {
        JobPosting jobPosting = jobPostingService.selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return error("招聘信息不存在");
        }
        List<JobPosting> list = jobPostingService.selectSimilarJobPostingList(jobPosting, limit);
        return success(list);
    }

    /**
     * 查询活跃的零工信息列表（公开接口）
     */
    @GetMapping("/workers")
    public TableDataInfo getActiveWorkers(WorkerProfile workerProfile) {
        startPage();
        List<WorkerProfile> list = workerProfileService.selectActiveWorkerProfileList(workerProfile);
        return getDataTable(list);
    }

    /**
     * 查询已验证的零工信息列表（公开接口）
     */
    @GetMapping("/workers/verified")
    public TableDataInfo getVerifiedWorkers(WorkerProfile workerProfile) {
        startPage();
        List<WorkerProfile> list = workerProfileService.selectVerifiedWorkerProfileList(workerProfile);
        return getDataTable(list);
    }

    /**
     * 查询高评分零工信息（公开接口）
     */
    @GetMapping("/workers/high-rated")
    public AjaxResult getHighRatedWorkers(@RequestParam(defaultValue = "4.0") Double minRating,
                                         @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectHighRatedWorkerProfileList(minRating, limit);
        return success(list);
    }

    /**
     * 查询经验丰富的零工信息（公开接口）
     */
    @GetMapping("/workers/experienced")
    public AjaxResult getExperiencedWorkers(@RequestParam(defaultValue = "3") Integer minExperience,
                                           @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectExperiencedWorkerProfileList(minExperience, limit);
        return success(list);
    }

    /**
     * 查询推荐零工信息（公开接口）
     */
    @GetMapping("/workers/recommended")
    public AjaxResult getRecommendedWorkers(@RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectRecommendedWorkerProfileList(limit);
        return success(list);
    }

    /**
     * 根据关键词搜索零工信息（公开接口）
     */
    @GetMapping("/workers/search")
    public TableDataInfo searchWorkers(@RequestParam String keyword) {
        startPage();
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileByKeyword(keyword);
        return getDataTable(list);
    }

    /**
     * 根据技能匹配零工信息（公开接口）
     */
    @GetMapping("/workers/match-skills")
    public AjaxResult matchWorkersBySkills(@RequestParam List<String> skills,
                                          @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileBySkills(skills, limit);
        return success(list);
    }

    /**
     * 根据地理位置匹配零工信息（公开接口）
     */
    @GetMapping("/workers/match-location")
    public AjaxResult matchWorkersByLocation(@RequestParam String location,
                                            @RequestParam(defaultValue = "10.0") Double radius,
                                            @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileByLocation(location, radius, limit);
        return success(list);
    }

    /**
     * 获取零工信息详细信息（公开接口）
     */
    @GetMapping("/workers/{workerId}")
    public AjaxResult getWorkerDetail(@PathVariable("workerId") Long workerId) {
        WorkerProfile workerProfile = workerProfileService.selectWorkerProfileDetailByWorkerId(workerId);
        if (workerProfile == null) {
            return error("零工信息不存在");
        }
        return success(workerProfile);
    }

    /**
     * 查询相似的零工信息（公开接口）
     */
    @GetMapping("/workers/{workerId}/similar")
    public AjaxResult getSimilarWorkers(@PathVariable Long workerId, @RequestParam(defaultValue = "5") Integer limit) {
        WorkerProfile workerProfile = workerProfileService.selectWorkerProfileByWorkerId(workerId);
        if (workerProfile == null) {
            return error("零工信息不存在");
        }
        List<WorkerProfile> list = workerProfileService.selectSimilarWorkerProfileList(workerProfile, limit);
        return success(list);
    }

    /**
     * 根据招聘信息统计数据（公开接口）
     */
    @GetMapping("/statistics/postings")
    public AjaxResult getJobPostingStatistics() {
        Map<String, Object> data = jobPostingService.selectJobPostingStatistics(null);
        return success(data);
    }

    /**
     * 根据工作类别统计招聘信息数量（公开接口）
     */
    @GetMapping("/statistics/postings/category")
    public AjaxResult getJobPostingStatisticsByCategory() {
        List<Map<String, Object>> data = jobPostingService.selectJobPostingCountByCategory();
        return success(data);
    }

    /**
     * 获取所有筛选选项（公开接口）
     */
    @GetMapping("/filter-options")
    public AjaxResult getFilterOptions() {
        Map<String, Object> result = new java.util.HashMap<>();

        try {
            // 获取所有工作类型
            List<String> jobTypes = jobPostingService.selectAllJobTypes();
            result.put("jobTypes", jobTypes);

            // 获取所有工作类别
            List<String> jobCategories = jobPostingService.selectAllJobCategories();
            result.put("jobCategories", jobCategories);

            // 获取所有薪资类型
            List<String> salaryTypes = jobPostingService.selectAllSalaryTypes();
            result.put("salaryTypes", salaryTypes);

            // 获取所有学历要求
            List<String> educationLevels = jobPostingService.selectAllEducationRequirements();
            result.put("educationLevels", educationLevels);

            return success(result);
        } catch (Exception e) {
            logger.error("获取筛选选项失败", e);
            return error("获取筛选选项失败");
        }
    }

    /**
     * 简化版招聘信息匹配零工（公开接口）
     * 专门为前端弹框匹配功能设计
     */
    @GetMapping("/simple/postings/{jobId}/match-workers")
    public AjaxResult simpleMatchWorkersForJob(@PathVariable Long jobId, @RequestParam(defaultValue = "10") Integer limit) {
        try {
            JobPosting jobPosting = jobPostingService.selectJobPostingByJobId(jobId);
            if (jobPosting == null) {
                return error("招聘信息不存在");
            }

            // 使用简化的匹配逻辑
            List<Map<String, Object>> matchResults = performSimpleMatching(jobPosting, limit);

            return success(matchResults);
        } catch (Exception e) {
            logger.error("简化匹配零工失败", e);
            // 返回模拟数据作为降级方案
            return success(generateMockMatchResults(jobId));
        }
    }

    /**
     * 执行简化匹配逻辑
     */
    private List<Map<String, Object>> performSimpleMatching(JobPosting jobPosting, Integer limit) {
        // 构建匹配参数
        Map<String, Object> matchParams = new java.util.HashMap<>();
        matchParams.put("jobCategory", jobPosting.getJobCategory());
        matchParams.put("jobType", jobPosting.getJobType());
        matchParams.put("salaryType", jobPosting.getSalaryType());
        matchParams.put("educationRequired", jobPosting.getEducationRequired());
        matchParams.put("workLocation", jobPosting.getWorkLocation());
        matchParams.put("limit", limit);

        // 获取匹配的零工列表
        List<WorkerProfile> workers = workerProfileService.selectWorkerProfileByMatchParams(matchParams);

        // 构建结果
        List<Map<String, Object>> results = new java.util.ArrayList<>();
        for (WorkerProfile worker : workers) {
            // 计算简化的相似度
            int similarity = calculateSimpleSimilarity(jobPosting, worker);

            Map<String, Object> result = new java.util.HashMap<>();
            result.put("worker", worker);
            result.put("similarity", similarity / 100.0);
            result.put("similarityPercentage", similarity);
            result.put("matchScore", similarity);

            results.add(result);
        }

        // 按相似度排序
        results.sort((a, b) -> {
            Integer simA = (Integer) a.get("similarityPercentage");
            Integer simB = (Integer) b.get("similarityPercentage");
            return simB.compareTo(simA);
        });

        return results;
    }

    /**
     * 计算简化的相似度评分
     */
    private int calculateSimpleSimilarity(JobPosting jobPosting, WorkerProfile worker) {
        int totalScore = 0;
        int maxScore = 0;

        // 工作类别匹配 (40分)
        maxScore += 40;
        if (jobPosting.getJobCategory() != null && worker.getWorkCategories() != null) {
            if (worker.getWorkCategories().contains(jobPosting.getJobCategory())) {
                totalScore += 40;
            } else if (worker.getWorkCategories().toLowerCase().contains(jobPosting.getJobCategory().toLowerCase())) {
                totalScore += 20;
            }
        }

        // 工作类型匹配 (30分)
        maxScore += 30;
        if (jobPosting.getJobType() != null && worker.getJobTypesPreferred() != null) {
            if (worker.getJobTypesPreferred().contains(jobPosting.getJobType())) {
                totalScore += 30;
            }
        }

        // 薪资类型匹配 (20分)
        maxScore += 20;
        if (jobPosting.getSalaryType() != null && worker.getSalaryTypePreference() != null) {
            if (jobPosting.getSalaryType().equals(worker.getSalaryTypePreference())) {
                totalScore += 20;
            }
        }

        // 学历匹配 (10分)
        maxScore += 10;
        if (jobPosting.getEducationRequired() != null && worker.getEducationLevel() != null) {
            if (isEducationMatch(jobPosting.getEducationRequired(), worker.getEducationLevel())) {
                totalScore += 10;
            }
        }

        return maxScore > 0 ? (totalScore * 100) / maxScore : 0;
    }

    /**
     * 判断学历是否匹配
     */
    private boolean isEducationMatch(String required, String actual) {
        if ("不限".equals(required)) return true;

        String[] educationLevels = {"初中", "高中", "中专", "大专", "本科", "硕士", "博士"};
        int requiredIndex = -1, actualIndex = -1;

        for (int i = 0; i < educationLevels.length; i++) {
            if (educationLevels[i].equals(required)) requiredIndex = i;
            if (educationLevels[i].equals(actual)) actualIndex = i;
        }

        return actualIndex >= requiredIndex;
    }

    /**
     * 生成模拟匹配结果
     */
    private List<Map<String, Object>> generateMockMatchResults(Long jobId) {
        List<Map<String, Object>> results = new java.util.ArrayList<>();

        // 模拟零工1
        Map<String, Object> worker1 = new java.util.HashMap<>();
        worker1.put("workerId", 101);
        worker1.put("realName", "张小美");
        worker1.put("nickname", "美美服务员");
        worker1.put("workCategories", "[\"服务员\"]");
        worker1.put("jobTypesPreferred", "[\"兼职\",\"临时工\"]");
        worker1.put("educationLevel", "高中");
        worker1.put("workExperienceYears", 2);
        worker1.put("salaryExpectationMin", 20.00);
        worker1.put("salaryExpectationMax", 30.00);
        worker1.put("salaryTypePreference", "hourly");
        worker1.put("currentLocation", "西宁市市南区");
        worker1.put("ratingAverage", 4.8);
        worker1.put("completedJobs", 18);
        worker1.put("successRate", 95.5);
        worker1.put("isVerified", 1);
        worker1.put("selfIntroduction", "有2年餐厅服务经验，服务态度好，沟通能力强");

        Map<String, Object> result1 = new java.util.HashMap<>();
        result1.put("worker", worker1);
        result1.put("similarity", 0.92);
        result1.put("similarityPercentage", 92);
        result1.put("matchScore", 88);
        results.add(result1);

        // 模拟零工2
        Map<String, Object> worker2 = new java.util.HashMap<>();
        worker2.put("workerId", 104);
        worker2.put("realName", "刘大姐");
        worker2.put("nickname", "勤劳刘姐");
        worker2.put("workCategories", "[\"保洁\"]");
        worker2.put("jobTypesPreferred", "[\"全职\",\"兼职\",\"临时工\"]");
        worker2.put("educationLevel", "初中");
        worker2.put("workExperienceYears", 8);
        worker2.put("salaryExpectationMin", 25.00);
        worker2.put("salaryExpectationMax", 35.00);
        worker2.put("salaryTypePreference", "hourly");
        worker2.put("currentLocation", "西宁市市北区");
        worker2.put("ratingAverage", 4.7);
        worker2.put("completedJobs", 38);
        worker2.put("successRate", 94.2);
        worker2.put("isVerified", 1);
        worker2.put("selfIntroduction", "有8年保洁经验，工作认真负责，熟悉各种清洁用品使用");

        Map<String, Object> result2 = new java.util.HashMap<>();
        result2.put("worker", worker2);
        result2.put("similarity", 0.75);
        result2.put("similarityPercentage", 75);
        result2.put("matchScore", 78);
        results.add(result2);

        return results;
    }

    /**
     * 根据工作地点统计招聘信息数量（公开接口）
     */
    @GetMapping("/statistics/postings/location")
    public AjaxResult getJobPostingStatisticsByLocation() {
        List<Map<String, Object>> data = jobPostingService.selectJobPostingCountByLocation();
        return success(data);
    }

    /**
     * 根据零工统计数据（公开接口）
     */
    @GetMapping("/statistics/workers")
    public AjaxResult getWorkerStatistics() {
        Map<String, Object> data = workerProfileService.selectWorkerProfileStatistics(null);
        return success(data);
    }

    /**
     * 根据工作类别统计零工数量（公开接口）
     */
    @GetMapping("/statistics/workers/category")
    public AjaxResult getWorkerStatisticsByCategory() {
        List<Map<String, Object>> data = workerProfileService.selectWorkerProfileCountByCategory();
        return success(data);
    }

    /**
     * 根据所在地统计零工数量（公开接口）
     */
    @GetMapping("/statistics/workers/location")
    public AjaxResult getWorkerStatisticsByLocation() {
        List<Map<String, Object>> data = workerProfileService.selectWorkerProfileCountByLocation();
        return success(data);
    }

    /**
     * 快速匹配接口 - 基于简单条件快速匹配
     */
    @GetMapping("/quick-match")
    public AjaxResult quickMatch(@RequestParam(required = false) String jobType,
                                @RequestParam(required = false) String location,
                                @RequestParam(required = false) String skills,
                                @RequestParam(defaultValue = "5") Integer limit) {
        try {
            Map<String, Object> result = new java.util.HashMap<>();

            // 根据条件查找招聘信息
            JobPosting searchJob = new JobPosting();
            if (jobType != null && !jobType.isEmpty()) {
                searchJob.setJobType(jobType);
            }
            if (location != null && !location.isEmpty()) {
                searchJob.setWorkLocation(location);
            }

            List<JobPosting> matchedJobs = jobPostingService.selectPublishedJobPostingList(searchJob);

            // 限制结果数量
            if (matchedJobs.size() > limit) {
                matchedJobs = matchedJobs.subList(0, limit);
            }

            // 为每个招聘信息添加匹配度信息
            List<Map<String, Object>> jobsWithMatch = new java.util.ArrayList<>();
            for (JobPosting job : matchedJobs) {
                Map<String, Object> jobData = new java.util.HashMap<>();
                jobData.put("job", job);
                jobData.put("matchScore", calculateQuickMatchScore(job, jobType, location, skills));
                jobData.put("matchReasons", getQuickMatchReasons(job, jobType, location, skills));
                jobsWithMatch.add(jobData);
            }

            // 按匹配度排序
            jobsWithMatch.sort((a, b) -> {
                Double scoreA = (Double) a.get("matchScore");
                Double scoreB = (Double) b.get("matchScore");
                return scoreB.compareTo(scoreA);
            });

            result.put("jobs", jobsWithMatch);
            result.put("total", matchedJobs.size());
            result.put("searchCriteria", Map.of(
                "jobType", jobType != null ? jobType : "不限",
                "location", location != null ? location : "不限",
                "skills", skills != null ? skills : "不限"
            ));

            return success(result);
        } catch (Exception e) {
            logger.error("快速匹配失败", e);
            return error("快速匹配服务暂时不可用");
        }
    }

    /**
     * 计算快速匹配分数
     */
    private double calculateQuickMatchScore(JobPosting job, String jobType, String location, String skills) {
        double score = 0.5; // 基础分数

        // 工作类型匹配
        if (jobType != null && jobType.equals(job.getJobType())) {
            score += 0.3;
        }

        // 地点匹配
        if (location != null && job.getWorkLocation() != null &&
            job.getWorkLocation().contains(location)) {
            score += 0.2;
        }


        return Math.min(1.0, score);
    }

    /**
     * 获取快速匹配原因
     */
    private java.util.List<String> getQuickMatchReasons(JobPosting job, String jobType, String location, String skills) {
        java.util.List<String> reasons = new java.util.ArrayList<>();

        if (jobType != null && jobType.equals(job.getJobType())) {
            reasons.add("工作类型匹配");
        }

        if (location != null && job.getWorkLocation() != null &&
            job.getWorkLocation().contains(location)) {
            reasons.add("工作地点匹配");
        }



        if (reasons.isEmpty()) {
            reasons.add("基础匹配");
        }

        return reasons;
    }
}
