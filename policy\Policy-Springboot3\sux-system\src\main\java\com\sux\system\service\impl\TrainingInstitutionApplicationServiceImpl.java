package com.sux.system.service.impl;

import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.system.domain.TrainingInstitutionApplication;
import com.sux.system.mapper.TrainingInstitutionApplicationMapper;
import com.sux.system.service.ITrainingInstitutionApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 培训机构申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class TrainingInstitutionApplicationServiceImpl implements ITrainingInstitutionApplicationService 
{
    @Autowired
    private TrainingInstitutionApplicationMapper trainingInstitutionApplicationMapper;

    /**
     * 查询培训机构申请
     * 
     * @param applicationId 培训机构申请主键
     * @return 培训机构申请
     */
    @Override
    public TrainingInstitutionApplication selectTrainingInstitutionApplicationByApplicationId(Long applicationId)
    {
        return trainingInstitutionApplicationMapper.selectTrainingInstitutionApplicationByApplicationId(applicationId);
    }

    /**
     * 查询培训机构申请列表
     * 
     * @param trainingInstitutionApplication 培训机构申请
     * @return 培训机构申请
     */
    @Override
    public List<TrainingInstitutionApplication> selectTrainingInstitutionApplicationList(TrainingInstitutionApplication trainingInstitutionApplication)
    {
        return trainingInstitutionApplicationMapper.selectTrainingInstitutionApplicationList(trainingInstitutionApplication);
    }

    /**
     * 新增培训机构申请
     * 
     * @param trainingInstitutionApplication 培训机构申请
     * @return 结果
     */
    @Override
    public int insertTrainingInstitutionApplication(TrainingInstitutionApplication trainingInstitutionApplication)
    {
        trainingInstitutionApplication.setCreateTime(DateUtils.getNowDate());
        return trainingInstitutionApplicationMapper.insertTrainingInstitutionApplication(trainingInstitutionApplication);
    }

    /**
     * 修改培训机构申请
     * 
     * @param trainingInstitutionApplication 培训机构申请
     * @return 结果
     */
    @Override
    public int updateTrainingInstitutionApplication(TrainingInstitutionApplication trainingInstitutionApplication)
    {
        trainingInstitutionApplication.setUpdateTime(DateUtils.getNowDate());
        return trainingInstitutionApplicationMapper.updateTrainingInstitutionApplication(trainingInstitutionApplication);
    }

    /**
     * 批量删除培训机构申请
     * 
     * @param applicationIds 需要删除的培训机构申请主键
     * @return 结果
     */
    @Override
    public int deleteTrainingInstitutionApplicationByApplicationIds(Long[] applicationIds)
    {
        return trainingInstitutionApplicationMapper.deleteTrainingInstitutionApplicationByApplicationIds(applicationIds);
    }

    /**
     * 删除培训机构申请信息
     * 
     * @param applicationId 培训机构申请主键
     * @return 结果
     */
    @Override
    public int deleteTrainingInstitutionApplicationByApplicationId(Long applicationId)
    {
        return trainingInstitutionApplicationMapper.deleteTrainingInstitutionApplicationByApplicationId(applicationId);
    }

    /**
     * 检查机构是否已申请某个培训订单
     * 
     * @param orderId 培训订单ID
     * @param userId 用户ID
     * @return 申请记录
     */
    @Override
    public TrainingInstitutionApplication checkInstitutionApplication(Long orderId, Long userId)
    {
        return trainingInstitutionApplicationMapper.selectApplicationByOrderIdAndUserId(orderId, userId);
    }

    /**
     * 检查机构名称是否已申请某个培训订单
     * 
     * @param orderId 培训订单ID
     * @param institutionName 机构名称
     * @return 申请记录
     */
    @Override
    public TrainingInstitutionApplication checkInstitutionNameApplication(Long orderId, String institutionName)
    {
        return trainingInstitutionApplicationMapper.selectApplicationByOrderIdAndInstitutionName(orderId, institutionName);
    }

    /**
     * 检查联系电话是否已申请某个培训订单
     * 
     * @param orderId 培训订单ID
     * @param contactPhone 联系电话
     * @return 申请记录
     */
    @Override
    public TrainingInstitutionApplication checkPhoneApplication(Long orderId, String contactPhone)
    {
        return trainingInstitutionApplicationMapper.selectApplicationByOrderIdAndPhone(orderId, contactPhone);
    }

    /**
     * 统计某个培训订单的机构申请数量
     * 
     * @param orderId 培训订单ID
     * @return 申请数量
     */
    @Override
    public int countApplicationsByOrderId(Long orderId)
    {
        return trainingInstitutionApplicationMapper.countApplicationsByOrderId(orderId);
    }

    /**
     * 统计某个培训订单的已通过机构申请数量
     * 
     * @param orderId 培训订单ID
     * @return 已通过申请数量
     */
    @Override
    public int countApprovedApplicationsByOrderId(Long orderId)
    {
        return trainingInstitutionApplicationMapper.countApprovedApplicationsByOrderId(orderId);
    }

    /**
     * 获取某个培训订单的机构申请列表
     * 
     * @param orderId 培训订单ID
     * @return 申请列表
     */
    @Override
    public List<TrainingInstitutionApplication> getApplicationsByOrderId(Long orderId)
    {
        return trainingInstitutionApplicationMapper.getApplicationsByOrderId(orderId);
    }

    /**
     * 审核机构申请
     * 
     * @param applicationId 申请ID
     * @param status 审核状态
     * @param reviewer 审核人
     * @param reviewComment 审核意见
     * @return 结果
     */
    @Override
    @Transactional
    public int reviewApplication(Long applicationId, String status, String reviewer, String reviewComment)
    {
        TrainingInstitutionApplication application = new TrainingInstitutionApplication();
        application.setApplicationId(applicationId);
        application.setApplicationStatus(status);
        application.setReviewer(reviewer);
        application.setReviewComment(reviewComment);
        application.setReviewTime(new Date());
        application.setUpdateTime(DateUtils.getNowDate());
        
        try {
            application.setUpdateId(SecurityUtils.getUserId());
        } catch (Exception e) {
            // 未登录用户，不设置更新者ID
        }
        
        return trainingInstitutionApplicationMapper.updateTrainingInstitutionApplication(application);
    }

    /**
     * 批量审核机构申请
     * 
     * @param applicationIds 申请ID数组
     * @param status 审核状态
     * @param reviewer 审核人
     * @param reviewComment 审核意见
     * @return 结果
     */
    @Override
    @Transactional
    public int batchReviewApplications(Long[] applicationIds, String status, String reviewer, String reviewComment)
    {
        return trainingInstitutionApplicationMapper.batchUpdateStatus(applicationIds, status, reviewer, reviewComment);
    }

    /**
     * 提交机构申请
     * 
     * @param trainingInstitutionApplication 申请信息
     * @return 结果
     */
    @Override
    @Transactional
    public int submitApplication(TrainingInstitutionApplication trainingInstitutionApplication)
    {
        // 设置申请时间和状态
        trainingInstitutionApplication.setApplicationTime(new Date());
        trainingInstitutionApplication.setApplicationStatus("0"); // 待审核
        trainingInstitutionApplication.setCreateTime(DateUtils.getNowDate());
        
        // 如果有登录用户，设置用户ID
        try {
            Long userId = SecurityUtils.getUserId();
            trainingInstitutionApplication.setUserId(userId);
            trainingInstitutionApplication.setCreateId(SecurityUtils.getUserId());
        } catch (Exception e) {
            // 未登录用户，不设置用户ID
        }
        
        return trainingInstitutionApplicationMapper.insertTrainingInstitutionApplication(trainingInstitutionApplication);
    }

    /**
     * 取消机构申请
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelApplication(Long applicationId)
    {
        TrainingInstitutionApplication application = new TrainingInstitutionApplication();
        application.setApplicationId(applicationId);
        application.setApplicationStatus("3"); // 已取消
        application.setUpdateTime(DateUtils.getNowDate());
        
        try {
            application.setUpdateId(SecurityUtils.getUserId());
        } catch (Exception e) {
            // 未登录用户，不设置更新者ID
        }
        
        return trainingInstitutionApplicationMapper.updateTrainingInstitutionApplication(application);
    }

    /**
     * 检查申请唯一性
     * 
     * @param trainingInstitutionApplication 申请信息
     * @return 结果
     */
    @Override
    public boolean checkApplicationUnique(TrainingInstitutionApplication trainingInstitutionApplication)
    {
        Long orderId = trainingInstitutionApplication.getOrderId();
        Long applicationId = trainingInstitutionApplication.getApplicationId();
        
        // 检查机构名称是否重复申请
        TrainingInstitutionApplication existingByName = trainingInstitutionApplicationMapper
                .selectApplicationByOrderIdAndInstitutionName(orderId, trainingInstitutionApplication.getInstitutionName());
        if (existingByName != null && !existingByName.getApplicationId().equals(applicationId)) {
            return false;
        }
        
        // 检查联系电话是否重复申请
        TrainingInstitutionApplication existingByPhone = trainingInstitutionApplicationMapper
                .selectApplicationByOrderIdAndPhone(orderId, trainingInstitutionApplication.getContactPhone());
        if (existingByPhone != null && !existingByPhone.getApplicationId().equals(applicationId)) {
            return false;
        }
        
        // 如果有用户ID，检查用户是否重复申请
        if (trainingInstitutionApplication.getUserId() != null) {
            TrainingInstitutionApplication existingByUser = trainingInstitutionApplicationMapper
                    .selectApplicationByOrderIdAndUserId(orderId, trainingInstitutionApplication.getUserId());
            if (existingByUser != null && !existingByUser.getApplicationId().equals(applicationId)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 根据申请状态统计数量
     * 
     * @param status 申请状态
     * @return 数量
     */
    @Override
    public int countByStatus(String status)
    {
        return trainingInstitutionApplicationMapper.countByStatus(status);
    }

    /**
     * 获取待审核的申请列表
     * 
     * @return 待审核申请列表
     */
    @Override
    public List<TrainingInstitutionApplication> selectPendingApplications()
    {
        return trainingInstitutionApplicationMapper.selectPendingApplications();
    }

    /**
     * 获取当前用户的申请列表
     * 
     * @param userId 用户ID
     * @return 申请列表
     */
    @Override
    public List<TrainingInstitutionApplication> getMyApplications(Long userId)
    {
        TrainingInstitutionApplication queryApplication = new TrainingInstitutionApplication();
        queryApplication.setUserId(userId);
        return trainingInstitutionApplicationMapper.selectTrainingInstitutionApplicationList(queryApplication);
    }
}
