package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 政策信息对象 policy_info
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@TableName("policy_info")
public class PolicyInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 政策ID */
    @TableId(type = IdType.AUTO)
    private Long policyId;

    /** 政策名称 */
    @Excel(name = "政策名称")
    @NotBlank(message = "政策名称不能为空")
    @Size(min = 0, max = 200, message = "政策名称不能超过200个字符")
    private String policyName;

    /** 政策描述 */
    @Excel(name = "政策描述")
    private String policyDescription;

    /** 政策类型 */
    @Excel(name = "政策类型")
    @Size(min = 0, max = 50, message = "政策类型不能超过50个字符")
    private String policyType;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField("del_flag")
    private String delFlag;

    /** 创建者ID */
    @TableField("create_id")
    private Long createId;

    /** 更新者ID */
    @TableField("update_id")
    private Long updateId;

    public void setPolicyId(Long policyId) 
    {
        this.policyId = policyId;
    }

    public Long getPolicyId() 
    {
        return policyId;
    }

    public void setPolicyName(String policyName) 
    {
        this.policyName = policyName;
    }

    public String getPolicyName() 
    {
        return policyName;
    }

    public void setPolicyDescription(String policyDescription) 
    {
        this.policyDescription = policyDescription;
    }

    public String getPolicyDescription() 
    {
        return policyDescription;
    }

    public void setPolicyType(String policyType) 
    {
        this.policyType = policyType;
    }

    public String getPolicyType() 
    {
        return policyType;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setCreateId(Long createId) 
    {
        this.createId = createId;
    }

    public Long getCreateId() 
    {
        return createId;
    }

    public void setUpdateId(Long updateId) 
    {
        this.updateId = updateId;
    }

    public Long getUpdateId() 
    {
        return updateId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("policyId", getPolicyId())
            .append("policyName", getPolicyName())
            .append("policyDescription", getPolicyDescription())
            .append("policyType", getPolicyType())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createId", getCreateId())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
