<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.JobPostingMapper">

    <!-- 核心匹配优化版 ResultMap -->
    <resultMap type="JobPosting" id="JobPostingResult">
        <result property="jobId"                    column="job_id"                    />
        <result property="jobTitle"                 column="job_title"                 />
        <result property="jobDescription"           column="job_description"           />
        <result property="jobType"                  column="job_type"                  />
        <result property="jobCategory"              column="job_category"              />
        <result property="workLocation"             column="work_location"             />
        <result property="salaryType"               column="salary_type"               />
        <result property="salaryMin"                column="salary_min"                />
        <result property="salaryMax"                column="salary_max"                />
        <result property="educationRequired"        column="education_required"        />
        <result property="experienceRequired"       column="experience_required"       />
        <result property="workHoursPerDay"          column="work_hours_per_day"        />
        <result property="workDaysPerWeek"          column="work_days_per_week"        />
        <result property="startDate"                column="start_date"                />
        <result property="endDate"                  column="end_date"                  />
        <result property="contactPerson"            column="contact_person"            />
        <result property="contactPhone"             column="contact_phone"             />
        <result property="companyName"              column="company_name"              />
        <result property="urgencyLevel"             column="urgency_level"             />
        <result property="positionsAvailable"       column="positions_available"       />
        <result property="positionsFilled"          column="positions_filled"          />
        <result property="status"                   column="status"                    />
        <result property="viewCount"                column="view_count"                />
        <result property="applicationCount"         column="application_count"         />
        <result property="publisherUserId"          column="publisher_user_id"         />
        <result property="isVerified"               column="is_verified"               />
        <result property="featured"                 column="featured"                  />
        <result property="createId"                 column="create_id"                 />
        <result property="createTime"               column="create_time"               />
        <result property="updateId"                 column="update_id"                 />
        <result property="updateTime"               column="update_time"               />
        <result property="delFlag"                  column="del_flag"                  />
        <result property="remark"                   column="remark"                    />
        <!-- 关联查询字段 -->
        <result property="publisherUserName"        column="publisher_user_name"       />
        <result property="publisherNickName"        column="publisher_nick_name"       />
        <result property="publisherPhone"           column="publisher_phone"           />
    </resultMap>

    <!-- 核心匹配优化版 SQL 片段 -->
    <sql id="selectJobPostingVo">
        select job_id, job_title, job_description, job_type, job_category, work_location,
               salary_type, salary_min, salary_max, education_required, experience_required,
               work_hours_per_day, work_days_per_week, start_date, end_date,
               contact_person, contact_phone, company_name, urgency_level,
               positions_available, positions_filled, status, view_count, application_count,
               publisher_user_id, is_verified, featured,
               create_id, create_time, update_id, update_time, del_flag, remark
        from job_posting
    </sql>

    <!-- 核心匹配优化版查询列表 -->
    <select id="selectJobPostingList" parameterType="JobPosting" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        <where>
            del_flag = '0'
            <if test="jobTitle != null and jobTitle != ''"> and job_title like concat('%', #{jobTitle}, '%')</if>
            <if test="jobType != null and jobType != ''"> and job_type = #{jobType}</if>
            <if test="jobCategory != null and jobCategory != ''"> and job_category = #{jobCategory}</if>
            <if test="salaryType != null and salaryType != ''"> and salary_type = #{salaryType}</if>
            <if test="educationRequired != null and educationRequired != ''"> and education_required = #{educationRequired}</if>
            <if test="workLocation != null and workLocation != ''"> and work_location like concat('%', #{workLocation}, '%')</if>
            <if test="companyName != null and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="urgencyLevel != null and urgencyLevel != ''"> and urgency_level = #{urgencyLevel}</if>
            <if test="isVerified != null"> and is_verified = #{isVerified}</if>
            <if test="featured != null"> and featured = #{featured}</if>
            <if test="publisherUserId != null"> and publisher_user_id = #{publisherUserId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectJobPostingByJobId" parameterType="Long" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where job_id = #{jobId} and del_flag = '0'
    </select>

    <select id="selectMyJobPostingList" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        <where>
            del_flag = '0' and publisher_user_id = #{publisherUserId}
            <if test="jobPosting.jobTitle != null and jobPosting.jobTitle != ''"> and job_title like concat('%', #{jobPosting.jobTitle}, '%')</if>
            <if test="jobPosting.jobType != null and jobPosting.jobType != ''"> and job_type = #{jobPosting.jobType}</if>
            <if test="jobPosting.jobCategory != null and jobPosting.jobCategory != ''"> and job_category = #{jobPosting.jobCategory}</if>
            <if test="jobPosting.status != null and jobPosting.status != ''"> and status = #{jobPosting.status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectPublishedJobPostingList" parameterType="JobPosting" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        <where>
            del_flag = '0' and status = 'published'
            <if test="jobTitle != null and jobTitle != ''"> and job_title like concat('%', #{jobTitle}, '%')</if>
            <if test="jobType != null and jobType != ''"> and job_type = #{jobType}</if>
            <if test="jobCategory != null and jobCategory != ''"> and job_category = #{jobCategory}</if>
            <if test="salaryType != null and salaryType != ''"> and salary_type = #{salaryType}</if>
            <if test="educationRequired != null and educationRequired != ''"> and education_required = #{educationRequired}</if>
            <if test="workLocation != null and workLocation != ''"> and work_location like concat('%', #{workLocation}, '%')</if>
            <if test="companyName != null and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="urgencyLevel != null and urgencyLevel != ''"> and urgency_level = #{urgencyLevel}</if>
        </where>
        order by featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <select id="selectHotJobPostingList" parameterType="Integer" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where del_flag = '0' and status = 'published'
        order by view_count desc, application_count desc
        limit #{limit}
    </select>

    <select id="selectFeaturedJobPostingList" parameterType="Integer" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where del_flag = '0' and status = 'published' and featured = 1
        order by create_time desc
        limit #{limit}
    </select>

    <select id="selectUrgentJobPostingList" parameterType="Integer" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where del_flag = '0' and status = 'published' and urgency_level = 'urgent'
        order by create_time desc
        limit #{limit}
    </select>

    <select id="selectJobPostingByKeyword" parameterType="String" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where del_flag = '0' and status = 'published'
        and (job_title like concat('%', #{keyword}, '%')
        or job_description like concat('%', #{keyword}, '%')
        or work_location like concat('%', #{keyword}, '%')
        or company_name like concat('%', #{keyword}, '%')
        or skills_required like concat('%', #{keyword}, '%'))
        order by create_time desc
    </select>

    <select id="selectJobPostingExpiringSoon" parameterType="Integer" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where del_flag = '0' and status = 'published'
        and application_deadline is not null
        and application_deadline between now() and date_add(now(), interval #{days} day)
        order by application_deadline asc
    </select>

    <select id="selectJobPostingDetailByJobId" parameterType="Long" resultMap="JobPostingResult">
        select j.*, u.user_name as publisher_user_name
        from job_posting j
                 left join sys_user u on j.publisher_user_id = u.user_id
        where j.job_id = #{jobId} and j.del_flag = '0'
    </select>

    <!-- 核心匹配优化版插入 -->
    <insert id="insertJobPosting" parameterType="JobPosting" useGeneratedKeys="true" keyProperty="jobId">
        insert into job_posting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobTitle != null and jobTitle != ''">job_title,</if>
            <if test="jobDescription != null">job_description,</if>
            <if test="jobType != null and jobType != ''">job_type,</if>
            <if test="jobCategory != null and jobCategory != ''">job_category,</if>
            <if test="workLocation != null and workLocation != ''">work_location,</if>
            <if test="salaryType != null and salaryType != ''">salary_type,</if>
            <if test="salaryMin != null">salary_min,</if>
            <if test="salaryMax != null">salary_max,</if>
            <if test="educationRequired != null">education_required,</if>
            <if test="experienceRequired != null">experience_required,</if>
            <if test="workHoursPerDay != null">work_hours_per_day,</if>
            <if test="workDaysPerWeek != null">work_days_per_week,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="companyName != null">company_name,</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">urgency_level,</if>
            <if test="positionsAvailable != null">positions_available,</if>
            <if test="positionsFilled != null">positions_filled,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="applicationCount != null">application_count,</if>
            <if test="publisherUserId != null">publisher_user_id,</if>
            <if test="isVerified != null">is_verified,</if>
            <if test="featured != null">featured,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobTitle != null and jobTitle != ''">#{jobTitle},</if>
            <if test="jobDescription != null">#{jobDescription},</if>
            <if test="jobType != null and jobType != ''">#{jobType},</if>
            <if test="jobCategory != null and jobCategory != ''">#{jobCategory},</if>
            <if test="workLocation != null and workLocation != ''">#{workLocation},</if>
            <if test="salaryType != null and salaryType != ''">#{salaryType},</if>
            <if test="salaryMin != null">#{salaryMin},</if>
            <if test="salaryMax != null">#{salaryMax},</if>
            <if test="educationRequired != null">#{educationRequired},</if>
            <if test="experienceRequired != null">#{experienceRequired},</if>
            <if test="workHoursPerDay != null">#{workHoursPerDay},</if>
            <if test="workDaysPerWeek != null">#{workDaysPerWeek},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">#{urgencyLevel},</if>
            <if test="positionsAvailable != null">#{positionsAvailable},</if>
            <if test="positionsFilled != null">#{positionsFilled},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="applicationCount != null">#{applicationCount},</if>
            <if test="publisherUserId != null">#{publisherUserId},</if>
            <if test="isVerified != null">#{isVerified},</if>
            <if test="featured != null">#{featured},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <!-- 核心匹配优化版更新 -->
    <update id="updateJobPosting" parameterType="JobPosting">
        update job_posting
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobTitle != null and jobTitle != ''">job_title = #{jobTitle},</if>
            <if test="jobDescription != null">job_description = #{jobDescription},</if>
            <if test="jobType != null and jobType != ''">job_type = #{jobType},</if>
            <if test="jobCategory != null and jobCategory != ''">job_category = #{jobCategory},</if>
            <if test="workLocation != null and workLocation != ''">work_location = #{workLocation},</if>
            <if test="salaryType != null and salaryType != ''">salary_type = #{salaryType},</if>
            <if test="salaryMin != null">salary_min = #{salaryMin},</if>
            <if test="salaryMax != null">salary_max = #{salaryMax},</if>
            <if test="educationRequired != null">education_required = #{educationRequired},</if>
            <if test="experienceRequired != null">experience_required = #{experienceRequired},</if>
            <if test="workHoursPerDay != null">work_hours_per_day = #{workHoursPerDay},</if>
            <if test="workDaysPerWeek != null">work_days_per_week = #{workDaysPerWeek},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">urgency_level = #{urgencyLevel},</if>
            <if test="positionsAvailable != null">positions_available = #{positionsAvailable},</if>
            <if test="positionsFilled != null">positions_filled = #{positionsFilled},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="applicationCount != null">application_count = #{applicationCount},</if>
            <if test="publisherUserId != null">publisher_user_id = #{publisherUserId},</if>
            <if test="isVerified != null">is_verified = #{isVerified},</if>
            <if test="featured != null">featured = #{featured},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where job_id = #{jobId}
    </update>

    <delete id="deleteJobPostingByJobId" parameterType="Long">
        update job_posting set del_flag = '2' where job_id = #{jobId}
    </delete>

    <delete id="deleteJobPostingByJobIds" parameterType="String">
        update job_posting set del_flag = '2' where job_id in
        <foreach item="jobId" collection="array" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </delete>

    <!-- 统计查询方法 -->
    <select id="selectJobPostingCountByCategory" resultType="java.util.Map">
        select job_category as category, count(*) as count
        from job_posting
        where del_flag = '0' and status = 'published'
        group by job_category
        order by count desc
    </select>

    <select id="selectJobPostingCountByLocation" resultType="java.util.Map">
        select work_location as location, count(*) as count
        from job_posting
        where del_flag = '0' and status = 'published'
        group by work_location
        order by count desc
    </select>

    <select id="selectJobPostingCountBySalaryRange" resultType="java.util.Map">
        select
        case
        when salary_min is null then '面议'
        when salary_min &lt; 3000 then '3000以下'
        when salary_min &lt; 5000 then '3000-5000'
        when salary_min &lt; 8000 then '5000-8000'
        when salary_min &lt; 12000 then '8000-12000'
        else '12000以上'
        end as salary_range,
        count(*) as count
        from job_posting
        where del_flag = '0' and status = 'published'
        group by salary_range
        order by count desc
    </select>

    <select id="selectSimilarJobPostingList" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where del_flag = '0' and status = 'published' and job_id != #{jobPosting.jobId}
        <if test="jobPosting.jobCategory != null and jobPosting.jobCategory != ''">
            and job_category = #{jobPosting.jobCategory}
        </if>
        <if test="jobPosting.workLocation != null and jobPosting.workLocation != ''">
            and work_location like concat('%', #{jobPosting.workLocation}, '%')
        </if>
        order by create_time desc
        limit #{limit}
    </select>

    <select id="selectJobPostingByMatchParams" parameterType="java.util.Map" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        <where>
            del_flag = '0' and status = #{status}
            <if test="workLocation != null and workLocation != ''">
                and work_location like concat('%', #{workLocation}, '%')
            </if>
            <if test="workCategories != null and workCategories != ''">
                and job_category in
                <foreach item="category" collection="workCategories.split(',')" open="(" separator="," close=")">
                    #{category}
                </foreach>
            </if>
            <if test="salaryMin != null">
                and (salary_min is null or salary_min &gt;= #{salaryMin})
            </if>
            <if test="salaryMax != null">
                and (salary_max is null or salary_max &lt;= #{salaryMax})
            </if>
            <if test="salaryType != null and salaryType != ''">
                and salary_type = #{salaryType}
            </if>
            <if test="jobTypes != null and jobTypes != ''">
                and job_type in
                <foreach item="type" collection="jobTypes.split(',')" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="skills != null and skills != ''">
                and skills_required like concat('%', #{skills}, '%')
            </if>
            <if test="availabilityStartDate != null">
                and (start_date is null or start_date &gt;= #{availabilityStartDate})
            </if>
            <if test="availabilityEndDate != null">
                and (end_date is null or end_date &lt;= #{availabilityEndDate})
            </if>
        </where>
        order by create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <update id="updateJobPostingViewCount" parameterType="Long">
        update job_posting set view_count = IFNULL(view_count, 0) + 1 where job_id = #{jobId}
    </update>

    <update id="updateJobPostingApplicationCount" parameterType="Long">
        update job_posting set application_count = IFNULL(application_count, 0) + 1 where job_id = #{jobId}
    </update>

    <update id="updateJobPostingPositionsFilled">
        update job_posting set positions_filled = #{positionsFilled} where job_id = #{jobId}
    </update>

    <update id="batchUpdateJobPostingStatus">
        update job_posting set status = #{status} where job_id in
        <foreach item="jobId" collection="jobIds" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </update>

    <select id="selectJobPostingStatistics" parameterType="Long" resultType="java.util.Map">
        select
        count(*) as total_count,
        sum(case when status = 'published' then 1 else 0 end) as published_count,
        sum(case when status = 'draft' then 1 else 0 end) as draft_count,
        sum(case when status = 'paused' then 1 else 0 end) as paused_count,
        sum(case when status = 'closed' then 1 else 0 end) as closed_count,
        sum(case when status = 'completed' then 1 else 0 end) as completed_count,
        sum(IFNULL(view_count, 0)) as total_views,
        sum(IFNULL(application_count, 0)) as total_applications,
        sum(IFNULL(positions_available, 0)) as total_positions,
        sum(IFNULL(positions_filled, 0)) as total_filled
        from job_posting
        where del_flag = '0'
        <if test="publisherUserId != null">
            and publisher_user_id = #{publisherUserId}
        </if>
    </select>

    <!-- ==================== 核心匹配优化查询方法 ==================== -->

    <!-- 基于核心字段搜索招聘信息 -->
    <select id="selectJobPostingByCoreFields" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        <where>
            del_flag = '0' and status = 'published'
            <if test="jobType != null and jobType != ''"> and job_type = #{jobType}</if>
            <if test="jobCategory != null and jobCategory != ''"> and job_category = #{jobCategory}</if>
            <if test="salaryType != null and salaryType != ''"> and salary_type = #{salaryType}</if>
            <if test="educationRequired != null and educationRequired != ''"> and education_required = #{educationRequired}</if>
            <if test="keyword != null and keyword != ''"> and job_title like concat('%', #{keyword}, '%')</if>
        </where>
        order by featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <!-- 根据工作类型查询招聘信息 -->
    <select id="selectJobPostingByJobType" parameterType="String" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where del_flag = '0' and status = 'published' and job_type = #{jobType}
        order by featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <!-- 根据工作类别查询招聘信息 -->
    <select id="selectJobPostingByJobCategory" parameterType="String" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where del_flag = '0' and status = 'published' and job_category = #{jobCategory}
        order by featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <!-- 根据薪资类型查询招聘信息 -->
    <select id="selectJobPostingBySalaryType" parameterType="String" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where del_flag = '0' and status = 'published' and salary_type = #{salaryType}
        order by featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <!-- 根据学历要求查询招聘信息 -->
    <select id="selectJobPostingByEducation" parameterType="String" resultMap="JobPostingResult">
        <include refid="selectJobPostingVo"/>
        where del_flag = '0' and status = 'published' and education_required = #{educationRequired}
        order by featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <!-- 获取所有工作类型列表 -->
    <select id="selectAllJobTypes" resultType="String">
        select distinct job_type from job_posting
        where del_flag = '0' and job_type is not null and job_type != ''
        order by job_type
    </select>

    <!-- 获取所有工作类别列表 -->
    <select id="selectAllJobCategories" resultType="String">
        select distinct job_category from job_posting
        where del_flag = '0' and job_category is not null and job_category != ''
        order by job_category
    </select>

    <!-- 获取所有薪资类型列表 -->
    <select id="selectAllSalaryTypes" resultType="String">
        select distinct salary_type from job_posting
        where del_flag = '0' and salary_type is not null and salary_type != ''
        order by salary_type
    </select>

    <!-- 获取所有学历要求列表 -->
    <select id="selectAllEducationRequirements" resultType="String">
        select distinct education_required from job_posting
        where del_flag = '0' and education_required is not null and education_required != ''
        order by education_required
    </select>
</mapper>
