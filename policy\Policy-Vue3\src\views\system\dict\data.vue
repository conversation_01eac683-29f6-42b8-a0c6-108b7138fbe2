<template>
    <div class="dict-data-container app-container">
        <!-- 使用 TableList 组件 -->
        <TableList v-if="isTableReady" :columns="tableColumns" :data="tableData" :loading="tableLoading"
            :showIndex="true" :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作"
            operationWidth="180" :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
            :defaultPage="{ pageSize: pageSize, currentPage: currentPage, total: total }"
            @current-change="handleCurrentChange" @size-change="handleSizeChange"
            @selection-change="handleSelectionChange">

            <!-- 左侧按钮插槽 -->
            <template #menu-left>
                <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['system:dict:add']">
                    新 增
                </el-button>
                <el-button type="warning" class="custom-btn" @click="handleExport" v-hasPermi="['system:dict:export']">
                    导 出
                </el-button>
                <el-button type="warning" class="custom-btn" plain @click="handleClose">
                    关 闭
                </el-button>
            </template>

            <!-- 字典标签列插槽 - 显示样式 -->
            <template #dictLabel="{ row }">
                <span
                    v-if="(row.listClass == '' || row.listClass == 'default') && (row.cssClass == '' || row.cssClass == null)">
                    {{ row.dictLabel }}
                </span>
                <el-tag v-else :type="row.listClass == 'primary' ? '' : row.listClass" :class="row.cssClass">
                    {{ row.dictLabel }}
                </el-tag>
            </template>

            <!-- 操作列插槽 -->
            <template #menu="{ row }">
                <div class="operation-btns">
                    <el-button type="primary" link @click="handleView(row)">查看</el-button>
                    <el-button type="primary" link @click="handleEdit(row)"
                        v-hasPermi="['system:dict:edit']">编辑</el-button>
                    <el-button type="danger" link @click="handleDelete(row)"
                        v-hasPermi="['system:dict:remove']">删除</el-button>
                </div>
            </template>
        </TableList>

        <div v-else class="loading-placeholder">
            <el-empty description="正在加载表格配置..."></el-empty>
        </div>

        <!-- 表单弹窗组件 -->
        <DictFormDialog ref="dictFormDialogRef" :formFields="formFields" :formOption="formOption"
            @submit="handleFormSubmit" @cancel="handleFormCancel" />
    </div>
</template>

<script setup name="Data">
import { ref, reactive, onMounted, getCurrentInstance, computed } from 'vue';
import { useRoute } from 'vue-router';
import { createDictDataTableOption } from "@/const/dict/index";
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils";
import { optionselect as getDictOptionselect, getType } from "@/api/system/dict/type";
import { listData, getData, delData, addData, updateData } from "@/api/system/dict/data";
import useDictStore from '@/store/modules/dict';
import TableList from '@/components/TableList/index.vue';
import DictFormDialog from './DictFormDialog.vue';

const { proxy } = getCurrentInstance();
const route = useRoute();
const tableColumns = ref([]);
const searchableColumns = ref([]);
const tableLoading = ref(false);
const isTableReady = ref(false);
const formOption = ref({
    dialogWidth: '700px',
    dialogHeight: '65vh'
});
const tableListRef = ref(null);
const dictFormDialogRef = ref(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref({});
const defaultDictType = ref("");
const typeOptions = ref([]);

// 选择相关
const selectedRows = ref([]);
const single = ref(true);
const multiple = ref(true);

// 表格数据
const tableData = ref([]);

// 表单字段配置
const formFields = ref([]);

// 初始化时获取数据
onMounted(async () => {
    await initializeConfig();
    getTypeList();
    getTypes(route.params && route.params.dictId);
});

// 初始化配置
const initializeConfig = async () => {
    try {
        // 获取基础配置
        const baseOption = createDictDataTableOption(proxy);

        // 使用工具类获取合并后的配置
        const mergedConfig = await getCoSyncColumn({
            baseOption,
            proxy
        });

        // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
        const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

        // 设置表格和搜索配置
        tableColumns.value = extractedTableColumns;
        searchableColumns.value = searchColumns;

        // 设置表单字段配置
        formFields.value = extractedFormFields;

        // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
        formOption.value = {
            ...formOption.value, // 保留默认配置
            ...formOptions       // 使用从配置文件中提取的完整选项
        };

        isTableReady.value = true;
    } catch (error) {
        isTableReady.value = false;
        console.error('初始化配置失败:', error);
    }
};

/** 查询字典类型详细 */
function getTypes(dictId) {
    getType(dictId).then(response => {
        searchParams.value.dictType = response.data.dictType
        defaultDictType.value = response.data.dictType
        getList()
    })
}

/** 查询字典类型列表 */
function getTypeList() {
    getDictOptionselect().then(response => {
        typeOptions.value = response.data
    })
}

/** 查询字典数据列表 */
function getList() {
    loadData();
}

/** 取消按钮 */
function handleFormCancel() {
    // 表单取消处理
}

/** 新增按钮操作 */
function handleAdd() {
    const defaultData = {
        dictType: defaultDictType.value,
        listClass: "default",
        dictSort: 0,
        status: "0"
    };
    dictFormDialogRef.value?.openDialog('add', '新增字典数据', defaultData);
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    selectedRows.value = selection;
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 批量修改按钮操作 */
function handleUpdate() {
    if (selectedRows.value.length === 1) {
        handleEdit();
    } else {
        proxy.$modal.msgWarning("请选择一条记录进行修改");
    }
}

/** 处理表单提交事件 */
const handleFormSubmit = async (payload) => {
    try {
        if (payload.type === 'add') {
            await addData(payload.data);
            proxy.$modal.msgSuccess("添加成功");
        } else if (payload.type === 'edit') {
            await updateData(payload.data);
            proxy.$modal.msgSuccess("修改成功");
        }

        // 清除字典缓存
        useDictStore().removeDict(defaultDictType.value);

        // 通知子组件提交成功
        dictFormDialogRef.value?.onSubmitSuccess();
        loadData();
    } catch (error) {
        // 通知子组件提交失败
        dictFormDialogRef.value?.onSubmitError();
        console.error('提交失败:', error);
    }
};

/** 删除按钮操作 */
function handleDelete(row) {
    const dictCodes = row ? row.dictCode : selectedRows.value.map(item => item.dictCode);
    const dictLabels = row ? row.dictLabel : selectedRows.value.map(item => item.dictLabel).join("、");

    proxy.$modal.confirm('是否确认删除字典数据【' + dictLabels + '】？').then(() => {
        return delData(dictCodes);
    }).then(() => {
        loadData();
        proxy.$modal.msgSuccess("删除成功");
        useDictStore().removeDict(defaultDictType.value);
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    const queryParams = {
        ...searchParams.value
    };
    proxy.download("system/dict/data/export", queryParams, `dict_data_${new Date().getTime()}.xlsx`);
}

/** 返回按钮操作 */
function handleClose() {
    const obj = { path: "/system/dict" }
    proxy.$tab.closeOpenPage(obj)
}



// 加载表格数据
const loadData = () => {
    tableLoading.value = true;

    const queryParams = {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        ...searchParams.value
    };

    listData(queryParams).then(response => {
        tableData.value = response.rows;
        total.value = response.total;
        tableLoading.value = false;
        updateTablePagination();
    }).catch(() => {
        tableLoading.value = false;
    });
};

// 更新表格分页信息
const updateTablePagination = () => {
    if (tableListRef.value && tableListRef.value.page) {
        tableListRef.value.page.total = total.value;
        tableListRef.value.page.currentPage = currentPage.value;
        tableListRef.value.page.pageSize = pageSize.value;
    }
};

// 处理搜索
const handleSearch = (params) => {
    searchParams.value = { ...params, dictType: defaultDictType.value };
    currentPage.value = 1;
    loadData();
};

// 重置搜索
const resetSearch = () => {
    searchParams.value = { dictType: defaultDictType.value };
    currentPage.value = 1;
    loadData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
    currentPage.value = page;
    loadData();
};

// 处理每页条数变化
const handleSizeChange = (size) => {
    pageSize.value = size;
    currentPage.value = 1;
    loadData();
};

// 查看
const handleView = (row) => {
    dictFormDialogRef.value?.openDialog('view', '查看字典数据', row);
};

// 编辑
const handleEdit = (row) => {
    const dictCode = row ? row.dictCode : selectedRows.value[0];
    if (!dictCode) return;

    getData(dictCode).then(response => {
        dictFormDialogRef.value?.openDialog('edit', '编辑字典数据', response.data);
    });
};

</script>

<style lang="scss" scoped>
.dict-data-container {
    width: 100%;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

    :deep(.el-table) {
        margin-top: 15px;
        width: 100%;
        max-width: 100%;
    }

    :deep(.el-pagination) {
        justify-content: flex-end;
    }

    .loading-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 300px;
    }

    .operation-btns {
        display: flex;
        gap: 8px;
        justify-content: center;
    }
}
</style>
