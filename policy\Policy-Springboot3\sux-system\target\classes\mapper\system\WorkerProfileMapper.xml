<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.WorkerProfileMapper">

	<resultMap type="WorkerProfile" id="WorkerProfileResult">
		<!-- 核心匹配优化版字段映射 -->
		<result property="workerId"                 column="worker_id"                 />
		<result property="userId"                   column="user_id"                   />
		<result property="realName"                 column="real_name"                 />
		<result property="nickname"                 column="nickname"                  />
		<result property="gender"                   column="gender"                    />
		<result property="age"                      column="age"                       />
		<result property="phone"                    column="phone"                     />
		<result property="currentLocation"          column="current_location"          />
		<result property="educationLevel"           column="education_level"           />
		<result property="workExperienceYears"      column="work_experience_years"     />
		<result property="workCategories"           column="work_categories"           />
		<result property="jobTypesPreferred"        column="job_types_preferred"       />
		<result property="skills"                   column="skills"                    />
		<result property="salaryExpectationMin"     column="salary_expectation_min"    />
		<result property="salaryExpectationMax"     column="salary_expectation_max"    />
		<result property="salaryTypePreference"     column="salary_type_preference"    />
		<result property="availabilityStartDate"    column="availability_start_date"   />
		<result property="workDaysPerWeek"          column="work_days_per_week"        />
		<result property="workHoursPerDay"          column="work_hours_per_day"        />
		<result property="profilePhoto"             column="profile_photo"             />
		<result property="selfIntroduction"         column="self_introduction"         />
		<result property="ratingAverage"            column="rating_average"            />
		<result property="ratingCount"              column="rating_count"              />
		<result property="completedJobs"            column="completed_jobs"            />
		<result property="successRate"              column="success_rate"              />
		<result property="status"                   column="status"                    />
		<result property="isVerified"               column="is_verified"               />
		<result property="lastActiveTime"           column="last_active_time"          />
		<result property="createId"                 column="create_id"                 />
		<result property="createTime"               column="create_time"               />
		<result property="updateId"                 column="update_id"                 />
		<result property="updateTime"               column="update_time"               />
		<result property="delFlag"                  column="del_flag"                  />
		<result property="remark"                   column="remark"                    />
	</resultMap>

	<!-- 核心匹配优化版 SQL 片段 -->
	<sql id="selectWorkerProfileVo">
		select worker_id, user_id, real_name, nickname, gender, age, phone, current_location,
			   education_level, work_experience_years, work_categories, job_types_preferred, skills,
			   salary_expectation_min, salary_expectation_max, salary_type_preference,
			   availability_start_date, work_days_per_week, work_hours_per_day,
			   profile_photo, self_introduction, rating_average, rating_count, completed_jobs,
			   success_rate, status, is_verified, last_active_time,
			   create_id, create_time, update_id, update_time, del_flag, remark
		from worker_profile
	</sql>

	<select id="selectWorkerProfileList" parameterType="WorkerProfile" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		<where>
			del_flag = '0'
			<if test="realName != null and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
			<if test="nickname != null and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
			<if test="phone != null and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
			<if test="gender != null and gender != ''"> and gender = #{gender}</if>
			<if test="currentLocation != null and currentLocation != ''"> and current_location like concat('%', #{currentLocation}, '%')</if>
			<if test="educationLevel != null and educationLevel != ''"> and education_level = #{educationLevel}</if>
			<if test="status != null and status != ''"> and status = #{status}</if>
			<if test="isVerified != null"> and is_verified = #{isVerified}</if>
			<if test="userId != null"> and user_id = #{userId}</if>
			<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
				and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
			</if>
			<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
				and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
			</if>
		</where>
		order by create_time desc
	</select>

	<select id="selectWorkerProfileByWorkerId" parameterType="Long" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where worker_id = #{workerId} and del_flag = '0'
	</select>

	<select id="selectWorkerProfileByUserId" parameterType="Long" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where user_id = #{userId} and del_flag = '0'
	</select>

	<select id="selectActiveWorkerProfileList" parameterType="WorkerProfile" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		<where>
			del_flag = '0' and status = 'active'
			<if test="realName != null and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
			<if test="nickname != null and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
			<if test="currentLocation != null and currentLocation != ''"> and current_location like concat('%', #{currentLocation}, '%')</if>
			<if test="educationLevel != null and educationLevel != ''"> and education_level = #{educationLevel}</if>
		</where>
		order by last_active_time desc, create_time desc
	</select>

	<select id="selectVerifiedWorkerProfileList" parameterType="WorkerProfile" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		<where>
			del_flag = '0' and is_verified = 1
			<if test="realName != null and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
			<if test="nickname != null and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
			<if test="currentLocation != null and currentLocation != ''"> and current_location like concat('%', #{currentLocation}, '%')</if>
			<if test="status != null and status != ''"> and status = #{status}</if>
		</where>
		order by verification_time desc, create_time desc
	</select>

	<select id="selectHighRatedWorkerProfileList" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active' and rating_average &gt;= #{minRating}
		order by rating_average desc, rating_count desc
		limit #{limit}
	</select>

	<select id="selectExperiencedWorkerProfileList" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active' and work_experience_years &gt;= #{minExperience}
		order by work_experience_years desc, rating_average desc
		limit #{limit}
	</select>

	<select id="selectRecommendedWorkerProfileList" parameterType="Integer" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active' and is_verified = 1
		order by rating_average desc, completed_jobs desc, success_rate desc
		limit #{limit}
	</select>

	<select id="selectNewWorkerProfileList" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active'
		and create_time &gt;= date_sub(now(), interval #{days} day)
		order by create_time desc
		limit #{limit}
	</select>

	<select id="selectWorkerProfileByKeyword" parameterType="String" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active'
		and (real_name like concat('%', #{keyword}, '%')
		or nickname like concat('%', #{keyword}, '%')
		or current_location like concat('%', #{keyword}, '%')
		or skills like concat('%', #{keyword}, '%')
		or work_categories like concat('%', #{keyword}, '%'))
		order by create_time desc
	</select>

	<select id="selectWorkerProfileAvailableSoon" parameterType="Integer" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active'
		and availability_start_date is not null
		and availability_start_date between now() and date_add(now(), interval #{days} day)
		order by availability_start_date asc
	</select>

	<select id="selectWorkerProfileDetailByWorkerId" parameterType="Long" resultMap="WorkerProfileResult">
		select w.*, u.user_name
		from worker_profile w
				 left join sys_user u on w.user_id = u.user_id
		where w.worker_id = #{workerId} and w.del_flag = '0'
	</select>

	<insert id="insertWorkerProfile" parameterType="WorkerProfile" useGeneratedKeys="true" keyProperty="workerId">
		insert into worker_profile
		<!-- 核心匹配优化版插入字段 -->
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="userId != null">user_id,</if>
			<if test="realName != null and realName != ''">real_name,</if>
			<if test="nickname != null">nickname,</if>
			<if test="gender != null and gender != ''">gender,</if>
			<if test="age != null">age,</if>
			<if test="phone != null and phone != ''">phone,</if>
			<if test="currentLocation != null">current_location,</if>
			<if test="educationLevel != null">education_level,</if>
			<if test="workExperienceYears != null">work_experience_years,</if>
			<if test="workCategories != null">work_categories,</if>
			<if test="jobTypesPreferred != null">job_types_preferred,</if>
			<if test="skills != null">skills,</if>
			<if test="salaryTypePreference != null">salary_type_preference,</if>
			<if test="salaryExpectationMin != null">salary_expectation_min,</if>
			<if test="salaryExpectationMax != null">salary_expectation_max,</if>
			<if test="availabilityStartDate != null">availability_start_date,</if>
			<if test="workDaysPerWeek != null">work_days_per_week,</if>
			<if test="workHoursPerDay != null">work_hours_per_day,</if>
			<if test="profilePhoto != null">profile_photo,</if>
			<if test="selfIntroduction != null">self_introduction,</if>
			<if test="ratingAverage != null">rating_average,</if>
			<if test="ratingCount != null">rating_count,</if>
			<if test="completedJobs != null">completed_jobs,</if>
			<if test="successRate != null">success_rate,</if>
			<if test="status != null and status != ''">status,</if>
			<if test="isVerified != null">is_verified,</if>
			<if test="lastActiveTime != null">last_active_time,</if>
			<if test="createId != null">create_id,</if>
			<if test="createTime != null">create_time,</if>
			<if test="updateId != null">update_id,</if>
			<if test="updateTime != null">update_time,</if>
			<if test="delFlag != null and delFlag != ''">del_flag,</if>
			<if test="remark != null">remark,</if>
		</trim>
		<!-- 核心匹配优化版插入值 -->
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="userId != null">#{userId},</if>
			<if test="realName != null and realName != ''">#{realName},</if>
			<if test="nickname != null">#{nickname},</if>
			<if test="gender != null and gender != ''">#{gender},</if>
			<if test="age != null">#{age},</if>
			<if test="phone != null and phone != ''">#{phone},</if>
			<if test="currentLocation != null">#{currentLocation},</if>
			<if test="educationLevel != null">#{educationLevel},</if>
			<if test="workExperienceYears != null">#{workExperienceYears},</if>
			<if test="workCategories != null">#{workCategories},</if>
			<if test="jobTypesPreferred != null">#{jobTypesPreferred},</if>
			<if test="skills != null">#{skills},</if>
			<if test="salaryTypePreference != null">#{salaryTypePreference},</if>
			<if test="salaryExpectationMin != null">#{salaryExpectationMin},</if>
			<if test="salaryExpectationMax != null">#{salaryExpectationMax},</if>
			<if test="availabilityStartDate != null">#{availabilityStartDate},</if>
			<if test="workDaysPerWeek != null">#{workDaysPerWeek},</if>
			<if test="workHoursPerDay != null">#{workHoursPerDay},</if>
			<if test="profilePhoto != null">#{profilePhoto},</if>
			<if test="selfIntroduction != null">#{selfIntroduction},</if>
			<if test="ratingAverage != null">#{ratingAverage},</if>
			<if test="ratingCount != null">#{ratingCount},</if>
			<if test="completedJobs != null">#{completedJobs},</if>
			<if test="successRate != null">#{successRate},</if>
			<if test="status != null and status != ''">#{status},</if>
			<if test="isVerified != null">#{isVerified},</if>
			<if test="lastActiveTime != null">#{lastActiveTime},</if>
			<if test="createId != null">#{createId},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateId != null">#{updateId},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="delFlag != null and delFlag != ''">#{delFlag},</if>
			<if test="remark != null">#{remark},</if>
		</trim>
	</insert>

	<!-- 核心匹配优化版更新 -->
	<update id="updateWorkerProfile" parameterType="WorkerProfile">
		update worker_profile
		<trim prefix="SET" suffixOverrides=",">
			<if test="userId != null">user_id = #{userId},</if>
			<if test="realName != null and realName != ''">real_name = #{realName},</if>
			<if test="nickname != null">nickname = #{nickname},</if>
			<if test="gender != null and gender != ''">gender = #{gender},</if>
			<if test="age != null">age = #{age},</if>
			<if test="phone != null and phone != ''">phone = #{phone},</if>
			<if test="currentLocation != null">current_location = #{currentLocation},</if>
			<if test="educationLevel != null">education_level = #{educationLevel},</if>
			<if test="workExperienceYears != null">work_experience_years = #{workExperienceYears},</if>
			<if test="workCategories != null">work_categories = #{workCategories},</if>
			<if test="jobTypesPreferred != null">job_types_preferred = #{jobTypesPreferred},</if>
			<if test="skills != null">skills = #{skills},</if>
			<if test="salaryTypePreference != null">salary_type_preference = #{salaryTypePreference},</if>
			<if test="salaryExpectationMin != null">salary_expectation_min = #{salaryExpectationMin},</if>
			<if test="salaryExpectationMax != null">salary_expectation_max = #{salaryExpectationMax},</if>
			<if test="availabilityStartDate != null">availability_start_date = #{availabilityStartDate},</if>
			<if test="workDaysPerWeek != null">work_days_per_week = #{workDaysPerWeek},</if>
			<if test="workHoursPerDay != null">work_hours_per_day = #{workHoursPerDay},</if>
			<if test="profilePhoto != null">profile_photo = #{profilePhoto},</if>
			<if test="selfIntroduction != null">self_introduction = #{selfIntroduction},</if>
			<if test="ratingAverage != null">rating_average = #{ratingAverage},</if>
			<if test="ratingCount != null">rating_count = #{ratingCount},</if>
			<if test="completedJobs != null">completed_jobs = #{completedJobs},</if>
			<if test="successRate != null">success_rate = #{successRate},</if>
			<if test="status != null and status != ''">status = #{status},</if>
			<if test="isVerified != null">is_verified = #{isVerified},</if>
			<if test="lastActiveTime != null">last_active_time = #{lastActiveTime},</if>
			<if test="updateId != null">update_id = #{updateId},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
			<if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
			<if test="remark != null">remark = #{remark},</if>
		</trim>
		where worker_id = #{workerId}
	</update>

	<delete id="deleteWorkerProfileByWorkerId" parameterType="Long">
		update worker_profile set del_flag = '2' where worker_id = #{workerId}
	</delete>

	<delete id="deleteWorkerProfileByWorkerIds" parameterType="String">
		update worker_profile set del_flag = '2' where worker_id in
		<foreach item="workerId" collection="array" open="(" separator="," close=")">
			#{workerId}
		</foreach>
	</delete>

	<update id="updateWorkerProfileLastActiveTime" parameterType="Long">
		update worker_profile set last_active_time = now() where worker_id = #{workerId}
	</update>

	<update id="updateWorkerProfileRating">
		update worker_profile
		set rating_average = #{ratingAverage}, rating_count = #{ratingCount}
		where worker_id = #{workerId}
	</update>

	<update id="updateWorkerProfileJobStats">
		update worker_profile
		set completed_jobs = #{completedJobs}, success_rate = #{successRate}
		where worker_id = #{workerId}
	</update>

	<update id="batchUpdateWorkerProfileStatus">
		update worker_profile set status = #{status} where worker_id in
		<foreach item="workerId" collection="workerIds" open="(" separator="," close=")">
			#{workerId}
		</foreach>
	</update>

	<!-- 统计查询方法 -->
	<select id="selectWorkerProfileCountByCategory" resultType="java.util.Map">
		select
			SUBSTRING_INDEX(SUBSTRING_INDEX(work_categories, ',', numbers.n), ',', -1) as category,
			count(*) as count
		from worker_profile
			cross join (
			select 1 n union all select 2 union all select 3 union all select 4 union all select 5
			) numbers
		where del_flag = '0' and status = 'active'
		  and char_length(work_categories) - char_length(replace(work_categories, ',', '')) &gt;= numbers.n - 1
		  and work_categories is not null and work_categories != ''
		group by category
		order by count desc
	</select>

	<select id="selectWorkerProfileCountByLocation" resultType="java.util.Map">
		select current_location as location, count(*) as count
		from worker_profile
		where del_flag = '0' and status = 'active' and current_location is not null
		group by current_location
		order by count desc
	</select>

	<select id="selectWorkerProfileCountByEducation" resultType="java.util.Map">
		select education_level as education, count(*) as count
		from worker_profile
		where del_flag = '0' and status = 'active' and education_level is not null
		group by education_level
		order by count desc
	</select>

	<select id="selectWorkerProfileCountByExperience" resultType="java.util.Map">
		select
		case
		when work_experience_years is null or work_experience_years = 0 then '无经验'
		when work_experience_years &lt; 2 then '1-2年'
		when work_experience_years &lt; 5 then '2-5年'
		when work_experience_years &lt; 10 then '5-10年'
		else '10年以上'
		end as experience_range,
		count(*) as count
		from worker_profile
		where del_flag = '0' and status = 'active'
		group by experience_range
		order by count desc
	</select>

	<select id="selectWorkerProfileBySkills" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active'
		<if test="skills != null and skills.size() > 0">
			and (
			<foreach item="skill" collection="skills" separator=" or ">
				skills like concat('%', #{skill}, '%')
			</foreach>
			)
		</if>
		order by rating_average desc, completed_jobs desc
		limit #{limit}
	</select>

	<select id="selectWorkerProfileByLocation" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active'
		and current_location like concat('%', #{location}, '%')
		order by rating_average desc, completed_jobs desc
		limit #{limit}
	</select>

	<select id="selectWorkerProfileBySalaryExpectation" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		<where>
			del_flag = '0' and status = 'active'
			<if test="salaryMin != null">
				and (salary_expectation_min is null or salary_expectation_min &lt;= #{salaryMin})
			</if>
			<if test="salaryMax != null">
				and (salary_expectation_max is null or salary_expectation_max &gt;= #{salaryMax})
			</if>
			<if test="salaryType != null and salaryType != ''">
				and salary_type_preference = #{salaryType}
			</if>
		</where>
		order by rating_average desc, completed_jobs desc
	</select>

	<select id="selectSimilarWorkerProfileList" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active' and worker_id != #{workerProfile.workerId}
		<if test="workerProfile.currentLocation != null and workerProfile.currentLocation != ''">
			and current_location like concat('%', #{workerProfile.currentLocation}, '%')
		</if>
		<if test="workerProfile.educationLevel != null and workerProfile.educationLevel != ''">
			and education_level = #{workerProfile.educationLevel}
		</if>
		<if test="workerProfile.workCategories != null and workerProfile.workCategories != ''">
			and work_categories like concat('%', #{workerProfile.workCategories}, '%')
		</if>
		order by rating_average desc, completed_jobs desc
		limit #{limit}
	</select>

	<select id="selectWorkerProfileByMatchParams" parameterType="java.util.Map" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		<where>
			del_flag = '0' and status = #{status}
			<if test="jobCategory != null and jobCategory != ''">
				and work_categories like concat('%', #{jobCategory}, '%')
			</if>
			<if test="salaryMin != null">
				and (salary_expectation_min is null or salary_expectation_min &lt;= #{salaryMin})
			</if>
			<if test="salaryMax != null">
				and (salary_expectation_max is null or salary_expectation_max &gt;= #{salaryMax})
			</if>
			<if test="salaryType != null and salaryType != ''">
				and salary_type_preference = #{salaryType}
			</if>
			<if test="jobType != null and jobType != ''">
				and job_types_preferred like concat('%', #{jobType}, '%')
			</if>
			<if test="skillsRequired != null and skillsRequired != ''">
				and skills like concat('%', #{skillsRequired}, '%')
			</if>
			<if test="ageMin != null">
				and (age is null or age &gt;= #{ageMin})
			</if>
			<if test="ageMax != null">
				and (age is null or age &lt;= #{ageMax})
			</if>
			<if test="gender != null and gender != ''">
				and gender = #{gender}
			</if>
			<if test="educationRequired != null and educationRequired != ''">
				and education_level = #{educationRequired}
			</if>
			<if test="experienceRequired != null and experienceRequired != ''">
				and work_experience_years &gt;= #{experienceRequired}
			</if>
			<if test="startDate != null">
				and (availability_start_date is null or availability_start_date &lt;= #{startDate})
			</if>
			<if test="endDate != null">
				and (availability_end_date is null or availability_end_date &gt;= #{endDate})
			</if>
		</where>
		order by rating_average desc, completed_jobs desc, success_rate desc
		<if test="limit != null">
			limit #{limit}
		</if>
	</select>

	<select id="selectWorkerProfileStatistics" parameterType="Long" resultType="java.util.Map">
		select
		count(*) as total_count,
		sum(case when status = 'active' then 1 else 0 end) as active_count,
		sum(case when status = 'inactive' then 1 else 0 end) as inactive_count,
		sum(case when status = 'suspended' then 1 else 0 end) as suspended_count,
		sum(case when status = 'banned' then 1 else 0 end) as banned_count,
		sum(case when is_verified = 1 then 1 else 0 end) as verified_count,
		sum(IFNULL(completed_jobs, 0)) as total_completed_jobs,
		avg(IFNULL(rating_average, 0)) as avg_rating,
		avg(IFNULL(success_rate, 0)) as avg_success_rate,
		avg(IFNULL(work_experience_years, 0)) as avg_experience
		from worker_profile
		where del_flag = '0'
		<if test="userId != null">
			and user_id = #{userId}
		</if>
	</select>

	<!-- ==================== 核心匹配优化查询方法 ==================== -->

	<!-- 基于核心字段搜索零工信息 -->
	<select id="selectWorkerProfileByCoreFields" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		<where>
			del_flag = '0' and status = 'active'
			<if test="jobTypesPreferred != null and jobTypesPreferred != ''">
				and (job_types_preferred like concat('%', #{jobTypesPreferred}, '%') or job_types_preferred is null)
			</if>
			<if test="workCategories != null and workCategories != ''">
				and (work_categories like concat('%', #{workCategories}, '%') or work_categories is null)
			</if>
			<if test="salaryTypePreference != null and salaryTypePreference != ''">
				and (salary_type_preference = #{salaryTypePreference} or salary_type_preference is null)
			</if>
			<if test="educationLevel != null and educationLevel != ''">
				and education_level = #{educationLevel}
			</if>
			<if test="keyword != null and keyword != ''">
				and (real_name like concat('%', #{keyword}, '%') or nickname like concat('%', #{keyword}, '%'))
			</if>
		</where>
		order by rating_average desc, completed_jobs desc, create_time desc
	</select>

	<!-- 根据偏好工作类型查询零工信息 -->
	<select id="selectWorkerProfileByJobType" parameterType="String" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active'
		and (job_types_preferred like concat('%', #{jobType}, '%') or job_types_preferred is null)
		order by rating_average desc, completed_jobs desc, create_time desc
	</select>

	<!-- 根据工作类别偏好查询零工信息 -->
	<select id="selectWorkerProfileByJobCategory" parameterType="String" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active'
		and (work_categories like concat('%', #{jobCategory}, '%') or work_categories is null)
		order by rating_average desc, completed_jobs desc, create_time desc
	</select>

	<!-- 根据薪资类型偏好查询零工信息 -->
	<select id="selectWorkerProfileBySalaryType" parameterType="String" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active'
		and (salary_type_preference = #{salaryType} or salary_type_preference is null)
		order by rating_average desc, completed_jobs desc, create_time desc
	</select>

	<!-- 根据学历水平查询零工信息 -->
	<select id="selectWorkerProfileByEducation" parameterType="String" resultMap="WorkerProfileResult">
		<include refid="selectWorkerProfileVo"/>
		where del_flag = '0' and status = 'active' and education_level = #{educationLevel}
		order by rating_average desc, completed_jobs desc, create_time desc
	</select>

	<!-- 获取所有偏好工作类型列表 -->
	<select id="selectAllJobTypesPreferred" resultType="String">
		select distinct
		TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(job_types_preferred, ',', numbers.n), ',', -1)) as job_type
		from worker_profile
		join (
			select 1 n union all select 2 union all select 3 union all select 4 union all select 5
		) numbers on CHAR_LENGTH(job_types_preferred) - CHAR_LENGTH(REPLACE(job_types_preferred, ',', '')) >= numbers.n - 1
		where del_flag = '0' and job_types_preferred is not null and job_types_preferred != ''
		having job_type is not null and job_type != ''
		order by job_type
	</select>

	<!-- 获取所有工作类别偏好列表 -->
	<select id="selectAllWorkCategories" resultType="String">
		select distinct
		TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(work_categories, ',', numbers.n), ',', -1)) as work_category
		from worker_profile
		join (
			select 1 n union all select 2 union all select 3 union all select 4 union all select 5
		) numbers on CHAR_LENGTH(work_categories) - CHAR_LENGTH(REPLACE(work_categories, ',', '')) >= numbers.n - 1
		where del_flag = '0' and work_categories is not null and work_categories != ''
		having work_category is not null and work_category != ''
		order by work_category
	</select>

	<!-- 获取所有薪资类型偏好列表 -->
	<select id="selectAllSalaryTypePreferences" resultType="String">
		select distinct salary_type_preference from worker_profile
		where del_flag = '0' and salary_type_preference is not null and salary_type_preference != ''
		order by salary_type_preference
	</select>

	<!-- 获取所有学历水平列表 -->
	<select id="selectAllEducationLevels" resultType="String">
		select distinct education_level from worker_profile
		where del_flag = '0' and education_level is not null and education_level != ''
		order by education_level
	</select>
</mapper>
