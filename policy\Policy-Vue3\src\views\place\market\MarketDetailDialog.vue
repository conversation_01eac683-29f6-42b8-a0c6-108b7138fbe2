<template>
    <el-dialog title="零工市场详情" v-model="dialogVisible" width="800px" append-to-body>
        <el-descriptions :column="2" border v-loading="loading">
            <el-descriptions-item label="市场名称">{{ detailData.marketName }}</el-descriptions-item>
            <el-descriptions-item label="市场编码">{{ detailData.marketCode }}</el-descriptions-item>
            <el-descriptions-item label="市场类型">
                <el-tag :type="getMarketTypeTagType(detailData.marketType)">
                    {{ detailData.marketType }}
                </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="营业时间">{{ detailData.operatingHours }}</el-descriptions-item>
            <el-descriptions-item label="市场地址" :span="2">{{ detailData.address }}</el-descriptions-item>
            <el-descriptions-item label="区域代码">{{ detailData.regionCode }}</el-descriptions-item>
            <el-descriptions-item label="区域名称">{{ detailData.regionName }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{ detailData.contactPerson }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ detailData.contactPhone }}</el-descriptions-item>
            <el-descriptions-item label="联系邮箱" :span="2">{{ detailData.contactEmail }}</el-descriptions-item>
            <el-descriptions-item label="零工容纳量">{{ detailData.workerCapacity }}</el-descriptions-item>
            <el-descriptions-item label="当前零工数量">{{ detailData.currentWorkerCount }}</el-descriptions-item>
            <el-descriptions-item label="日均用工需求">{{ detailData.dailyAvgDemand }}</el-descriptions-item>
            <el-descriptions-item label="用工高峰时段">{{ detailData.peakDemandTime }}</el-descriptions-item>
            <el-descriptions-item label="管理费用">{{ detailData.managementFee }} 元/人/天</el-descriptions-item>
            <el-descriptions-item label="服务费率">{{ detailData.serviceFeeRate }}%</el-descriptions-item>
            <el-descriptions-item label="是否推荐">
                <el-tag :type="detailData.isFeatured ? 'success' : 'info'">
                    {{ detailData.isFeatured ? '是' : '否' }}
                </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
                <el-tag :type="detailData.status === '0' ? 'success' : 'danger'">
                    {{ detailData.status === '0' ? '正常' : '停用' }}
                </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="浏览次数">{{ detailData.viewCount }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatTime(detailData.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="安全措施" :span="2">{{ detailData.safetyMeasures }}</el-descriptions-item>
            <el-descriptions-item label="市场描述" :span="2">{{ detailData.description }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ detailData.remark }}</el-descriptions-item>
        </el-descriptions>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">关 闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup name="MarketDetailDialog">
import { ref, reactive, getCurrentInstance } from 'vue'
import { getLaborMarketInfo } from "@/api/place/market"

const { proxy } = getCurrentInstance()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)

// 详情数据
const detailData = reactive({
    marketId: null,
    marketName: '',
    marketCode: '',
    marketType: '',
    address: '',
    regionCode: '',
    regionName: '',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    operatingHours: '',
    workerCapacity: 0,
    currentWorkerCount: 0,
    dailyAvgDemand: 0,
    peakDemandTime: '',
    managementFee: '',
    serviceFeeRate: '',
    safetyMeasures: '',
    description: '',
    status: '0',
    isFeatured: 0,
    viewCount: 0,
    createTime: '',
    remark: ''
})

// 获取市场类型标签类型
function getMarketTypeTagType(marketType) {
    const typeMap = {
        '综合市场': 'primary',
        '专业市场': 'success',
        '临时市场': 'warning'
    }
    return typeMap[marketType] || 'info'
}

// 格式化时间
function formatTime(time) {
    if (!time) return ''
    return proxy.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

// 重置数据
function resetData() {
    Object.assign(detailData, {
        marketId: null,
        marketName: '',
        marketCode: '',
        marketType: '',
        address: '',
        regionCode: '',
        regionName: '',
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
        operatingHours: '',
        workerCapacity: 0,
        currentWorkerCount: 0,
        dailyAvgDemand: 0,
        peakDemandTime: '',
        managementFee: '',
        serviceFeeRate: '',
        safetyMeasures: '',
        description: '',
        status: '0',
        isFeatured: 0,
        viewCount: 0,
        createTime: '',
        remark: ''
    })
}

// 打开对话框
function openDialog(marketId) {
    resetData()
    dialogVisible.value = true
    
    if (marketId) {
        loading.value = true
        getLaborMarketInfo(marketId).then(response => {
            Object.assign(detailData, response.data)
        }).catch(() => {
            proxy.$modal.msgError('获取市场详情失败')
        }).finally(() => {
            loading.value = false
        })
    }
}

// 关闭对话框
function handleClose() {
    dialogVisible.value = false
    resetData()
}

// 暴露方法
defineExpose({
    openDialog
})
</script>

<style scoped>
.dialog-footer {
    text-align: right;
}
</style>
