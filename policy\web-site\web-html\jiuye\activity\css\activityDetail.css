.welcome_a:hover,.webvnavBbox a:hover{
    color: #fff;
}
dd{
    margin: 0;
}
body,
html {
    background: url(../image/cyhd_bg.jpg) repeat;
}

.bgBox {
    background: url(../image/cyhd_topbg.png) no-repeat top center;
    overflow: hidden;
    min-width: 1400px;
}
.conAuto3 {
    width: 1400px;
    max-width: 98%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 10px;
    box-sizing: border-box;
    padding: 0 10px;
}

.conAuto2 {
    width: 1400px;
    max-width: 98%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 30px;
    box-sizing: border-box;
    padding: 0 10px;
}

/* 顶部信息 */
.topModuleSco {
    height: 412px;
    width: 100%;
    margin-bottom: 40px;
}

.topModuleSco {
    position: relative;
    width: 100%;
    min-height: 450px;
    margin-bottom: 20px;
}

.topModuleSco .leftBox {
    width: 100%;
    height: auto;
    min-height: 387px;
    background: url(../image/cyhd_toppic.png) no-repeat;
    background-size: cover;
    background-position: center;
    position: relative;
    z-index: 1;
    box-shadow: 3px 0 20px #85adde;
    padding: 20px;
    border-radius: 12px;
    overflow: hidden;
    max-width: 100%;
    box-sizing: border-box;
}

.topModuleSco .parkName {
    font-size: 24px;
    font-weight: bold;
    height: 33px;
    line-height: 33px;
    max-width: 576px;
    margin-bottom: 15px;
}

.topModuleSco .activityThemeName {
    background: #f5f9fd;
    height: 30px;
    line-height: 30px;
    padding: 0 15px;
    border-radius: 0 50px 50px 50px;
    color: #0052d9;
    margin-right: 10px;
    max-width: 460px;
}

.topModuleSco .activityLevel {
    background: #ffefe5;
    height: 30px;
    line-height: 30px;
    padding: 0 15px;
    border-radius: 0 50px 50px 50px;
    color: #ff6000;
    margin-right: 10px;
    max-width: 460px;
}

.topModuleSco .category {
    background: #edfeff;
    height: 30px;
    line-height: 30px;
    padding: 0 15px;
    border-radius: 0 50px 50px 50px;
    color: #13b0be;
    margin-right: 10px;
    max-width: 460px;
}

.topModuleSco .infop {
    line-height: 34px;
    height: 34px;
    color: #666;
    font-size: 16px;
    padding-left: 43px;
    width: 630px;
}

.topModuleSco .infop span {
    color: #333;
}

.topModuleSco .mapBtn {
    display: block;
    
    width: 35px;
    height: 40px;
    background: url(../image/mapIcon.png) center center no-repeat;


}

.topModuleSco .linkBox {
    width: 566px;
    height: 80px;
    background: url(../image/cyhd_linkBg.png) no-repeat;
    margin-left: 43px;
    margin-top: 15px;
}

.topModuleSco .linkBox p {
    padding-left: 58px;
    font-size: 16px;
    color: #666;
    line-height: 80px;
}

.topModuleSco .linkBox p span {
    padding-left: 10px;
    color: #333;
}

.topModuleSco .linkBox .icon1 {
    background: url(../image/cyhd_linkIcon1.png) no-repeat 20px center;
    width: 210px;
}

.topModuleSco .linkBox .icon2 {
    background: url(../image/cyhd_linkIcon2.png) no-repeat 20px center;
}

.topModuleSco .bottomBoxs .btnCont1,
.topModuleSco .bottomBoxs .btnCont2 {
    width: 146px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    color: #fff;
    font-size: 18px;
    background: url(../../place/image/new_cycdBtn.png) no-repeat;
    margin: 0 23px 0 38px;
    opacity: 1;
}

.topModuleSco .bottomBoxs .btnCont1:hover {
    opacity: .8;
}

.topModuleSco .bottomBoxs .btnCont3 {
    width: 146px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    color: #fff;
    font-size: 18px;
    background: url(../../place/image/new_cycdBtn2.png) no-repeat;
    margin: 0 23px 0 38px;
}

.topModuleSco .bottomBoxs .btnCont5 {
    width: 200px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    color: #fff;
    font-size: 18px;
    background: url(../image/hd_btn.png) no-repeat;
    margin: 0 23px 0 38px;
}

.btnCont5 .bmPop {
    background: #0052d9;
    padding: 5px;
    width: 290px;
    border-radius: 8px;
    top: 16px;
    left: -50px;
}

.btnCont5 .bmPop .arr {
    width: 16px;
    height: 8px;
    background: url(../image/hd_arr.png) no-repeat;
    top: -8px;
    margin-left: 50%;
    left: -8px;
}

.btnCont5 .bmPop .webUrl {
    background: #fff url(../image/hd_icon1.png) no-repeat 9px 10px;
    border-radius: 8px;
    padding: 13px 20px 13px 50px;
    vertical-align: middle;
    line-height: 24px;
    color: #333;
    font-size: 14px;
    margin-bottom: 5px;
}

.btnCont5 .bmPop .codeUrl {
    background: #fff url(../image/hd_icon2.png) no-repeat 9px center;
    border-radius: 8px;
    padding: 13px 20px 13px 50px;
    vertical-align: middle;
    line-height: 90px;
    color: #333;
    font-size: 14px;
}

.btnCont5 .bmPop .codeUrl img {
    width: 90px;
    height: 90px;
}

.topModuleSco .bottomBoxs .btnCont4 {
    width: 146px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    color: #666;
    font-size: 18px;
    background: url(../../place/image/new_cycdBtn3.png) no-repeat;
    margin: 0 23px 0 38px;
}

.topModuleSco .bottomBoxs .enlistNumber {
    font-size: 16px;
    line-height: 46px;
}

.topModuleSco .bottomBoxs .enlistNumber span {
    padding: 0 5px;
    color: #ff6000;
}

.topModuleSco .bottomBoxs .chargeTypeName {
    font-size: 24px;
    font-weight: bold;
    color: #ff6000;
    line-height: 46px;
    margin-right: 20px;
}

/* 图片轮播 */
.topModuleSco .rightMag {
    position: absolute;
    display: block;
    height: 338px;
    width: 600px;
    top: 73px;
    right: 0;
    border-radius: 0 90px 8px 0;
    overflow: hidden;
}

.topModuleSco .rightMag .bd img,
.topModuleSco .rightMag .bd {
    display: block;
    width: 600px;
    height: 338px;
}

.topModuleSco .rightMag .hd {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 50px;
    background: url(../../place/image/cd_picTitleBg.png) repeat;
    z-index: 3;
}

.topModuleSco .rightMag .hd .prev {
    display: block;
    width: 30px;
    height: 30px;
    background: url(../../place/image/prev.png) left center no-repeat;
    margin-top: 10px;
}

.topModuleSco .rightMag .hd .prev:hover {
    background: url(../../place/image/prevOn.png) center center no-repeat !important;
}

.topModuleSco .rightMag .hd .next {
    display: block;
    width: 30px;
    height: 30px;
    background: url(../../place/image/next.png) left center no-repeat;
    margin: 10px 10px 0 20px;
}

.topModuleSco .rightMag .hd .next:hover {
    background: url(../../place/image/nextOn.png) left center no-repeat;
}

/* 标题 */
.textStyles {
    background: url(../image/cyhd_textbg.png) no-repeat left top;
    font-size: 24px;
    font-weight: bold;
    line-height: 43px;
    padding-left: 10px;
}

.infoBoxs {
    border: 1px solid #e4f1fc;
    border-radius: 8px;
    background: #fff;
    padding: 20px 28px;
    line-height: 30px;
    font-size: 16px;
    color: #666;
}

.infoBoxs p,
.infoBoxs p span,
.infoBoxs p em {
    line-height: 30px !important;
    font-size: 16px !important;
    color: #666 !important;
}

.infoBoxs img {
    display: block;
    max-width: 100%;
    margin: 0 auto;
}

.leftBoxs {
    width: calc(100% - 450px);
    max-width: 950px;
    min-width: 600px;
    box-sizing: border-box;
    padding-right: 30px;
    float: left;
}

/* 推荐 */
.rightBoxs .codeBox {
    width: 420px;
    height: 160px;
    background: url(../image/codebg.png) no-repeat;
    margin-bottom: 30px;
}

#erweima {
    width: 130px;
    height: 130px;
    display: block;
    margin: 9px 25px 0 0;
    border: 5px solid #fff;
    border-radius: 8px;
}

.rightBoxs {
    width: 420px;
    max-width: 420px;
    min-width: 300px;
    box-sizing: border-box;
    float: right;
}

/* 确保左右侧布局容器正确工作 */
.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

/* 确保浮动布局正确工作 */
.leftBoxs.fl,
.leftBoxs {
    float: left;
}

.rightBoxs.fr,
.rightBoxs {
    float: right;
}

.changeBox {
    background: url(../image/cyhd_hyh.png) no-repeat right center;
    padding-right: 28px;
    color: #0052d9;
    line-height: 43px;
}

.rightBoxs ul {
    margin-top: 10px;
}

.rightBoxs li {
    width: 100%;
    max-width: 380px;
    height: 143px;
    background: url(../image/cyhd_tjbg.png) no-repeat;
    background-size: 100% 100%;
    box-shadow: 0px 0 10px #d0e5ff;
    margin-bottom: 30px;
    border-radius: 5px;
    overflow: hidden;
    padding: 20px 20px 0;
    box-sizing: border-box;
}

.rightBoxs li:hover {
    background: url(../image/cyhd_tjbgOn.png) no-repeat;
    background-size: 100% 100%;
}

.rightBoxs li a {
    display: block;
    font-size: 20px;
    height: 24px;
    line-height: 24px;
    margin-bottom: 20px;
}

.rightBoxs li a:hover {
    color: #0052d9;
}

.rightBoxs li .activityTheme {
    height: 30px;
    margin-bottom: 35px;
}

.rightBoxs li .activityTheme span {
    background: #eaf2ff;
    padding: 0 15px;
    height: 30px;
    line-height: 30px;
    color: #0052d9;
    border-radius: 0 50px 50px 50px;
    max-width: 300px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.rightBoxs li .timeBoxs {
    padding-left: 22px;
    background: url(../image/new_zhdTime.png) no-repeat left center;
    color: #666;
}

/* 弹窗 */

.alertBg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10;
}

.alertCont {
    width: 818px;
    background-color: #ffffff;
    margin: 5% auto 0;
    border-radius: 5px;
    /* overflow: hidden; */
}

.alertTitle {
    line-height: 67px;
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
    background: url(../../place/image/new_cycdPop1.png) no-repeat;
    text-align: center;
    position: relative;
}

.alertClose {
    width: 15px;
    height: 15px;
    position: absolute;
    right: 20px;
    top: 26px;
}

.alertClose:hover {
    cursor: pointer;
}

.alertClose img {
    display: block;
    width: 100%;
}

.alertUl {
    max-height: 560px;
    padding: 30px 30px 30px 0;
    background: url(../../place/image/new_cycdPop2.png) no-repeat bottom center;
}

.tip {
    padding-left: 117px;
    color: #ff6000;
    margin-bottom: 15px;
}

.tip a {
    color: #077dea;
    text-decoration: underline;
}

.alertLable {
    width: 143px;
    font-size: 14px;
    color: #000000;
    text-align: right;
    line-height: 44px;
}

.alertLable em {
    color: rgb(243, 74, 74);
    margin-right: 4px;
}

.alertIpt {
    height: 44px !important;
    width: 601px !important;
    border: 1px solid #ededed !important;
    border-radius: 4px !important;
    box-sizing: border-box !important;
    padding: 5px 10px !important;
    background: #fff !important;
}

.alertIpt input {
    width: 100%;
    height: 32px;
    border: none;
    outline: none;

}

.w390 {
    width: 390px;
}

.w250 {
    width: 250px;
}

.alertBtnCont {
    text-align: center;
}

.alertIptBtn:hover,
.alertBtn:hover {
    cursor: pointer;
}

.alertBtn {
    width: 110px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    display: inline-block;
    border-radius: 50px;
}

.btnCancel {
    border: 1px solid #8db6f2;
    color: #1569e6;
    background-color: #e7f1fc;
}

.inputBox span {
    width: 143px;
    margin-right: 10px;
    text-align: right;
    height: 44px;
    line-height: 44px;
    color: #000000;
}

.inputBox span em {
    color: #ff0000;
}

.inputBox input[type="text"],
.dateInput {
    width: 580px;
    height: 42px;
    padding: 0 10px;
    border: 1px solid #ededed;
    border-radius: 5px;
}

.inputBox span.yzBox {
    width: 91px;
    height: 30px;
    line-height: 30px;
    color: #fff;
    text-align: center;
    border-radius: 5px;
    position: absolute;
    top: 7px;
    right: 50px;
    border: none;
}

.inputBox input.DuanXinBox {
    width: 91px;
    height: 30px;
    line-height: 30px;
    color: #0052d9;
    text-align: center;
    background: #f4f8ff;
    border-radius: 5px;
    position: absolute;
    top: 7px;
    right: 50px;
    border: none;
}


.alertLi .input-file input[type="file"],
#infoLogoForm {
    position: absolute;
    top: 0;
    left: 0px;
    width: 70px;
    background-color: #fff;
    height: 40px;
    opacity: 0;
    height: 40px;
    font-size: 0;
}

.alertLi .upBtn {
    background-color: #ffefe5;
    color: #ff6000;
    width: 72px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin-right: 5px;
    position: relative;
    border-radius: 0 5px 5px 0;
}

.alertBg select {
    height: 44px;
    width: 601px;
    border: 1px solid #ededed;
    border-radius: 4px;
    box-sizing: border-box;
    padding: 5px 10px;
}

.upload {
    width: 432px !important;
    height: 38px !important;
    padding: 0 10px !important;
    border: 1px solid #ededed !important;
    border-radius: 5px 0 0 5px !important;
    background: #fff !important;
}

.btnSubmit {
    background: url(../../place/image/new_cycdPop3.png) no-repeat;
    color: #ffffff;
}

.alertLi .filedelete {
    background-color: #ffe5e5;
    color: #ff0000;
    width: 70px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin-right: 5px;
    position: relative;
    border-radius: 5px;
}

.topModuleSco .tab1 {
    background: url(../image/hdtab2.png) no-repeat;
    width: 60px;
    height: 60px;
    top: 0;
    left: 0;
}

.topModuleSco .tab2 {
    background: url(../image/hdtab1.png) no-repeat;
    width: 60px;
    height: 60px;
    top: 0;
    left: 0;
}

.topModuleSco .tab3 {
    background: url(../image/hdtab3.png) no-repeat;
    width: 60px;
    height: 60px;
    top: 0;
    left: 0;
}

/* 一键生成图片 */
#container {
    width: 750px;
    position: fixed;
    z-index: -1;
    left: -750px;
    background-image: url(../pic/hd_topBg.jpg), url(../pic/hd_bottomBg.png);
    background-repeat: no-repeat, no-repeat;
    background-position: top center, bottom center;
    background-color: #05399f;
    padding-top: 60px;
    overflow: hidden;
    padding-bottom: 37px;
}

#container .logo {
    display: block;
    margin: 0 auto 55px;
}

#container .parkName {
    color: #fff;
    line-height: 70px;
    font-size: 48px;
    font-weight: bold;
    width: 528px;
    margin: 0 auto;
    text-align: center;
    height: 371px;
}

#container .timeAddress {
    width: 642px;
    background: url(../pic/hd_timeAddress.png) no-repeat top center;
    background-size: 100% 100%;
    padding: 60px 17px 17px 22px;
    margin: 0 auto 60px;
}

#container .timeAddress .time {
    padding-left: 70px;
    background-image: url(../pic/hd_icon1.png), url(../pic/hd_line.png);
    background-repeat: no-repeat, no-repeat;
    background-position: left top, bottom center;
    line-height: 50px;
    padding-bottom: 16px;
    font-size: 24px;
    color: #fff;
}

#container .timeAddress .address {
    padding-left: 70px;
    background: url(../pic/hd_icon2.png) no-repeat left 12px;
    line-height: 34px;
    min-height: 43px;
    padding-top: 19px;
    font-size: 24px;
    color: #fff;
}

#container .styleTitle {
    background: url(../pic/hd_titleBg.png) no-repeat center;
    color: #fff;
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    width: 330px;
    height: 79px;
    line-height: 79px;
    margin: 0 auto;
}

#container .hdjj,
#container .zzjg {
    width: 680px;
    position: relative;
    margin: 20px auto 58px;
    height: auto;
    padding-bottom: 11px;
}

#container .hdjj1,
#container .zzjg1 {
    position: absolute;
    width: 602px;
    padding: 17px 38px 22px 30px;
    background: #1d4fb2;
    border-radius: 10px;
    font-size: 24px;
    color: #fff;
    line-height: 48px;
    top: 0;
    left: 0;
    z-index: 3;
}

#container .hdjj2,
#container .zzjg2 {
    border: 1px dashed #9eb5e2;
    width: 666px;
    height: 10px;
    position: absolute;
    bottom: 5px;
    left: 7px;
    border-radius: 10px;
    z-index: 2;
}

#container .hdjj3,
#container .zzjg3 {
    background: #3d6bc5;
    width: 670px;
    height: 10px;
    position: absolute;
    bottom: 0;
    left: 10px;
    border-radius: 10px;
    z-index: 1;
}

#container .codeBox {
    width: 342px;
    height: 342px;
    background: url(../pic/hd_codeBg.png) no-repeat;
    margin: 0 auto;
    overflow: hidden;
}

#container .codeBox #erweima2 {
    width: 240px;
    height: 240px;
    margin: 51px auto 0;
}

#container .codeText {
    width: 260px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    font-size: 24px;
    color: #fff;
    background: url(../pic/hd_codeTextBg.png) no-repeat;
    margin: 15px auto 111px;
}

#container .bottomText {
    text-align: center;
    color: #fff;
    line-height: 48px;
    font-size: 24px;
}

#saveBtn {
    display: block;
    top: 13px;
    right: 437px;
    width: 126px;
    height: 46px;
    line-height: 46px;
    color: #fff;
    font-size: 18px;
    padding-left: 20px;
    background: url(../pic/hd_btn.png) no-repeat;
}

.styleTit {
    background: url(../../public/images/icons/icon_titleBg.png) no-repeat #ecf3ff 14px center;
    height: 50px;
    line-height: 50px;
    border-radius: 5px;
    padding-left: 37px;
    font-size: 18px;
    font-weight: bold;
    color: #0052d9;
    margin-bottom: 20px;
    width: 675px;
    position: relative;
}

.styleTit a {
    display: block;
    position: absolute;
    width: 24px;
    height: 24px;
    top: 13px;
    right: 24px;
    background: url(../../public/images/icons/popDown.png) no-repeat;
}

.styleTit a.on {
    background: url(../../public/images/icons/popUp.png) no-repeat;
}

.height0 {
    height: 0px;
    overflow: hidden;
}

/* 样式覆盖 */
a {
    text-decoration: none;
    color: #303946;
}

a:hover {
    text-decoration: none;
}

ul,
ol {
    list-style: none;
    margin: 0;
}

.detailsPop .popupTable,
.buildDetailsPop .popupTable {
    min-height: 42vh;
    max-height: 60vh;
}

p {
    margin: 0;
}

em {
    font-style: normal;
}
img{
    vertical-align: initial;
}
select:focus,
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
    outline: none;
}

label {
    display: initial;
}

input[type="radio"] {
    margin: 0;
} 

.tipsBox {
    width: 200px;
    height: 80px;
    left: 154px;
    top: 48px;
    position: absolute;
    background-color: #fff;
    border-radius: 5px;
    line-height: 80px;
    text-align: center;
    box-shadow: 0 0 10px 1px #d5d5d5;
}
.tipsBox img{
    width: 30px !important;
    height: 30px !important;
    vertical-align: middle;
}
.alertCont input{
    margin-bottom: 0;
    -webkit-box-shadow:none;
    box-shadow: none;
    vertical-align: initial;
    padding: 0;
}

/* 新的培训订单详情页面样式 */

/* 状态标签 */
.status-tag-new {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
}

.status-text-new {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: #fff;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

/* 状态样式类 */
.status-text-new.status-draft {
    background: linear-gradient(135deg, #d9d9d9 0%, #bfbfbf 100%);
    color: #666;
    box-shadow: 0 2px 8px rgba(217, 217, 217, 0.3);
}

.status-text-new.status-published {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.status-text-new.status-ongoing {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.status-text-new.status-completed {
    background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(114, 46, 209, 0.3);
}

.status-text-new.status-cancelled {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
}

/* 标题区域 */
.title-section-new {
    margin-bottom: 15px;
}

.training-title-new {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 12px 0;
    line-height: 1.3;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.title-meta-new {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.meta-tag-new {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.type-tag-new {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.level-tag-new {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.category-tag-new {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 信息网格 */
.info-grid-new {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.info-item-new {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.info-item-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.info-icon-new {
    font-size: 20px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
}

.info-content-new {
    flex: 1;
}

.info-label-new {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
}

.info-value-new {
    font-size: 14px;
    color: #333;
    font-weight: 600;
    line-height: 1.4;
}

.price-highlight-new {
    color: #ff6b35;
    font-size: 16px;
    font-weight: 700;
}

/* 进度条区域 */
.progress-section-new {
    background: rgba(255, 255, 255, 0.9);
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

.progress-header-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.progress-label-new {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.progress-stats-new {
    font-size: 14px;
    color: #666;
}

.progress-bar-container-new {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
}

.progress-bar-bg-new {
    flex: 1;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-fill-new {
    height: 100%;
    background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-percentage-new {
    font-size: 12px;
    font-weight: 600;
    color: #52c41a;
    min-width: 35px;
    text-align: right;
}

/* 操作按钮区域 */
.action-section-new {
    background: rgba(255, 255, 255, 0.9);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

.primary-actions-new {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.btn-primary-new {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: #fff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.btn-primary-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(82, 196, 26, 0.4);
}

.price-display-new {
    text-align: right;
}

.price-label-new {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.price-value-new {
    font-size: 18px;
    font-weight: 700;
    color: #ff6b35;
}

.secondary-actions-new {
    display: flex;
    gap: 10px;
}

.btn-secondary-new {
    flex: 1;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border: 1px solid rgba(102, 126, 234, 0.2);
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.btn-secondary-new:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: translateY(-1px);
}

.btn-icon-new {
    font-size: 14px;
}

.btn-text-new {
    font-weight: 500;
}

/* 分享提示样式 */
.share-tip-new {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 16px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 6px;
    color: #856404;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.share-tip-new:hover {
    background: rgba(255, 193, 7, 0.2);
    border-color: rgba(255, 193, 7, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.share-tip-new:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(255, 193, 7, 0.3);
}

.tip-icon-new {
    font-size: 14px;
}

.tip-text-new {
    font-weight: 500;
    font-size: 12px;
}

/* 右侧快速信息卡片 */
.quick-info-card-new {
    position: absolute;
    right: 30px;
    top: 30px;
    width: 280px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    z-index: 10;
    animation: slideInRight 0.6s ease-out;
}

.card-header-new {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header-new h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.status-indicator-new {
    font-size: 16px;
}

.card-content-new {
    padding: 20px;
}

/* 倒计时 */
.countdown-section-new {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #fff5f5 0%, #fff0f0 100%);
    border-radius: 8px;
    border: 1px solid #ffe0e0;
}

.countdown-label-new {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.countdown-timer-new {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.time-unit-new {
    text-align: center;
}

.time-value-new {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #ff6b35;
    line-height: 1;
}

.time-label-new {
    font-size: 10px;
    color: #999;
    margin-top: 2px;
}

/* 快速报名按钮 */
.quick-signup-btn-new {
    width: 100%;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
    color: #fff;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.quick-signup-btn-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

/* 培训亮点迷你版 */
.highlights-mini-new {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.highlight-mini-item-new {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.highlight-mini-item-new:hover {
    background: #e9ecef;
    transform: translateX(3px);
}

.highlight-icon-new {
    font-size: 14px;
}

.highlight-text-new {
    font-size: 12px;
    color: #555;
    font-weight: 500;
}

/* 动画效果 */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .conAuto2 {
        width: 95%;
        min-width: auto;
        padding: 0 20px;
        max-width: 1400px;
    }

    .topModuleSco .leftBox {
        width: 65%;
        padding-right: 320px;
    }

    .quick-info-card-new {
        width: 260px;
        right: 20px;
    }

    /* 修复左右侧布局 */
    .leftBoxs, .leftBoxs.fl {
        width: calc(100% - 450px);
        max-width: 900px;
        padding-right: 30px;
        box-sizing: border-box;
        float: left;
    }

    .rightBoxs, .rightBoxs.fr {
        width: 420px;
        max-width: 420px;
        min-width: 350px;
        float: right;
    }

    .rightBoxs li {
        width: 100%;
        max-width: 380px;
    }
}

@media (max-width: 1200px) {
    .conAuto2 {
        width: 98%;
        padding: 0 15px;
    }

    .topModuleSco .leftBox {
        width: 100%;
        max-width: 700px;
        padding-right: 280px;
    }

    .quick-info-card-new {
        width: 240px;
        right: 20px;
    }

    .info-grid-new {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .training-title-new {
        font-size: 24px;
    }

    /* 修复左右侧布局 */
    .leftBoxs, .leftBoxs.fl {
        width: calc(100% - 400px);
        max-width: 700px;
        padding-right: 20px;
        min-width: 500px;
        float: left;
    }

    .rightBoxs, .rightBoxs.fr {
        width: 380px;
        max-width: 380px;
        min-width: 320px;
        float: right;
    }

    /* 确保推荐区域不会被挤压 */
    .rightBoxs li {
        width: 100%;
        max-width: 350px;
        padding: 15px;
    }
}

/* 中等屏幕尺寸优化 */
@media (max-width: 1000px) {
    .leftBoxs, .leftBoxs.fl {
        width: calc(100% - 350px);
        max-width: 600px;
        min-width: 400px;
        float: left;
    }

    .rightBoxs, .rightBoxs.fr {
        width: 330px;
        max-width: 330px;
        min-width: 300px;
        float: right;
    }

    .rightBoxs li {
        width: 100%;
        max-width: 310px;
        padding: 12px;
    }

    .rightBoxs li a {
        font-size: 18px;
    }
}

@media (max-width: 768px) {
    .conAuto2 {
        width: 100%;
        padding: 0 10px;
    }

    .topModuleSco {
        height: auto;
    }

    .topModuleSco .leftBox {
        width: 100%;
        padding: 20px;
        position: static;
        margin-bottom: 20px;
    }

    .quick-info-card-new {
        position: static;
        width: 100%;
        margin-top: 20px;
        animation: none;
    }

    .training-title-new {
        font-size: 20px;
        text-align: center;
    }

    .title-meta-new {
        justify-content: center;
    }

    .info-grid-new {
        grid-template-columns: 1fr;
    }

    .primary-actions-new {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .secondary-actions-new {
        flex-direction: column;
        gap: 8px;
    }

    .share-tip-new {
        flex: none;
    }

    .tip-text-new {
        font-size: 11px;
    }

    .countdown-timer-new {
        gap: 12px;
    }

    .time-value-new {
        font-size: 16px;
    }

    /* 移动端左右侧布局改为垂直堆叠 */
    .leftBoxs, .leftBoxs.fl {
        width: 100% !important;
        max-width: none !important;
        padding-right: 0 !important;
        margin-bottom: 30px;
        float: none !important;
    }

    .rightBoxs, .rightBoxs.fr {
        width: 100% !important;
        max-width: none !important;
        min-width: auto !important;
        float: none !important;
    }

    /* 移动端推荐项目样式调整 */
    .rightBoxs li {
        width: 100% !important;
        max-width: none !important;
        margin-bottom: 20px;
        height: auto;
        min-height: 120px;
    }
}

@media (max-width: 480px) {
    .topModuleSco .leftBox {
        padding: 15px;
    }

    .training-title-new {
        font-size: 18px;
    }

    .meta-tag-new {
        font-size: 11px;
        padding: 3px 10px;
    }

    .info-item-new {
        padding: 12px;
    }

    .info-icon-new {
        font-size: 16px;
    }

    .info-value-new {
        font-size: 13px;
    }

    .btn-primary-new {
        padding: 10px 20px;
        font-size: 14px;
    }

    .card-content-new {
        padding: 15px;
    }

    .quick-signup-btn-new {
        padding: 10px 16px;
        font-size: 14px;
    }
}

/* 主要布局容器 */
.main-layout-container {
    display: flex;
    gap: 20px;
    align-items: flex-start;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    box-sizing: border-box;
    position: relative;
}

.left-main-content {
    flex: 1;
    min-width: 0;
    width: calc(100% - 420px);
    max-width: calc(100% - 420px);
    box-sizing: border-box;
}

.right-sidebar-content {
    flex: 0 0 400px;
    width: 400px;
    min-width: 400px;
    max-width: 400px;
    position: sticky;
    top: 20px;
    align-self: flex-start;
    height: fit-content;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInFromRight 0.6s ease-out;
    transition: all 0.3s ease;
    z-index: 10;
}

.right-sidebar-content:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

/* 右侧边栏滑入动画 */
@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 新的内容布局样式 */
.content-layout-new {
    display: flex;
    gap: 30px;
    margin-top: 30px;
}

.main-content-new {
    flex: 1;
}

.sidebar-content-new {
    flex: 1;
    min-width: 300px;
}

/* 内容区块样式 */
.content-section-new {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 25px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
}

/* 右侧边栏中的内容区块特殊样式 */
.right-sidebar-content .content-section-new {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    backdrop-filter: blur(5px);
    margin-bottom: 0;
}

.section-title-new {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
    padding: 20px 25px 15px;
    border-bottom: 2px solid #f0f0f0;
    position: relative;
}

.section-title-new:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 25px;
    width: 40px;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.section-content-new {
    padding: 20px 25px 25px;
    line-height: 1.8;
    font-size: 15px;
    color: #555;
}

/* 右侧边栏中的内容区域特殊样式 */
.right-sidebar-content .section-content-new {
    padding: 16px 18px 18px;
    font-size: 14px;
}

.section-content-new p {
    margin-bottom: 15px;
}

.section-content-new ul {
    padding-left: 20px;
    margin-bottom: 15px;
}

.section-content-new li {
    margin-bottom: 8px;
    position: relative;
}

.section-content-new li:before {
    content: "▸";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: -15px;
}

/* 培训安排网格 */
.schedule-grid-new {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.schedule-item-new {
    display: flex;
    align-items: center;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    border-radius: 8px;
    border: 1px solid #e1e8ed;
    transition: all 0.3s ease;
}

.schedule-item-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.schedule-icon-new {
    font-size: 20px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
}

.schedule-details-new {
    flex: 1;
}

.schedule-label-new {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
}

.schedule-value-new {
    font-size: 14px;
    color: #333;
    font-weight: 600;
    line-height: 1.4;
}

/* 推荐区块头部 */
.section-header-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px 12px;
    border-bottom: 2px solid #f0f0f0;
}

/* 右侧边栏中的区块头部特殊样式 */
.right-sidebar-content .section-header-new {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    margin: -1px -1px 0 -1px;
    border-radius: 12px 12px 0 0;
    border-bottom: 2px solid #e1e8ed;
}

.refresh-btn-new {
    color: #667eea;
    font-size: 12px;
    text-decoration: none;
    padding: 4px 10px;
    border: 1px solid #667eea;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.refresh-btn-new:hover {
    background: #667eea;
    color: #fff;
    transform: translateY(-1px);
}

.title-icon {
    font-size: 16px;
    margin-right: 6px;
}

/* 推荐列表 */
.recommendations-list-new {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 0;
}

.recommendation-item-new {
    padding: 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 15px;
}

.recommendation-item-new:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.recommendation-item-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.recommendation-item-new:hover:before {
    transform: scaleY(1);
}

.recommendation-content-new {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.recommendation-title-new {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    line-height: 1.5;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

.recommendation-meta-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.recommendation-type-new,
.recommendation-level-new {
    font-size: 12px;
    padding: 4px 12px;
    border-radius: 15px;
    font-weight: 500;
    white-space: nowrap;
}

.recommendation-type-new {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.recommendation-level-new {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
}

.recommendation-price-new {
    font-size: 16px;
    font-weight: 700;
    color: #ff6b35;
    text-align: right;
    white-space: nowrap;
}

/* 确保推荐项目不会溢出 */
.recommendation-item-new {
    padding: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
}

.recommendation-title-new {
    font-size: 13px;
    font-weight: 600;
    color: #333;
    margin: 0;
    line-height: 1.3;
    word-break: break-word;
    overflow-wrap: break-word;
}

.recommendation-meta-new {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    align-items: center;
}

.recommendation-price-new {
    font-size: 13px;
    font-weight: 700;
    color: #ff6b35;
    white-space: nowrap;
}

/* 内容区域响应式设计 */
@media (max-width: 1400px) {
    .main-layout-container {
        gap: 15px;
        max-width: 100%;
        padding: 0 10px;
    }

    .left-main-content {
        width: calc(100% - 380px);
        max-width: calc(100% - 380px);
    }

    .right-sidebar-content {
        flex: 0 0 360px;
        width: 360px;
        min-width: 360px;
        max-width: 360px;
        position: sticky;
    }
}

    .content-layout-new {
        gap: 12px;
    }

    .schedule-grid-new {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .sidebar-content-new {
        min-width: 240px;
    }
}

/* 中等屏幕优化 */
@media (max-width: 1200px) {
    .main-layout-container {
        gap: 12px;
        padding: 0 8px;
    }

    .left-main-content {
        width: calc(100% - 340px);
        max-width: calc(100% - 340px);
    }

    .right-sidebar-content {
        flex: 0 0 320px;
        width: 320px;
        min-width: 320px;
        max-width: 320px;
    }

    .info-grid-new {
        gap: 8px;
    }

    .info-item-new {
        padding: 8px;
    }

    .topModuleSco .leftBox {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .main-layout-container {
        flex-direction: column !important;
        gap: 20px !important;
    }

    .left-main-content,
    .right-sidebar-content {
        flex: none !important;
        min-width: auto !important;
        max-width: none !important;
        width: 100% !important;
        position: static !important;
    }

    .right-sidebar-content {
        order: 2 !important;
        margin-top: 20px !important;
    }

    .left-main-content {
        order: 1 !important;
    }

    .topModuleSco .leftBox {
        padding: 20px;
    }

    .content-layout-new {
        flex-direction: column;
        gap: 20px;
        margin-top: 20px;
    }

    .main-content-new,
    .sidebar-content-new {
        flex: none;
        min-width: auto;
    }

    .section-title-new {
        font-size: 18px;
        padding: 15px 20px 12px;
        text-align: center;
    }

    .section-title-new:after {
        left: 50%;
        transform: translateX(-50%);
    }

    .section-content-new {
        padding: 15px 20px 20px;
        font-size: 14px;
    }

    .schedule-grid-new {
        grid-template-columns: 1fr;
    }

    .schedule-item-new {
        padding: 12px;
    }

    .section-header-new {
        padding: 15px 20px 12px;
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .recommendations-list-new {
        gap: 12px;
    }

    .recommendation-item-new {
        padding: 12px;
    }

    .recommendation-title-new {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .section-title-new {
        font-size: 16px;
        padding: 12px 15px 10px;
    }

    .section-content-new {
        padding: 12px 15px 15px;
        font-size: 13px;
    }

    .schedule-item-new {
        padding: 10px;
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .schedule-icon-new {
        margin-right: 0;
        margin-bottom: 4px;
    }

    .recommendation-item-new {
        padding: 10px;
    }

    .recommendation-title-new {
        font-size: 13px;
    }

    .recommendation-meta-new {
        justify-content: center;
    }

    .recommendation-price-new {
        text-align: center;
        font-size: 13px;
    }
}
textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus,
.uneditable-input:focus {
    border-color:none;
    outline: none;
    outline: none;
    /* IE6-9 */
  
    -webkit-box-shadow:none;
       -moz-box-shadow: none;
            box-shadow: none;
}
/* 防止内容重叠的额外样式 */
.topModuleSco {
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

.topModuleSco .leftBox {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

/* 强制布局分离 */
.main-layout-container {
    display: flex !important;
    gap: 20px !important;
    align-items: flex-start !important;
    width: 100% !important;
    max-width: 1400px !important;
    margin: 0 auto !important;
    box-sizing: border-box !important;
    position: relative !important;
}

.left-main-content {
    flex: 1 !important;
    min-width: 0 !important;
    width: calc(100% - 320px) !important;
    max-width: calc(100% - 320px) !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

.right-sidebar-content {
    flex: 0 0 300px !important;
    width: 300px !important;
    min-width: 300px !important;
    max-width: 300px !important;
    position: sticky !important;
    top: 20px !important;
    align-self: flex-start !important;
    height: fit-content !important;
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    animation: slideInFromRight 0.6s ease-out !important;
    transition: all 0.3s ease !important;
    z-index: 10 !important;
}

/* 确保所有内容区域都有正确的盒模型 */
.content-section-new,
.right-sidebar-content .content-section-new,
.recommendations-list-new,
.recommendation-item-new {
    box-sizing: border-box;
    width: 100%;
}

/* 防止文本溢出 */
.recommendation-title-new,
.training-name,
.section-title-new {
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    hyphens: auto;
}

/* 左侧内容区域强制约束 */
.leftBox {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

.leftBox .pa {
    position: relative !important;
}

/* 信息网格强制约束 */
.info-grid-new {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

.info-item-new {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

/* 标题区域强制约束 */
.title-section-new,
.training-title-new {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    word-wrap: break-word !important;
}

/* 清除可能的浮动影响 */
.main-layout-container::after {
    content: "";
    display: table;
    clear: both;
}

.left-main-content,
.right-sidebar-content {
    float: none !important;
    clear: none !important;
}

/* 确保容器不会被子元素撑破 */
.conAuto3 {
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

.mb30 {
    margin-bottom: 30px;
}

/* 强制重置可能冲突的样式 */
.pa {
    position: relative !important;
}

/* 样式覆盖 */
