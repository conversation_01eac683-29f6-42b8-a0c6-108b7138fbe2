<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.TrainingOrderMapper">
    
    <resultMap type="TrainingOrder" id="TrainingOrderResult">
        <result property="orderId"              column="order_id"              />
        <result property="orderTitle"           column="order_title"           />
        <result property="orderDescription"     column="order_description"     />
        <result property="trainingType"         column="training_type"         />
        <result property="trainingCategory"     column="training_category"     />
        <result property="trainingLevel"        column="training_level"        />
        <result property="trainingDuration"     column="training_duration"     />
        <result property="maxParticipants"      column="max_participants"      />
        <result property="currentParticipants"  column="current_participants"  />
        <result property="trainingFee"          column="training_fee"          />
        <result property="trainingAddress"      column="training_address"      />
        <result property="contactPerson"        column="contact_person"        />
        <result property="contactPhone"         column="contact_phone"         />
        <result property="contactEmail"         column="contact_email"         />
        <result property="startDate"            column="start_date"            />
        <result property="endDate"              column="end_date"              />
        <result property="registrationDeadline" column="registration_deadline" />
        <result property="orderStatus"          column="order_status"          />
        <result property="isFeatured"           column="is_featured"           />
        <result property="requirements"         column="requirements"          />
        <result property="certificateInfo"      column="certificate_info"      />
        <result property="delFlag"              column="del_flag"              />
        <result property="createId"             column="create_id"             />
        <result property="createTime"           column="create_time"           />
        <result property="updateId"             column="update_id"             />
        <result property="updateTime"           column="update_time"           />
        <result property="remark"               column="remark"                />
    </resultMap>

    <sql id="selectTrainingOrderVo">
        select order_id, order_title, order_description, training_type, training_category, training_level, 
               training_duration, max_participants, current_participants, training_fee, training_address, 
               contact_person, contact_phone, contact_email, start_date, end_date, registration_deadline, 
               order_status, is_featured, requirements, certificate_info, del_flag, create_id, create_time, 
               update_id, update_time, remark 
        from training_order
    </sql>

    <select id="selectTrainingOrderList" parameterType="TrainingOrder" resultMap="TrainingOrderResult">
        <include refid="selectTrainingOrderVo"/>
        <where>  
            del_flag = '0'
            <if test="orderTitle != null  and orderTitle != ''"> and order_title like concat('%', #{orderTitle}, '%')</if>
            <if test="trainingType != null  and trainingType != ''"> and training_type = #{trainingType}</if>
            <if test="trainingCategory != null  and trainingCategory != ''"> and training_category = #{trainingCategory}</if>
            <if test="trainingLevel != null  and trainingLevel != ''"> and training_level = #{trainingLevel}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
            <if test="isFeatured != null  and isFeatured != ''"> and is_featured = #{isFeatured}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND start_date &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND start_date &lt;= #{params.endTime}
            </if>
        </where>
        order by create_time desc, order_id desc
    </select>
    
    <select id="selectTrainingOrderByOrderId" parameterType="Long" resultMap="TrainingOrderResult">
        <include refid="selectTrainingOrderVo"/>
        where order_id = #{orderId} and del_flag = '0'
    </select>
        
    <insert id="insertTrainingOrder" parameterType="TrainingOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into training_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderTitle != null and orderTitle != ''">order_title,</if>
            <if test="orderDescription != null">order_description,</if>
            <if test="trainingType != null">training_type,</if>
            <if test="trainingCategory != null">training_category,</if>
            <if test="trainingLevel != null">training_level,</if>
            <if test="trainingDuration != null">training_duration,</if>
            <if test="maxParticipants != null">max_participants,</if>
            <if test="currentParticipants != null">current_participants,</if>
            <if test="trainingFee != null">training_fee,</if>
            <if test="trainingAddress != null">training_address,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="registrationDeadline != null">registration_deadline,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="isFeatured != null">is_featured,</if>
            <if test="requirements != null">requirements,</if>
            <if test="certificateInfo != null">certificate_info,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderTitle != null and orderTitle != ''">#{orderTitle},</if>
            <if test="orderDescription != null">#{orderDescription},</if>
            <if test="trainingType != null">#{trainingType},</if>
            <if test="trainingCategory != null">#{trainingCategory},</if>
            <if test="trainingLevel != null">#{trainingLevel},</if>
            <if test="trainingDuration != null">#{trainingDuration},</if>
            <if test="maxParticipants != null">#{maxParticipants},</if>
            <if test="currentParticipants != null">#{currentParticipants},</if>
            <if test="trainingFee != null">#{trainingFee},</if>
            <if test="trainingAddress != null">#{trainingAddress},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="registrationDeadline != null">#{registrationDeadline},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="isFeatured != null">#{isFeatured},</if>
            <if test="requirements != null">#{requirements},</if>
            <if test="certificateInfo != null">#{certificateInfo},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTrainingOrder" parameterType="TrainingOrder">
        update training_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderTitle != null and orderTitle != ''">order_title = #{orderTitle},</if>
            <if test="orderDescription != null">order_description = #{orderDescription},</if>
            <if test="trainingType != null">training_type = #{trainingType},</if>
            <if test="trainingCategory != null">training_category = #{trainingCategory},</if>
            <if test="trainingLevel != null">training_level = #{trainingLevel},</if>
            <if test="trainingDuration != null">training_duration = #{trainingDuration},</if>
            <if test="maxParticipants != null">max_participants = #{maxParticipants},</if>
            <if test="currentParticipants != null">current_participants = #{currentParticipants},</if>
            <if test="trainingFee != null">training_fee = #{trainingFee},</if>
            <if test="trainingAddress != null">training_address = #{trainingAddress},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="registrationDeadline != null">registration_deadline = #{registrationDeadline},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="isFeatured != null">is_featured = #{isFeatured},</if>
            <if test="requirements != null">requirements = #{requirements},</if>
            <if test="certificateInfo != null">certificate_info = #{certificateInfo},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where order_id = #{orderId}
    </update>

    <delete id="deleteTrainingOrderByOrderId" parameterType="Long">
        update training_order set del_flag = '2' where order_id = #{orderId}
    </delete>

    <delete id="deleteTrainingOrderByOrderIds" parameterType="String">
        update training_order set del_flag = '2' where order_id in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

    <select id="checkOrderTitleUnique" parameterType="TrainingOrder" resultMap="TrainingOrderResult">
        <include refid="selectTrainingOrderVo"/>
        where order_title = #{orderTitle} and del_flag = '0'
        <if test="orderId != null and orderId != 0">
            and order_id != #{orderId}
        </if>
        limit 1
    </select>

    <update id="updateCurrentParticipants">
        update training_order
        set current_participants = current_participants + #{increment}
        where order_id = #{orderId} and del_flag = '0'
    </update>

    <update id="updateCurrentParticipantsCount">
        update training_order
        set current_participants = #{count}
        where order_id = #{orderId} and del_flag = '0'
    </update>

    <select id="selectUpcomingTrainingOrders" parameterType="int" resultMap="TrainingOrderResult">
        <include refid="selectTrainingOrderVo"/>
        where del_flag = '0' 
        and order_status = '1'
        and start_date between now() and date_add(now(), interval #{days} day)
        order by start_date asc
    </select>

    <select id="selectExpiredTrainingOrders" resultMap="TrainingOrderResult">
        <include refid="selectTrainingOrderVo"/>
        where del_flag = '0' 
        and order_status = '1'
        and registration_deadline &lt; now()
        order by registration_deadline desc
    </select>

    <select id="selectOrderStatusStatistics" resultMap="TrainingOrderResult">
        select order_status, count(*) as current_participants
        from training_order 
        where del_flag = '0'
        group by order_status
    </select>

</mapper>
