<template>
  <div class="post-container app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="tableData" :loading="tableLoading" :showIndex="true"
      :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作" operationWidth="200"
      :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: pageSize, currentPage: currentPage, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['system:post:add']">新 增</el-button>
        <el-button type="warning" class="custom-btn" plain @click="handleExport" v-hasPermi="['system:post:export']">导
          出</el-button>
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <el-button type="primary" link @click="handleView(row)" v-hasPermi="['system:post:query']">查看</el-button>
          <el-button type="primary" link @click="handleEdit(row)" v-hasPermi="['system:post:edit']">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)" v-hasPermi="['system:post:remove']">删除</el-button>
        </div>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-switch v-if="row && row.postId && row.postName" v-model="row.status" active-value="0" inactive-value="1"
          @change="handleStatusChange(row)" :disabled="!checkPermi(['system:post:edit'])" />
        <span v-else>-</span>
      </template>

      <!-- 创建时间列插槽 -->
      <template #createTime="{ row }">
        <span>{{ parseTime(row.createTime) }}</span>
      </template>
    </TableList>

    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..." />
    </div>

    <!-- 表单弹窗组件 -->
    <PostFormDialog ref="postFormDialogRef" :formFields="formFields" :formOption="formOption" @submit="handleFormSubmit"
      @cancel="handleFormCancel" />
  </div>
</template>

<script setup name="Post">
import { ref, reactive, onMounted, getCurrentInstance, computed } from 'vue';
import { listPost, addPost, delPost, getPost, updatePost } from "@/api/system/post";
import { createPostTableOption } from "@/const/system/post";
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils";
import TableList from '@/components/TableList/index.vue';
import PostFormDialog from './PostFormDialog.vue';

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

// 表格相关
const tableColumns = ref([]);
const searchableColumns = ref([]);
const tableLoading = ref(false);
const isTableReady = ref(false);
const tableListRef = ref(null);
const postFormDialogRef = ref(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref({});

// 表格数据
const tableData = ref([]);
const selectedRows = ref([]);
const single = ref(true);
const multiple = ref(true);

// 初始化状态标志，防止初始化时触发状态变更
const isInitializing = ref(false);

// 表单配置
const formOption = ref({
  dialogWidth: '600px',
  dialogHeight: '65vh'
});
const formFields = ref([]);

// 初始化
onMounted(async () => {
  await initializeConfig();
  getList();
});

// 初始化配置
const initializeConfig = async () => {
  try {
    // 获取基础配置
    const baseOption = createPostTableOption(proxy);

    // 使用工具类获取合并后的配置
    const mergedConfig = await getCoSyncColumn({
      baseOption,
      proxy
    });

    // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
    const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

    // 设置表格和搜索配置
    tableColumns.value = extractedTableColumns;
    searchableColumns.value = searchColumns;

    // 设置表单字段配置
    formFields.value = extractedFormFields;

    // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
    formOption.value = {
      ...formOption.value, // 保留默认配置
      ...formOptions       // 使用从配置文件中提取的完整选项
    };

    isTableReady.value = true;
  } catch (error) {
    isTableReady.value = false;
    console.error('初始化配置失败:', error);
    proxy.$modal.msgError('表格配置加载失败');
  }
};

// 查询岗位列表
const getList = () => {
  isInitializing.value = true; // 设置初始化状态
  tableLoading.value = true;
  const params = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...searchParams.value
  };

  listPost(params).then(response => {
    tableData.value = response.rows;
    total.value = response.total;
    tableLoading.value = false;
    // 延迟重置初始化状态，确保组件完全渲染完毕
    setTimeout(() => {
      isInitializing.value = false;
    }, 100);
  }).catch(() => {
    tableLoading.value = false;
    isInitializing.value = false;
    proxy.$modal.msgError('数据加载失败');
  });
};

// 搜索处理
const handleSearch = (params) => {
  searchParams.value = params;
  currentPage.value = 1;
  getList();
};

// 重置搜索
const resetSearch = () => {
  searchParams.value = {};
  currentPage.value = 1;
  getList();
};

// 分页处理
const handleCurrentChange = (page) => {
  currentPage.value = page;
  getList();
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  getList();
};

// 多选处理
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 权限检查函数
const checkPermi = (perms) => {
  return proxy.$auth.hasPermi(perms);
};

// 状态切换处理
const handleStatusChange = (row) => {
  // 如果正在初始化，则忽略状态变更事件
  if (isInitializing.value) {
    return;
  }

  // 数据验证 - 确保必要的数据存在
  if (!row || !row.postId) {
    return;
  }

  // 防止 postName 为 undefined 的情况
  const postName = row.postName || `ID为${row.postId}的岗位` || '该岗位';
  const text = row.status === "0" ? "启用" : "停用";

  proxy.$modal.confirm(`确认要${text}"${postName}"岗位吗？`).then(() => {
    return updatePost(row);
  }).then(() => {
    proxy.$modal.msgSuccess(`${text}成功`);
    getList();
  }).catch(() => {
    row.status = row.status === "0" ? "1" : "0";
  });
};

// 新增
const handleAdd = () => {
  postFormDialogRef.value.openDialog('add', '新增岗位', {
    postSort: 0,
    status: '0'
  });
};

// 查看
const handleView = (row) => {
  const postId = row.postId;
  getPost(postId).then(response => {
    postFormDialogRef.value.openDialog('view', '查看岗位', response.data);
  }).catch(() => {
    proxy.$modal.msgError('数据加载失败');
  });
};

// 编辑
const handleEdit = (row) => {
  const postId = row.postId;
  getPost(postId).then(response => {
    postFormDialogRef.value.openDialog('edit', '编辑岗位', response.data);
  }).catch(() => {
    proxy.$modal.msgError('数据加载失败');
  });
};

// 修改（从按钮触发）
const handleUpdate = () => {
  if (selectedRows.value.length === 1) {
    handleEdit(selectedRows.value[0]);
  }
};

// 删除
const handleDelete = (row) => {
  const postIds = row ? [row.postId] : selectedRows.value.map(item => item.postId);
  const postNames = row ? [row.postName] : selectedRows.value.map(item => item.postName);

  proxy.$modal.confirm(`是否确认删除岗位"${postNames.join('、')}"？`).then(() => {
    return delPost(postIds.join(','));
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
};

// 导出
const handleExport = () => {
  proxy.download("system/post/export", {
    ...searchParams.value
  }, `post_${new Date().getTime()}.xlsx`);
};

// 表单提交处理
const handleFormSubmit = async (formData) => {
  try {
    if (formData.type === 'add') {
      await addPost(formData.data);
      proxy.$modal.msgSuccess("新增成功");
    } else if (formData.type === 'edit') {
      await updatePost(formData.data);
      proxy.$modal.msgSuccess("修改成功");
    }

    postFormDialogRef.value.closeDialog();
    getList();
  } catch (error) {
    console.error('提交失败:', error);
  }
};

// 表单取消处理
const handleFormCancel = () => {
  postFormDialogRef.value.closeDialog();
};
</script>

<style lang="scss" scoped>
.post-container {
  .operation-btns {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;

    .op-btn {
      padding: 2px 4px;
      font-size: 12px;
      border-radius: 3px;

      &:hover {
        background-color: var(--el-color-primary-light-9);
      }
    }
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    background-color: var(--el-bg-color-page);
    border-radius: 4px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .post-container {

    .operation-btns {
      flex-direction: column;
      gap: 4px;

      .op-btn {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

// 暗黑模式适配
@media (prefers-color-scheme: dark) {
  .post-container {
    .loading-placeholder {
      background-color: var(--el-bg-color-overlay);
    }

    .operation-btns .op-btn:hover {
      background-color: var(--el-color-primary-dark-2);
    }
  }
}
</style>
