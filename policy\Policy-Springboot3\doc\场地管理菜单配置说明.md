# 场地管理菜单配置说明

## 概述
本文档描述了场地管理模块的菜单配置，包括用工信息、场地信息和劳务市场三个子模块的菜单结构和权限配置。

## 菜单结构

### 主菜单
- **场地管理** (menu_id: 4000)
  - 路由地址: `place`
  - 图标: `build`
  - 排序: 3
  - 类型: 目录菜单 (M)

### 子菜单

#### 1. 用工信息管理 (menu_id: 4001)
- **菜单名称**: 用工信息
- **路由地址**: `employment`
- **组件路径**: `place/employment/index`
- **权限标识**: `place:employment:list`
- **图标**: `peoples`
- **功能描述**: 管理用工信息，包括招聘信息发布、审核、统计等功能

**权限按钮**:
- 查询 (4101): `place:employment:query`
- 新增 (4102): `place:employment:add`
- 修改 (4103): `place:employment:edit`
- 删除 (4104): `place:employment:remove`
- 导出 (4105): `place:employment:export`
- 审核 (4106): `place:employment:review`
- 发布 (4107): `place:employment:publish`
- 统计 (4108): `place:employment:statistics`

#### 2. 场地信息管理 (menu_id: 4002)
- **菜单名称**: 场地信息
- **路由地址**: `info`
- **组件路径**: `place/info/index`
- **权限标识**: `place:info:list`
- **图标**: `component`
- **功能描述**: 管理场地基础信息，包括场地类型、等级、区域等信息维护

**权限按钮**:
- 查询 (4201): `place:info:query`
- 新增 (4202): `place:info:add`
- 修改 (4203): `place:info:edit`
- 删除 (4204): `place:info:remove`
- 导出 (4205): `place:info:export`
- 审核 (4206): `place:info:review`
- 统计 (4207): `place:info:statistics`

#### 3. 劳务市场管理 (menu_id: 4003)
- **菜单名称**: 劳务市场
- **路由地址**: `market`
- **组件路径**: `place/market/index`
- **权限标识**: `place:market:list`
- **图标**: `shopping`
- **功能描述**: 管理劳务市场信息，包括市场类型、区域分布、统计分析等

**权限按钮**:
- 查询 (4301): `place:market:query`
- 新增 (4302): `place:market:add`
- 修改 (4303): `place:market:edit`
- 删除 (4304): `place:market:remove`
- 导出 (4305): `place:market:export`
- 审核 (4306): `place:market:review`
- 统计 (4307): `place:market:statistics`

## 安装步骤

### 1. 执行SQL脚本
```sql
-- 执行菜单配置SQL
source policy/Policy-Springboot3/doc/place_management_menu.sql;
```

### 2. 验证菜单安装
```sql
-- 查询场地管理相关菜单
SELECT menu_id, menu_name, parent_id, path, component, perms, icon, order_num 
FROM sys_menu 
WHERE menu_id BETWEEN 4000 AND 4999 
ORDER BY menu_id;
```

### 3. 角色权限配置
在系统管理 -> 角色管理中，为相应角色分配场地管理相关权限：

**管理员角色建议权限**:
- 场地管理 (完整权限)
  - 用工信息 (所有操作权限)
  - 场地信息 (所有操作权限)
  - 劳务市场 (所有操作权限)

**普通用户角色建议权限**:
- 场地管理 (查看权限)
  - 用工信息 (查询权限)
  - 场地信息 (查询权限)
  - 劳务市场 (查询权限)

## 前端组件对应关系

### Vue组件路径
- 用工信息: `policy/Policy-Vue3/src/views/place/employment/index.vue`
- 场地信息: `policy/Policy-Vue3/src/views/place/info/index.vue`
- 劳务市场: `policy/Policy-Vue3/src/views/place/market/index.vue`

### 路由配置
前端路由会自动根据菜单配置生成，无需手动配置。系统会根据用户权限动态加载对应的菜单和路由。

## 权限控制

### 后端权限控制
在Controller中使用`@PreAuthorize`注解进行权限控制：

```java
// 用工信息列表查询
@PreAuthorize("@ss.hasPermi('place:employment:list')")
@GetMapping("/list")
public TableDataInfo list(EmploymentInfo employmentInfo) {
    // 实现代码
}

// 用工信息新增
@PreAuthorize("@ss.hasPermi('place:employment:add')")
@PostMapping
public AjaxResult add(@RequestBody EmploymentInfo employmentInfo) {
    // 实现代码
}
```

### 前端权限控制
在Vue组件中使用`v-hasPermi`指令进行按钮权限控制：

```vue
<!-- 新增按钮 -->
<el-button 
  type="primary" 
  plain 
  icon="Plus" 
  @click="handleAdd" 
  v-hasPermi="['place:employment:add']">
  新增
</el-button>

<!-- 修改按钮 -->
<el-button 
  type="success" 
  plain 
  icon="Edit" 
  :disabled="single" 
  @click="handleUpdate"
  v-hasPermi="['place:employment:edit']">
  修改
</el-button>
```

## 注意事项

1. **菜单ID范围**: 场地管理模块使用4000-4999的菜单ID范围，避免与其他模块冲突
2. **权限命名规范**: 使用`place:模块:操作`的命名规范，如`place:employment:add`
3. **图标选择**: 选择了与功能相符的图标，可根据实际需要调整
4. **排序设置**: 主菜单排序为3，子菜单按功能重要性排序
5. **组件路径**: 确保Vue组件路径与菜单配置中的component字段一致

## 扩展说明

如需添加新的功能按钮或子菜单，请遵循以下规则：
- 菜单ID继续使用4000-4999范围内的数字
- 权限标识遵循`place:模块:操作`的命名规范
- 保持与现有菜单结构的一致性

## 故障排除

### 常见问题
1. **菜单不显示**: 检查用户角色是否分配了相应权限
2. **按钮不显示**: 检查前端组件中的`v-hasPermi`权限配置
3. **路由404**: 检查Vue组件路径是否正确，组件文件是否存在
4. **权限验证失败**: 检查后端Controller中的`@PreAuthorize`注解配置

### 调试方法
1. 查看浏览器控制台错误信息
2. 检查后端日志中的权限验证信息
3. 使用开发者工具检查网络请求和响应
4. 验证数据库中的菜单和权限配置是否正确
