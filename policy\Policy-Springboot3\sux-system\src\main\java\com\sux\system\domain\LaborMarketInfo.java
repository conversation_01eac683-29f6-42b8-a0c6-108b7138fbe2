package com.sux.system.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;


/**
 * 零工市场基础信息对象 labor_market_info
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@TableName("labor_market_info")
public class LaborMarketInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 市场ID */
    @TableId(type = IdType.AUTO)
    private Long marketId;

    /** 市场名称 */
    @Excel(name = "市场名称")
    @NotBlank(message = "市场名称不能为空")
    @Size(min = 2, max = 200, message = "市场名称长度必须介于 2 和 200 之间")
    private String marketName;

    /** 市场编码 */
    @Excel(name = "市场编码")
    @Size(min = 0, max = 50, message = "市场编码不能超过50个字符")
    private String marketCode;

    /** 市场类型（综合市场/专业市场/临时市场） */
    @Excel(name = "市场类型")
    @NotBlank(message = "市场类型不能为空")
    @Size(min = 0, max = 50, message = "市场类型不能超过50个字符")
    private String marketType;

    /** 市场地址 */
    @Excel(name = "市场地址")
    @NotBlank(message = "市场地址不能为空")
    @Size(min = 0, max = 500, message = "市场地址不能超过500个字符")
    private String address;

    /** 区域代码 */
    @Excel(name = "区域代码")
    @Size(min = 0, max = 20, message = "区域代码不能超过20个字符")
    private String regionCode;

    /** 区域名称 */
    @Excel(name = "区域名称")
    @Size(min = 0, max = 100, message = "区域名称不能超过100个字符")
    private String regionName;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 联系人 */
    @Excel(name = "联系人")
    @Size(min = 0, max = 100, message = "联系人不能超过100个字符")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @Size(min = 0, max = 20, message = "联系电话不能超过20个字符")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    @Size(min = 0, max = 100, message = "联系邮箱不能超过100个字符")
    private String contactEmail;

    /** 营业时间 */
    @Excel(name = "营业时间")
    @Size(min = 0, max = 200, message = "营业时间不能超过200个字符")
    private String operatingHours;

    /** 服务类别（JSON格式存储） */
    @Excel(name = "服务类别")
    private String serviceCategories;

    /** 零工容纳量 */
    @Excel(name = "零工容纳量")
    private Integer workerCapacity;

    /** 当前零工数量 */
    @Excel(name = "当前零工数量")
    private Integer currentWorkerCount;

    /** 日均用工需求 */
    @Excel(name = "日均用工需求")
    private Integer dailyAvgDemand;

    /** 用工高峰时段 */
    @Excel(name = "用工高峰时段")
    @Size(min = 0, max = 100, message = "用工高峰时段不能超过100个字符")
    private String peakDemandTime;

    /** 管理费用（元/人/天） */
    @Excel(name = "管理费用")
    private BigDecimal managementFee;

    /** 服务费率（%） */
    @Excel(name = "服务费率")
    private BigDecimal serviceFeeRate;

    /** 配套设施（JSON格式存储） */
    @Excel(name = "配套设施")
    private String facilities;

    /** 安全措施描述 */
    @Excel(name = "安全措施描述")
    private String safetyMeasures;

    /** 市场主图片URL */
    @Excel(name = "市场主图片URL")
    @Size(min = 0, max = 500, message = "市场主图片URL不能超过500个字符")
    private String imageUrl;

    /** 市场图片集（JSON格式存储多张图片URL） */
    @Excel(name = "市场图片集")
    private String imageGallery;

    /** 市场详细描述 */
    @Excel(name = "市场详细描述")
    private String description;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 是否推荐（0否 1是） */
    @Excel(name = "是否推荐", readConverterExp = "0=否,1=是")
    private Integer isFeatured;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer sortOrder;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("marketId", getMarketId())
            .append("marketName", getMarketName())
            .append("marketCode", getMarketCode())
            .append("marketType", getMarketType())
            .append("address", getAddress())
            .append("regionCode", getRegionCode())
            .append("regionName", getRegionName())
            .append("longitude", getLongitude())
            .append("latitude", getLatitude())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("contactEmail", getContactEmail())
            .append("operatingHours", getOperatingHours())
            .append("serviceCategories", getServiceCategories())
            .append("workerCapacity", getWorkerCapacity())
            .append("currentWorkerCount", getCurrentWorkerCount())
            .append("dailyAvgDemand", getDailyAvgDemand())
            .append("peakDemandTime", getPeakDemandTime())
            .append("managementFee", getManagementFee())
            .append("serviceFeeRate", getServiceFeeRate())
            .append("facilities", getFacilities())
            .append("safetyMeasures", getSafetyMeasures())
            .append("imageUrl", getImageUrl())
            .append("imageGallery", getImageGallery())
            .append("description", getDescription())
            .append("status", getStatus())
            .append("isFeatured", getIsFeatured())
            .append("sortOrder", getSortOrder())
            .append("viewCount", getViewCount())
            .append("createId", getCreateId())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("remark", getRemark())
            .toString();
    }
}
