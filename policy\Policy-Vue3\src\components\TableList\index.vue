<template>
    <div class="table-container">
        <!-- 搜索区域 -->
        <div class="search-container" v-if="searchFields.length > 0 && isShowSearch">
            <el-form :model="searchParams" ref="searchForm" @keyup.enter="handleSearch">
                <div class="search-form-wrapper">
                    <template v-for="(item, index) in searchFields" :key="index">
                        <div class="search-field-item">
                            <el-form-item :prop="item.prop" :label="item.label">
                                <!-- 根据字段类型渲染不同搜索组件 -->
                                <!-- 自定义插槽 -->
                                <slot v-if="item.searchSlot" :name="'search-' + item.prop" :row="searchParams">
                                </slot>
                                <template v-else-if="item.type === 'select'">
                                    <el-select v-model="searchParams[item.prop]" :placeholder="'请选择' + item.label"
                                        clearable class="search-field-width" :multiple="item.searchMultiple"
                                        :style="{ width: item.searchWidth || '150px' }">
                                        <el-option v-for="dict in item.dicData" :key="dict.value" :label="dict.label"
                                            :value="dict.value" />
                                    </el-select>
                                </template>
                                <!-- 日期范围 -->
                                <template
                                    v-else-if="item.type === 'daterange' || item.type === 'date' || item.type === 'datetime'">
                                    <el-date-picker v-model="searchParams[item.prop]" type="daterange"
                                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable
                                        class="search-field-width" :style="{ width: item.searchWidth || '260px' }" />
                                </template>
                                <template v-else>
                                    <el-input v-model="searchParams[item.prop]" :placeholder="'请输入' + item.label"
                                        clearable class="search-field-width"
                                        :style="{ width: item.searchWidth || '150px' }" />
                                </template>
                            </el-form-item>
                        </div>
                    </template>
                    <!-- 搜索按钮区域 -->
                    <div class="search-button-wrapper">
                        <el-form-item>
                            <el-button type="primary" class="custom-btn" @click="handleSearch">搜 索</el-button>
                            <el-button class="custom-btn" @click="resetSearch">重 置</el-button>
                            <el-button type="text" size="small" v-if="showToggleSearch && !searchCollapse"
                                @click="toggleSearchCollapse">
                                收 起
                            </el-button>
                            <el-button type="text" size="small" v-if="showToggleSearch && searchCollapse"
                                @click="toggleSearchCollapse">
                                展 开
                            </el-button>
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </div>

        <!-- 操作按钮区 -->
        <div class="table-header">
            <div class="left-buttons">
                <slot name="menu-left"></slot>
            </div>
            <div class="right-buttons">
                <slot name="menu-right"></slot>
            </div>
        </div>

        <!-- 表格 -->
        <el-table v-loading="tableLoading" :data="tableData" style="width: 100%"
            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }" @selection-change="handleSelectionChange"
            :max-height="tableHeight" :min-height="tableHeight ? undefined : 'auto'" :span-method="spanMethod"
            :scrollbar-always-on="true" :flexible="true" empty-text="暂无数据">
            <!-- 多选列 -->
            <el-table-column v-if="showSelection" type="selection" width="55" align="center" />

            <!-- 动态列 -->
            <template v-for="(column, index) in tableColumns" :key="index">
                <el-table-column :prop="column.prop" :label="column.label" :width="column.width"
                    :min-width="column.minWidth || '100'" :align="column.align || 'center'" :sortable="column.sortable"
                    :show-overflow-tooltip="column.showOverflowTooltip !== false">
                    <template #default="scope">
                        <!-- 不同类型的显示方式 -->
                        <!-- 自定义插槽 -->
                        <template v-if="column.tableSlot">
                            <slot :name="column.prop" :row="scope.row" :index="scope.$index"></slot>
                        </template>
                        <template v-else-if="column.type === 'date'">
                            {{ parseTime(scope.row[column.prop], '{y}-{m}-{d}') }}
                        </template>
                        <template v-else-if="column.type === 'datetime'">
                            {{ parseTime(scope.row[column.prop], '{y}-{m}-{d} {h}:{i}:{s}') }}
                        </template>
                        <!-- 字典翻译 -->
                        <template v-else-if="column.dicData">
                            {{ getDictLabel(column.dicData, scope.row[column.prop]) }}
                        </template>
                        <!-- 默认显示 -->
                        <template v-else>
                            {{ scope.row[column.prop] }}
                        </template>
                    </template>
                </el-table-column>
            </template>

            <!-- 操作列 -->
            <el-table-column v-if="showOperation" :label="operationLabel" :width="operationWidth" align="center"
                :fixed="fixedOperation ? 'right' : false">
                <template #default="scope">
                    <slot name="menu" :row="scope.row" :index="scope.$index"></slot>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container" v-if="showPagination">
            <el-pagination v-model:current-page="page.currentPage" v-model:page-size="page.pageSize"
                :page-sizes="[10, 20, 50, 100]" :layout="paginationLayout" :total="page.total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" background />
        </div>

        <!-- 表单弹窗 -->
        <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增' : dialogType === 'edit' ? '编辑' : '查看'"
            :width="dialogWidth" destroy-on-close append-to-body>
            <component :is="formComponent" v-if="dynamicForm && dialogVisible" ref="formRef" v-bind="formProps"
                @submit="handleFormSubmit" @cancel="closeDialog" />
            <template v-else>
                <slot name="form" :form="form" :type="dialogType"></slot>
            </template>
            <template #footer v-if="!hideDialogFooter">
                <div class="dialog-footer">
                    <el-button @click="closeDialog">取消</el-button>
                    <el-button type="primary" @click="submitForm" v-if="dialogType !== 'view'">确认</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, computed, watch, getCurrentInstance, onMounted, defineAsyncComponent } from 'vue';

const props = defineProps({
    // 表格列配置
    columns: {
        type: Array,
        default: () => []
    },
    // 是否显示搜索栏
    isShowSearch: {
        type: Boolean,
        default: true
    },
    // 是否需要从后端获取列配置
    fetchColumns: {
        type: Boolean,
        default: false
    },
    // 菜单代码，用于获取后端配置
    menuCode: {
        type: String,
        default: ''
    },
    // 搜索栏字段配置
    searchColumns: {
        type: Array,
        default: null // 如果为null，则使用columns中search为true的字段
    },
    // 表格数据
    data: {
        type: Array,
        default: () => []
    },
    // 表格高度
    tableHeight: {
        type: String,
    },
    // 是否显示加载状态
    loading: {
        type: Boolean,
        default: false
    },
    // 是否显示序号列
    showIndex: {
        type: Boolean,
        default: true
    },
    // 是否显示多选列
    showSelection: {
        type: Boolean,
        default: false
    },
    // 是否显示操作列
    showOperation: {
        type: Boolean,
        default: true
    },
    // 操作列标题
    operationLabel: {
        type: String,
        default: '操作'
    },
    // 操作列宽度
    operationWidth: {
        type: String,
        default: '220'
    },
    // 是否显示分页
    showPagination: {
        type: Boolean,
        default: true
    },
    // 分页布局
    paginationLayout: {
        type: String,
        default: 'total, sizes, prev, pager, next, jumper'
    },
    // 表单组件
    formComponent: {
        type: [Object, Function],
        default: null
    },
    // 表单属性
    formProps: {
        type: Object,
        default: () => ({})
    },
    // 是否使用动态表单
    dynamicForm: {
        type: Boolean,
        default: false
    },
    // 对话框宽度
    dialogWidth: {
        type: String,
        default: '50%'
    },
    // 是否隐藏对话框底部按钮
    hideDialogFooter: {
        type: Boolean,
        default: false
    },
    // 初始加载数据的API
    loadDataApi: {
        type: Function
    },
    // 添加数据的API
    addApi: {
        type: Function
    },
    // 更新数据的API
    updateApi: {
        type: Function
    },
    // 删除数据的API
    deleteApi: {
        type: Function
    },
    // 默认分页参数
    defaultPage: {
        type: Object,
        default: () => ({
            pageSize: 10,
            currentPage: 1,
            total: 0
        })
    },
    // 默认排序字段
    defaultSortField: {
        type: String,
        default: 'create_time'
    },
    // 默认排序方向
    defaultSortOrder: {
        type: String,
        default: 'desc'
    },
    // 是否显示收起/展开搜索按钮
    showToggleSearch: {
        type: Boolean,
        default: false
    },
    // 是否固定操作列在右侧
    fixedOperation: {
        type: Boolean,
        default: false
    },
    // 表格跨行方法
    spanMethod: {
        type: [Function, Object],
    },
    // 搜索参数 - 添加这个新的 prop
    searchParams: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits([
    'update:loading',
    'selection-change',
    'row-save',
    'row-update',
    'row-delete',
    'search',
    'reset',
    'load',
    'refresh',
    'current-change',
    'size-change',
    'update:searchParams' // 添加这个新的事件
]);

const { proxy } = getCurrentInstance();

// 表格数据
const state = reactive({
    tableData: props.data || [],
    tableColumns: props.columns || [],
    searchFields: props.searchColumns || [], // 搜索字段
    tableLoading: props.loading,
    searchParams: props.searchParams || {}, // 初始化搜索参数
    page: { ...props.defaultPage },
    dialogVisible: false,
    dialogType: 'add', // add, edit, view
    form: {}, // 表单数据
    selectedRows: [], // 多选选中的行
    searchCollapse: false, // 搜索区域是否收起
    spanMethod: props.spanMethod
});

// 解构响应式变量
const {
    tableData,
    tableColumns,
    searchFields,
    tableLoading,
    searchParams,
    page,
    dialogVisible,
    dialogType,
    form,
    selectedRows,
    searchCollapse,
    spanMethod
} = toRefs(state);

const searchForm = ref(null);
const formRef = ref(null);

// 监听props中data变化
watch(() => props.data, (val) => {
    if (val) {
        state.tableData = val;
    }
}, { deep: true, immediate: true });

// 监听props中loading变化
watch(() => props.loading, (val) => {
    state.tableLoading = val;
}, { immediate: true });

// 监听searchColumns变化
watch(() => props.searchColumns, (val) => {
    if (val && val.length > 0) {
        state.searchFields = val;
    }
}, { deep: true, immediate: true });

// 监听外部searchParams变化
watch(() => props.searchParams, (val) => {
    if (val && Object.keys(val).length > 0) {
        // 避免无限循环，只有当值不同时才更新
        if (JSON.stringify(val) !== JSON.stringify(state.searchParams)) {
            state.searchParams = { ...val };
        }
    }
}, { deep: true, immediate: true });

// 监听defaultPage变化
watch(() => props.defaultPage, (val) => {
    if (val) {
        // 只更新有变化的属性，避免不必要的重新渲染
        if (val.total !== state.page.total ||
            val.currentPage !== state.page.currentPage ||
            val.pageSize !== state.page.pageSize) {
            state.page = { ...val };
        }
    }
}, { deep: true, immediate: true });

// 监听内部searchParams变化，向父组件同步
watch(() => state.searchParams, (val) => {
    // 避免无限循环，只有当值不同时才触发更新
    if (JSON.stringify(val) !== JSON.stringify(props.searchParams)) {
        emit('update:searchParams', { ...val });
    }
}, { deep: true });



// 初始化
onMounted(() => {
    // 如果搜索字段已经传入，立即更新
    if (props.searchColumns && props.searchColumns.length > 0) {
        state.searchFields = props.searchColumns;
    }

    // 如果直接提供了数据，使用传入的数据
    if (props.data && props.data.length > 0) {
        state.tableData = props.data;
    }
    // 如果需要从后端获取列配置
    else if (props.fetchColumns && props.menuCode) {
        getColumnConfig();
    } else {
        // 初始化搜索字段
        initSearchFields();
    }

    // 如果提供了加载API，自动加载数据
    if (props.loadDataApi && state.tableData.length === 0 && !props.data) {
        loadData();
    }
});

// 初始化搜索字段
const initSearchFields = () => {
    if (!props.searchColumns) {
        // 默认使用columns中search为true的字段作为搜索字段
        state.searchFields = state.tableColumns.filter(column => column.search === true);
    }
};

// 获取列配置
const getColumnConfig = async () => {
    try {
        // 假设返回的数据格式符合要求，可能需要适配处理
        state.tableColumns = [];
        initSearchFields();
    } catch (error) {
    }
};

// 加载数据
const loadData = (params = {}) => {
    if (!props.loadDataApi) return;

    const queryParams = {
        ...params,
        pageNum: state.page.currentPage,
        pageSize: state.page.pageSize,
        orderByColumn: props.defaultSortField,
        isAsc: props.defaultSortOrder,
    };

    state.tableLoading = true;
    emit('update:loading', true);

    props.loadDataApi(queryParams)
        .then((response) => {
            state.tableData = response.rows || response.data || [];
            state.page.total = response.total || 0;
            state.tableLoading = false;
            emit('update:loading', false);
            emit('load', { data: state.tableData, total: state.page.total });
        })
        .catch(() => {
            state.tableLoading = false;
            emit('update:loading', false);
        });
};

// 处理搜索
const handleSearch = () => {
    state.page.currentPage = 1;
    const params = { ...state.searchParams };
    loadData(params);
    emit('search', params);
};

// 重置搜索
const resetSearch = () => {
    searchForm.value?.resetFields();
    state.searchParams = {};
    state.page.currentPage = 1;
    loadData();
    emit('update:searchParams', {}); // 重置时也要通知父组件
    emit('reset');
};

// 处理日期范围变化
const handleDateRangeChange = (val, prop) => {
    if (val) {
        state.searchParams[prop] = val;
    } else {
        state.searchParams[prop] = undefined;
    }
};

// 处理选择变化
const handleSelectionChange = (selection) => {
    state.selectedRows = selection;
    emit('selection-change', selection);
};

// 处理页码变化
const handleCurrentChange = (currentPage) => {
    state.page.currentPage = currentPage;
    loadData(state.searchParams);
    emit('current-change', currentPage);
};

// 处理每页大小变化
const handleSizeChange = (pageSize) => {
    state.page.pageSize = pageSize;
    state.page.currentPage = 1;
    loadData(state.searchParams);
    emit('size-change', pageSize);
};

// 获取字典标签
const getDictLabel = (dicData, value) => {
    if (!dicData || value === undefined || value === null || value === '') return '';

    // 处理数组类型
    if (Array.isArray(value)) {
        return value.map(v => {
            const dict = dicData.find(item => item.value == v);
            return dict ? dict.label : v;
        }).join(', ');
    }

    // 处理单个值
    const dict = dicData.find(item => item.value == value);
    return dict ? dict.label : value;
};

// 切换搜索区域收起状态
const toggleSearchCollapse = () => {
    state.searchCollapse = !state.searchCollapse;
};

// 处理新增
const handleAdd = () => {
    state.dialogType = 'add';
    state.form = {};
    state.dialogVisible = true;
};

// 处理编辑
const handleEdit = (row) => {
    state.dialogType = 'edit';
    state.form = { ...row };
    state.dialogVisible = true;
};

// 处理查看
const handleView = (row) => {
    state.dialogType = 'view';
    state.form = { ...row };
    state.dialogVisible = true;
};

// 处理删除
const handleDelete = (row) => {
    if (!props.deleteApi) return;

    const id = row.id || row[Object.keys(row).find(key => key.toLowerCase().includes('id'))];
    proxy.$modal.confirm(`是否确认删除该数据项？`).then(function () {
        return props.deleteApi(id);
    }).then(() => {
        loadData(state.searchParams);
        proxy.$modal.msgSuccess("删除成功");
        emit('row-delete', row);
    }).catch(() => { });
};

// 处理表单提交
const submitForm = () => {
    formRef.value?.submit?.() || handleFormSubmit();
};

// 处理表单提交（从子组件回调）
const handleFormSubmit = (formData) => {
    const data = formData || state.form;

    if (state.dialogType === 'add') {
        handleSave(data);
    } else if (state.dialogType === 'edit') {
        handleUpdate(data);
    }
};

// 处理保存
const handleSave = (data) => {
    if (!props.addApi) {
        closeDialog();
        return;
    }

    props.addApi(data).then(response => {
        proxy.$modal.msgSuccess("添加成功");
        closeDialog();
        loadData(state.searchParams);
        emit('row-save', response);
    }).catch(() => { });
};

// 处理更新
const handleUpdate = (data) => {
    if (!props.updateApi) {
        closeDialog();
        return;
    }

    props.updateApi(data).then(response => {
        proxy.$modal.msgSuccess("修改成功");
        closeDialog();
        loadData(state.searchParams);
        emit('row-update', response);
    }).catch(() => { });
};

// 关闭对话框
const closeDialog = () => {
    state.dialogVisible = false;
    state.form = {};
};

// 刷新
const refresh = () => {
    loadData(state.searchParams);
    emit('refresh');
};

// 暴露给父组件的方法
defineExpose({
    // 暴露搜索方法给父组件调用
    search: (externalParams) => {
        if (externalParams) {
            // 合并外部参数到搜索参数中
            Object.assign(state.searchParams, externalParams);
        }
        handleSearch();
    },
    // 暴露重置方法给父组件调用
    reset: () => {
        resetSearch();
    },
    // 获取当前搜索参数
    getSearchParams: () => {
        return { ...state.searchParams };
    },
    // 设置搜索参数
    setSearchParams: (params) => {
        state.searchParams = { ...params };
    },
    refresh,
    tableData,
    selectedRows,
    add: handleAdd,
    edit: handleEdit,
    view: handleView,
    delete: handleDelete,
    loadData,
    searchForm,
    formRef,
    page
});
</script>

<style lang="scss" scoped>
.table-container {
    .search-container {
        background: #fff;
        padding: 16px;
        margin-bottom: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        .el-form {
            .search-form-wrapper {
                display: flex;
                flex-wrap: wrap;
                gap: 16px;
                align-items: flex-end;

                // 搜索字段样式
                .search-field-item {
                    flex-shrink: 0;
                    min-width: 120px;

                    .el-form-item {
                        margin-bottom: 0;
                        width: 100%;

                        // 确保所有搜索组件宽度一致
                        .search-field-width {}

                        // 统一的现代化样式
                        :deep(.el-input__wrapper) {
                            border-radius: 6px;
                            transition: all 0.3s ease;

                            &:hover {
                                border-color: #409eff;
                            }

                            &.is-focus {
                                border-color: #409eff;
                            }
                        }

                        :deep(.el-select__wrapper) {
                            border-radius: 6px;
                            transition: all 0.3s ease;

                            &:hover {
                                border-color: #409eff;
                            }

                            &.is-focus {
                                border-color: #409eff;
                                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
                            }
                        }

                        :deep(.el-date-editor) {
                            border-radius: 6px;
                            transition: all 0.3s ease;

                            &:hover {
                                border-color: #409eff;
                            }

                            &.is-focus {
                                border-color: #409eff;
                                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
                            }
                        }
                    }
                }

                // 搜索按钮区域样式
                .search-button-wrapper {
                    flex-shrink: 0;
                    margin-left: auto;

                    .el-form-item {
                        margin-bottom: 0;

                        .el-form-item__content {
                            display: flex;
                            gap: 8px;
                            flex-wrap: wrap;
                        }
                    }
                }
            }
        }

        // 搜索按钮样式已移至 element-ui.scss

        // 统一按钮样式已移至 element-ui.scss

        // 文本按钮样式
        .el-button--text {
            color: #409eff;
            font-size: 12px;
            padding: 4px 8px;

            &:hover {
                color: #66b3ff;
                text-decoration: underline;
            }
        }
    }

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding: 0 4px;

        .left-buttons,
        .right-buttons {
            display: flex;
            gap: 8px;
        }
    }

    // 表格样式
    .el-table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        :deep(.el-table__header) {
            th {
                background: #f8f8f9 !important;
                color: #515a6e !important;
                font-weight: 600;
                border: none;
            }
        }

        :deep(.el-table__body) {
            tr {
                transition: all 0.3s ease;

                &:hover {
                    background-color: #f5f7fa;
                }

                td {
                    border: none;
                    border-bottom: 1px solid #f0f0f0;
                    padding: 12px 8px !important; // 增加行间距，上下各12px
                }
            }
        }

        // 滚动条样式优化
        :deep(.el-scrollbar) {
            .el-scrollbar__wrap {
                overflow-x: auto;
                overflow-y: auto;
            }

            .el-scrollbar__bar {
                &.is-vertical {
                    .el-scrollbar__thumb {
                        background-color: rgba(144, 147, 153, 0.3);
                        border-radius: 4px;

                        &:hover {
                            background-color: rgba(144, 147, 153, 0.5);
                        }
                    }
                }

                &.is-horizontal {
                    .el-scrollbar__thumb {
                        background-color: rgba(144, 147, 153, 0.3);
                        border-radius: 4px;

                        &:hover {
                            background-color: rgba(144, 147, 153, 0.5);
                        }
                    }
                }
            }
        }

        // 表格内容区域滚动条
        :deep(.el-table__body-wrapper) {
            &::-webkit-scrollbar {
                width: 8px;
                height: 8px;
            }

            &::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(144, 147, 153, 0.3);
                border-radius: 4px;

                &:hover {
                    background: rgba(144, 147, 153, 0.5);
                }
            }
        }

        // 空状态样式
        :deep(.el-table__empty-block) {
            padding: 40px 0;

            .el-table__empty-text {
                color: #909399;
                font-size: 14px;
            }
        }

        // 表格固定列阴影优化
        :deep(.el-table__fixed) {
            &::before {
                background-color: rgba(0, 0, 0, 0.05);
            }
        }

        :deep(.el-table__fixed-right) {
            &::before {
                background-color: rgba(0, 0, 0, 0.05);
            }
        }
    }

    .pagination-container {
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
        padding: 16px 0;

        .el-pagination {
            :deep(.el-pager li) {
                border-radius: 4px;
                margin: 0 2px;
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-1px);
                }

                &.is-active {
                    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
                    border: none;
                }
            }

            :deep(.btn-prev),
            :deep(.btn-next) {
                border-radius: 4px;
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-1px);
                }
            }
        }
    }

    // 对话框样式
    :deep(.el-dialog) {
        border-radius: 12px;
        overflow: hidden;

        .el-dialog__header {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            color: white;
            padding: 20px 24px;

            .el-dialog__title {
                font-weight: 600;
                font-size: 16px;
            }

            .el-dialog__headerbtn {
                color: white;

                &:hover {
                    color: #f0f0f0;
                }
            }
        }

        .el-dialog__body {
            padding: 24px;
        }

        .el-dialog__footer {
            padding: 16px 24px;
            border-top: 1px solid #f0f0f0;
            background: #fafafa;

            .dialog-footer {
                display: flex;
                justify-content: flex-end;
                gap: 12px;

                .el-button {
                    border-radius: 6px;
                    font-weight: 500;
                    padding: 8px 20px;

                    &.el-button--primary {
                        background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
                        border: none;

                        &:hover {
                            transform: translateY(-1px);
                            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
                        }
                    }
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        .search-container {
            padding: 12px;

            .el-form {
                .search-form-wrapper {
                    flex-direction: column;
                    gap: 12px;

                    // 移动端下搜索字段全宽显示
                    .search-field-item {
                        width: 100% !important;
                        min-width: unset;
                    }

                    // 搜索按钮区域
                    .search-button-wrapper {
                        margin-left: unset;
                        width: 100%;

                        .el-form-item__content {
                            justify-content: center;
                        }
                    }
                }
            }
        }

        .table-header {
            flex-direction: column;
            gap: 12px;

            .left-buttons,
            .right-buttons {
                justify-content: center;
                width: 100%;
            }
        }

        .pagination-container {
            justify-content: center;
        }
    }
}
</style>
