<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 西宁市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>招聘信息匹配-青创通 · 西宁市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <link rel="stylesheet" type="text/css" href="../public/css/commonNew.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/talentSpecial.css?v=202507231517" />
    <link rel="stylesheet" type="text/css" href="css/jobWorkerMatch.css?v=202507231517" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- banner start -->
    <div class="bannerBox pr">
        <div class="bannerSlide">
            <img src="./images/ts_bannerBg.jpg">
        </div>
    </div>
    <!-- banner end -->
    
    <!-- main content start -->
    <div class="width100">
        <div class="conAuto1400">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="./talentSpecial.html">找人才</a>
                <span class="separator">></span>
                <span class="current">招聘信息匹配</span>
            </div>
            
            <!-- 招聘信息详情 -->
            <div class="jobDetailSection" id="jobDetailSection">
                <div class="jobDetailCard">
                    <div class="jobDetailHeader">
                        <h1 class="jobDetailTitle" id="jobDetailTitle">加载中...</h1>
                        <div class="jobDetailSalary" id="jobDetailSalary">--</div>
                    </div>
                    <div class="jobDetailInfo">
                        <div class="jobDetailItem">
                            <span class="label">工作类型：</span>
                            <span class="value" id="jobDetailType">--</span>
                        </div>
                        <div class="jobDetailItem">
                            <span class="label">工作地点：</span>
                            <span class="value" id="jobDetailLocation">--</span>
                        </div>
                        <div class="jobDetailItem">
                            <span class="label">工作类别：</span>
                            <span class="value" id="jobDetailCategory">--</span>
                        </div>
                        <div class="jobDetailItem">
                            <span class="label">发布时间：</span>
                            <span class="value" id="jobDetailPublishTime">--</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 匹配结果 -->
            <div class="matchResultSection">
                <div class="sectionHeader">
                    <h2 class="sectionTitle">匹配的零工</h2>
                    <div class="matchStats">
                        <span class="matchCount" id="matchCount">找到 0 个匹配的零工</span>
                        <button class="refreshBtn" onclick="refreshMatches()">刷新匹配</button>
                    </div>
                </div>
                
                <!-- 排序和筛选 -->
                <div class="matchFilters">
                    <div class="sortOptions">
                        <label>排序方式：</label>
                        <select id="sortBy" onchange="sortMatches()">
                            <option value="similarity">相似度</option>
                            <option value="rating">评分</option>
                            <option value="experience">经验</option>
                            <option value="completedJobs">完成工作数</option>
                        </select>
                    </div>
                    <div class="filterOptions">
                        <label>最低相似度：</label>
                        <select id="minSimilarity" onchange="filterMatches()">
                            <option value="0">不限</option>
                            <option value="50">50%以上</option>
                            <option value="70">70%以上</option>
                            <option value="80">80%以上</option>
                            <option value="90">90%以上</option>
                        </select>
                    </div>
                </div>
                
                <!-- 匹配结果列表 -->
                <div class="matchResultsList" id="matchResultsList">
                    <div class="loading" id="loadingIndicator">
                        <div class="loadingSpinner"></div>
                        <p>正在匹配零工...</p>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div class="pagination-container" id="matchPagination"></div>
            </div>
        </div>
    </div>
    
    <!-- 零工详情模态框 -->
    <div id="workerDetailModal" class="modal-overlay" style="display: none;">
        <div class="modal-content worker-detail-modal">
            <div class="modal-header">
                <h3>零工详情</h3>
                <button class="modal-close" onclick="closeWorkerDetailModal()">&times;</button>
            </div>
            <div class="modal-body" id="workerDetailContent">
                <!-- 零工详情内容将在这里动态加载 -->
            </div>
        </div>
    </div>
    
    <!-- 联系模态框 -->
    <div id="contactModal" class="modal-overlay" style="display: none;">
        <div class="modal-content contact-modal">
            <div class="modal-header">
                <h3>联系零工</h3>
                <button class="modal-close" onclick="closeContactModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="contact-info" id="contactInfo">
                    <!-- 联系信息将在这里显示 -->
                </div>
                <div class="contact-form">
                    <h4>发送消息</h4>
                    <textarea id="contactMessage" placeholder="请输入您要发送的消息..." rows="4"></textarea>
                    <div class="contact-actions">
                        <button class="btn-send" onclick="sendMessage()">发送消息</button>
                        <button class="btn-cancel" onclick="closeContactModal()">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="footerBar"></div>
    
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript"
        charset="utf-8"></script>
    <script src="../public/js/words.js" type="text/javascript" charset="utf-8"></script>
    <!--common js-->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/jobWorkerMatch.js?v=202507231900" type="text/javascript" charset="utf-8"></script>
</body>

</html>
