/**
 * 获取并合并列配置
 * @param {Object} options - 配置选项
 * @param {String} options.menuCode - 菜单代码
 * @param {Object} options.baseOption - 基础配置对象
 * @param {Object} options.proxy - 当前组件实例的proxy对象
 * @param {Function} options.callback - 获取完成后的回调函数，接收合并后的配置作为参数
 * @returns {Promise<Object>} 返回合并后的配置对象
 */
export async function getCoSyncColumn(options) {
  const { menuCode, baseOption, proxy, callback } = options;

  try {
    if(!menuCode) {
      return baseOption;
    }

    // 这里暂时直接返回基础配置，避免依赖后端API
    // 如果需要后端配置，可以取消注释下面的代码
    /*
    // 从后端获取角色权限配置
    const res = await getColumns({
      menuCode: menuCode
    });

    // 合并角色权限配置和基础配置
    let mergedConfig = baseOption;

    // 如果存在自定义合并方法，则使用自定义合并方法
    if (proxy && proxy.mergeColumn) {
      mergedConfig = proxy.mergeColumn(res.data, baseOption);
    } else {
      // 默认合并逻辑
      if (res.data && res.data.column && res.data.column.length > 0) {
        // 使用后端配置覆盖前端配置
        mergedConfig.column = res.data.column.map(serverCol => {
          // 查找前端是否有相同prop的配置
          const clientCol = baseOption.column.find(col => col.prop === serverCol.prop);
          // 合并配置，后端配置优先
          return { ...clientCol, ...serverCol };
        });
      }
    }
    */

    // 如果有回调函数，执行回调
    if (typeof callback === 'function') {
      callback(baseOption);
    }

    return baseOption;
  } catch (error) {
    console.error('获取列配置失败', error);
    return baseOption;
  }
}

/**
 * 提取完整的表格和表单配置
 * @param {Object} mergedConfig - 合并后的配置对象
 * @returns {Object} 包含表格列、搜索字段、表单字段和表单选项的完整配置对象
 */
export function extractTableColumns(mergedConfig) {
  if (!mergedConfig || !mergedConfig.column || !mergedConfig.column.length) {
    return { 
      tableColumns: [], 
      searchColumns: [], 
      formFields: [], 
      formOptions: {} 
    };
  }
  
  // 提取表格列 - 只显示未隐藏且允许在列表中显示的字段
  const tableColumns = mergedConfig.column
    .filter(col => col.showColumn !== false && col.divider != true);
    
  // 提取搜索字段 - 只使用允许搜索的字段
  const searchColumns = mergedConfig.column
    .filter(col => col.search === true || col.search === 1);

  // 提取表单字段配置
  const formFields = mergedConfig.column.map(col => ({
    ...col,
    required: col.isRequired || (col.rules && col.rules.some(rule => rule.required)) || false,
  }));

  // 提取表单选项配置 - 包含除 column 之外的所有配置项
  const formOptions = {};
  Object.keys(mergedConfig).forEach(key => {
    if (key !== 'column') {
      formOptions[key] = mergedConfig[key];
    }
  });
    
  return { 
    tableColumns, 
    searchColumns, 
    formFields, 
    formOptions 
  };
}

/**
 * 提取表单字段配置
 * @param {Object} mergedConfig - 合并后的配置对象
 * @returns {Array} 表单字段配置数组
 */
export function extractFormFields(mergedConfig) {
  if (!mergedConfig || !mergedConfig.column || !mergedConfig.column.length) {
    return [];
  }
  
  // 转换为表单字段配置
  return mergedConfig.column.map(col => ({
    ...col,
    required: col.isRequired || (col.rules && col.rules.some(rule => rule.required)) || false,
  }));
}
