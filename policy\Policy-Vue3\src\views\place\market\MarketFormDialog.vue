<template>
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="800px" append-to-body @close="handleClose">
        <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="市场名称" prop="marketName">
                        <el-input v-model="formData.marketName" placeholder="请输入市场名称" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="市场编码" prop="marketCode">
                        <el-input v-model="formData.marketCode" placeholder="请输入市场编码" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="市场类型" prop="marketType">
                        <el-select v-model="formData.marketType" placeholder="请选择市场类型" style="width: 100%">
                            <el-option label="综合市场" value="综合市场" />
                            <el-option label="专业市场" value="专业市场" />
                            <el-option label="临时市场" value="临时市场" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="营业时间" prop="operatingHours">
                        <el-input v-model="formData.operatingHours" placeholder="请输入营业时间" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="市场地址" prop="address">
                <el-input v-model="formData.address" placeholder="请输入市场地址" />
            </el-form-item>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="区域代码" prop="regionCode">
                        <el-input v-model="formData.regionCode" placeholder="请输入区域代码" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="区域名称" prop="regionName">
                        <el-input v-model="formData.regionName" placeholder="请输入区域名称" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="联系人" prop="contactPerson">
                        <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="联系电话" prop="contactPhone">
                        <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="联系邮箱" prop="contactEmail">
                <el-input v-model="formData.contactEmail" placeholder="请输入联系邮箱" />
            </el-form-item>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="零工容纳量" prop="workerCapacity">
                        <el-input-number v-model="formData.workerCapacity" :min="0" style="width: 100%" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="当前零工数" prop="currentWorkerCount">
                        <el-input-number v-model="formData.currentWorkerCount" :min="0" style="width: 100%" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="日均需求" prop="dailyAvgDemand">
                        <el-input-number v-model="formData.dailyAvgDemand" :min="0" style="width: 100%" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="管理费用" prop="managementFee">
                        <el-input v-model="formData.managementFee" placeholder="元/人/天" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="服务费率" prop="serviceFeeRate">
                        <el-input v-model="formData.serviceFeeRate" placeholder="%" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="用工高峰时段" prop="peakDemandTime">
                <el-input v-model="formData.peakDemandTime" placeholder="请输入用工高峰时段" />
            </el-form-item>
            <el-form-item label="安全措施" prop="safetyMeasures">
                <el-input v-model="formData.safetyMeasures" type="textarea" :rows="3" placeholder="请输入安全措施描述" />
            </el-form-item>
            <el-form-item label="市场描述" prop="description">
                <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入市场详细描述" />
            </el-form-item>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="是否推荐" prop="isFeatured">
                        <el-radio-group v-model="formData.isFeatured">
                            <el-radio :label="1">是</el-radio>
                            <el-radio :label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="formData.status">
                            <el-radio label="0">正常</el-radio>
                            <el-radio label="1">停用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="备注" prop="remark">
                <el-input v-model="formData.remark" type="textarea" :rows="2" placeholder="请输入备注" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleCancel">取 消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup name="MarketFormDialog">
import { ref, reactive, getCurrentInstance } from 'vue'
import { getLaborMarketInfo, addLaborMarketInfo, updateLaborMarketInfo } from "@/api/place/market"

const { proxy } = getCurrentInstance()

// 组件引用
const formRef = ref(null)

// 响应式数据
const dialogVisible = ref(false)
const dialogTitle = ref('')
const submitLoading = ref(false)
const formMode = ref('add') // add | edit

// 表单数据
const formData = reactive({
    marketId: null,
    marketName: '',
    marketCode: '',
    marketType: '',
    address: '',
    regionCode: '',
    regionName: '',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    operatingHours: '',
    workerCapacity: 0,
    currentWorkerCount: 0,
    dailyAvgDemand: 0,
    peakDemandTime: '',
    managementFee: '',
    serviceFeeRate: '',
    safetyMeasures: '',
    description: '',
    status: '0',
    isFeatured: 0,
    remark: ''
})

// 表单验证规则
const formRules = {
    marketName: [
        { required: true, message: '市场名称不能为空', trigger: 'blur' }
    ],
    marketType: [
        { required: true, message: '市场类型不能为空', trigger: 'change' }
    ],
    address: [
        { required: true, message: '市场地址不能为空', trigger: 'blur' }
    ]
}

// 发射事件
const emit = defineEmits(['submit', 'cancel'])

// 重置表单
function resetForm() {
    Object.assign(formData, {
        marketId: null,
        marketName: '',
        marketCode: '',
        marketType: '',
        address: '',
        regionCode: '',
        regionName: '',
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
        operatingHours: '',
        workerCapacity: 0,
        currentWorkerCount: 0,
        dailyAvgDemand: 0,
        peakDemandTime: '',
        managementFee: '',
        serviceFeeRate: '',
        safetyMeasures: '',
        description: '',
        status: '0',
        isFeatured: 0,
        remark: ''
    })
    formRef.value?.resetFields()
}

// 打开对话框
function openDialog(mode, marketId = null) {
    formMode.value = mode
    dialogTitle.value = mode === 'add' ? '新增市场信息' : '修改市场信息'
    
    resetForm()
    
    if (mode === 'edit' && marketId) {
        getLaborMarketInfo(marketId).then(response => {
            Object.assign(formData, response.data)
        }).catch(() => {
            proxy.$modal.msgError('获取市场信息失败')
        })
    }
    
    dialogVisible.value = true
}

// 关闭对话框
function handleClose() {
    dialogVisible.value = false
    resetForm()
}

// 取消
function handleCancel() {
    handleClose()
    emit('cancel')
}

// 提交
function handleSubmit() {
    formRef.value?.validate((valid) => {
        if (valid) {
            submitLoading.value = true
            
            const submitData = { ...formData }
            const apiCall = formMode.value === 'add' ? addLaborMarketInfo(submitData) : updateLaborMarketInfo(submitData)
            
            apiCall.then(() => {
                proxy.$modal.msgSuccess(formMode.value === 'add' ? '新增成功' : '修改成功')
                handleClose()
                emit('submit')
            }).catch(() => {
                proxy.$modal.msgError(formMode.value === 'add' ? '新增失败' : '修改失败')
            }).finally(() => {
                submitLoading.value = false
            })
        }
    })
}

// 暴露方法
defineExpose({
    openDialog
})
</script>

<style scoped>
.dialog-footer {
    text-align: right;
}
</style>
