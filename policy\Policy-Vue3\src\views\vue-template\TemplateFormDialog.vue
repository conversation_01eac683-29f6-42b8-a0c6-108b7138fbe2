<template>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="formOption.dialogWidth" destroy-on-close
        :close-on-click-modal="false" :fullscreen="isFullscreen" @closed="handleDialogClosed" @open="handleDialogOpened"
        class="custom-dialog">
        <div class="dialog-content" :class="{ 'view-mode': dialogType === 'view' }" :style="dialogContentStyle">
            <FormList ref="formListRef" v-model="formData" :fields="currentFormFields" :is-view="dialogType === 'view'"
                :showActions="false" :labelWidth="formOption.labelWidth" :inline="false"
                @field-change="handleFieldChange" v-if="dialogType !== 'view'">

                <!-- 富文本编辑器插槽示例 -->
                <template #description="{ field, value }">
                    <el-input type="textarea" v-model="formData.description"
                        :placeholder="field.placeholder || '请输入描述信息'" :rows="field.rows || 4"
                        :maxlength="field.maxlength || 500" :show-word-limit="field.showWordLimit || true"
                        style="width: 100%; max-width: 100%; box-sizing: border-box;">
                    </el-input>
                </template>

                <!-- 价格输入插槽 -->
                <template #price="{ field, value }">
                    <el-input-number v-model="formData.price" :placeholder="field.placeholder || '请输入价格'"
                        :precision="field.precision || 2" :min="field.min || 0" :max="field.max || 999999.99"
                        :step="field.step || 0.01" controls-position="right"
                        style="width: 100%; max-width: 100%; box-sizing: border-box;">
                        <template #prepend>
                            <span style="padding: 0 8px;">¥</span>
                        </template>
                    </el-input-number>
                </template>
            </FormList>

            <!-- 查看模式使用ViewList组件 -->
            <ViewList v-else v-model="formData" :fields="currentFormFields"
                :labelWidth="formOption.labelWidth || '120px'">
            </ViewList>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button class="common-btn" @click="toggleFullscreen">
                    {{ isFullscreen ? '退 出 全 屏' : '全 屏 显 示' }}
                </el-button>
                <el-button class="common-btn" @click="handleCancel">
                    {{ dialogType === 'view' ? '关 闭' : '取 消' }}
                </el-button>
                <el-button v-if="dialogType !== 'view'" type="primary" class="common-btn"
                    @click="handleSubmitForm" :loading="submitLoading" :disabled="submitDisabled">
                    确 认
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup name="TemplateFormDialog">
import { ref, reactive, computed, getCurrentInstance } from 'vue';
import FormList from '@/components/FormList/index.vue';
import ViewList from '@/components/ViewList/index.vue';

const { proxy } = getCurrentInstance();

// Props
const props = defineProps({
    formFields: {
        type: Array,
        default: () => []
    },
    formOption: {
        type: Object,
        default: () => ({
            dialogWidth: '800px',
            dialogHeight: '70vh'
        })
    }
});

// Emits
const emit = defineEmits([
    'submit',
    'cancel'
]);

// 表单相关
const dialogVisible = ref(false);
const dialogType = ref('add'); // add, edit, view
const dialogTitle = ref('新增模板');
const formListRef = ref(null);
const formData = ref({});
const submitLoading = ref(false);
const submitDisabled = ref(false);

// 控制弹窗全屏状态
const isFullscreen = ref(false);

// 根据表单类型动态筛选字段
const currentFormFields = computed(() => {
    if (!props.formFields.length) return [];

    if (dialogType.value === 'add') {
        return props.formFields.filter(field => field.addDisplay !== false);
    }
    else if (dialogType.value === 'edit') {
        return props.formFields.filter(field => field.editDisplay !== false);
    }
    else { // view模式
        return props.formFields.filter(field => field.viewDisplay !== false);
    }
});

// 弹窗内容样式计算
const dialogContentStyle = computed(() => {
    // 弹窗内容区域自适应高度，不设置固定高度
    const baseStyle = {
        overflow: 'visible', // 允许内容自然展示
        padding: '20px 10px', // 适当的内边距
        overflowX: 'hidden', // 隐藏横向滚动条
    };

    if (isFullscreen.value) {
        return {
            ...baseStyle,
            maxHeight: 'calc(100vh - 180px)', // 全屏模式下设置最大高度
            overflowY: 'auto', // 超出时滚动
            overflowX: 'hidden', // 隐藏横向滚动条
        };
    }

    // 非全屏模式：自适应高度，设置最大高度而不是固定高度
    return {
        ...baseStyle,
        maxHeight: props.formOption.dialogHeight || '70vh', // 只设置最大高度
        overflowY: 'auto', // 内容超出时显示滚动条
        overflowX: 'hidden', // 隐藏横向滚动条
        minHeight: 'auto', // 允许自适应最小高度
    };
});

// 切换全屏状态
const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
};

// 对外暴露的方法
const openDialog = (type, title, data = {}) => {
    dialogType.value = type;
    dialogTitle.value = title;

    // 重置表单数据，确保有完整的初始值
    formData.value = {
        id: data.id || undefined,
        name: data.name || '',
        code: data.code || '',
        category: data.category || 'tech',
        type: data.type || '1',
        description: data.description || '',
        price: data.price || 0,
        quantity: data.quantity || 0,
        effectiveDate: data.effectiveDate || '',
        isRecommend: data.isRecommend || '0',
        tags: data.tags || [],
        status: data.status || '0',
        createBy: data.createBy || '',
        createTime: data.createTime || '',
        updateTime: data.updateTime || '',
        ...data
    };

    dialogVisible.value = true;
};

const closeDialog = () => {
    dialogVisible.value = false;
};

// 处理表单字段变更
const handleFieldChange = (field, value) => {
};

// 处理表单提交
const handleSubmitForm = async () => {
    // 防止重复提交
    if (submitLoading.value || submitDisabled.value) {
        return;
    }

    // 确保formListRef存在
    if (!formListRef.value) {
        return;
    }

    try {
        // 设置按钮状态
        submitLoading.value = true;
        submitDisabled.value = true;

        // 表单验证
        await formListRef.value.validate();

        // 发送提交事件给父组件
        emit('submit', {
            type: dialogType.value,
            data: formData.value
        });

    } catch (error) {
        // 验证失败不显示错误消息，因为表单会自动显示错误
        submitLoading.value = false;
        submitDisabled.value = false;
    }
};

// 处理取消
const handleCancel = () => {
    emit('cancel');
    closeDialog();
};

// 处理表单对话框打开
const handleDialogOpened = () => {
    // 设置表单状态
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 处理表单对话框关闭
const handleDialogClosed = () => {
    // 清空表单数据
    formData.value = {};

    // 重置提交状态
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 提交成功后的回调
const onSubmitSuccess = () => {
    submitLoading.value = false;
    submitDisabled.value = false;
    closeDialog();
};

// 提交失败后的回调
const onSubmitError = () => {
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 暴露方法给父组件
defineExpose({
    openDialog,
    closeDialog,
    onSubmitSuccess,
    onSubmitError
});
</script>

<style lang="scss" scoped>
// 公用按钮样式已移至 element-ui.scss

// 弹窗内容区域样式优化
.dialog-content {
    // 移除固定高度设置，使用动态计算的样式
    max-height: v-bind("dialogContentStyle.maxHeight");
    min-height: v-bind("dialogContentStyle.minHeight || 'auto'");
    overflow-y: v-bind("dialogContentStyle.overflowY");
    overflow-x: hidden !important; // 强制隐藏横向滚动条
    padding: v-bind("dialogContentStyle.padding");
    width: 100%;
    box-sizing: border-box;

    // 内容区域自适应
    &:not(.view-mode) {

        // 表单模式下的优化
        :deep(.el-form) {
            width: 100%;

            .el-form-item {
                margin-bottom: 18px;
                width: 100%;
                box-sizing: border-box;

                // 统一控件高度
                .el-input,
                .el-select,
                .el-date-editor,
                .el-cascader,
                .el-input-number {
                    width: 100%;
                    max-width: 100%;
                    box-sizing: border-box;

                    .el-input__wrapper,
                    .el-select__wrapper {
                        border-radius: 6px;
                        width: 100%;
                        box-sizing: border-box;
                    }
                }

                .el-textarea {
                    width: 100%;
                    max-width: 100%;
                    box-sizing: border-box;

                    .el-textarea__inner {
                        border-radius: 6px;
                        min-height: 80px;
                        width: 100%;
                        box-sizing: border-box;
                        resize: vertical; // 只允许垂直调整大小
                    }
                }

                // 控件组合的样式处理
                .el-input-group {
                    width: 100%;
                    max-width: 100%;
                    box-sizing: border-box;

                    .el-input-group__append,
                    .el-input-group__prepend {
                        white-space: nowrap;
                    }
                }

                // 数字输入框样式
                .el-input-number {
                    width: 100%;

                }

                // 复选框和单选框样式
                .el-checkbox-group,
                .el-radio-group {
                    width: 100%;

                    .el-checkbox,
                    .el-radio {
                        margin-right: 16px;
                        margin-bottom: 8px;
                    }
                }
            }
        }
    }

    &.view-mode {
        padding: 16px 10px;
        width: 100%;
        box-sizing: border-box;
        overflow-x: hidden !important; // 防止查看模式出现横向滚动条

        :deep(.view-list) {
            width: 100%;
            box-sizing: border-box;

            .el-descriptions-item__label {
                color: #606266;
                font-weight: 500;
                word-wrap: break-word; // 长文本自动换行
                word-break: break-all;
            }

            .el-descriptions-item__content {
                color: #303133;
                word-wrap: break-word; // 长文本自动换行
                word-break: break-all;
                max-width: 100%;
                overflow-wrap: break-word;
            }
        }
    }
}

// 弹窗底部样式优化
:deep(.el-dialog__footer) {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f2f5;
    background: linear-gradient(180deg, #fafbfc 0%, #f8f9fa 100%);
    border-radius: 0 0 12px 12px;

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 12px;
    }
}

// 弹窗主体样式优化
:deep(.el-dialog) {
    border-radius: 12px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden; // 防止弹窗整体出现滚动条

    .el-dialog__header {
        padding: 20px 24px 16px;
        border-bottom: 1px solid #f0f2f5;
        background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
        border-radius: 12px 12px 0 0;
        overflow: hidden; // 防止头部出现滚动条

        .el-dialog__title {
            color: #303133;
            font-weight: 600;
            font-size: 16px;
            white-space: nowrap; // 防止标题换行
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .el-dialog__body {
        padding: 0;
        background: #ffffff;
        overflow-x: hidden !important; // 强制隐藏横向滚动条
        width: 100%;
        box-sizing: border-box;
    }
}
</style>