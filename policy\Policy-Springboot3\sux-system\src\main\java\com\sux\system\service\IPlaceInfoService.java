package com.sux.system.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sux.system.domain.PlaceInfo;

/**
 * 场地信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IPlaceInfoService extends IService<PlaceInfo>
{
    /**
     * 查询场地信息列表
     * 
     * @param placeInfo 场地信息
     * @return 场地信息集合
     */
    public List<PlaceInfo> selectPlaceInfoList(PlaceInfo placeInfo);

    /**
     * 查询场地信息
     * 
     * @param placeId 场地信息主键
     * @return 场地信息
     */
    public PlaceInfo selectPlaceInfoByPlaceId(Long placeId);

    /**
     * 新增场地信息
     * 
     * @param placeInfo 场地信息
     * @return 结果
     */
    public int insertPlaceInfo(PlaceInfo placeInfo);

    /**
     * 修改场地信息
     * 
     * @param placeInfo 场地信息
     * @return 结果
     */
    public int updatePlaceInfo(PlaceInfo placeInfo);

    /**
     * 批量删除场地信息
     * 
     * @param placeIds 需要删除的场地信息主键集合
     * @return 结果
     */
    public int deletePlaceInfoByPlaceIds(Long[] placeIds);

    /**
     * 删除场地信息信息
     * 
     * @param placeId 场地信息主键
     * @return 结果
     */
    public int deletePlaceInfoByPlaceId(Long placeId);

    /**
     * 查询推荐场地信息列表
     * 
     * @param placeInfo 场地信息
     * @return 场地信息集合
     */
    public List<PlaceInfo> selectFeaturedPlaceInfoList(PlaceInfo placeInfo);

    /**
     * 查询活跃场地信息列表
     * 
     * @param placeInfo 场地信息
     * @return 场地信息集合
     */
    public List<PlaceInfo> selectActivePlaceInfoList(PlaceInfo placeInfo);

    /**
     * 根据场地类型查询场地信息列表
     * 
     * @param placeType 场地类型
     * @return 场地信息集合
     */
    public List<PlaceInfo> selectPlaceInfoByType(String placeType);

    /**
     * 根据区域代码查询场地信息列表
     * 
     * @param regionCode 区域代码
     * @return 场地信息集合
     */
    public List<PlaceInfo> selectPlaceInfoByRegion(String regionCode);

    /**
     * 根据场地等级查询场地信息列表
     * 
     * @param placeLevel 场地等级
     * @return 场地信息集合
     */
    public List<PlaceInfo> selectPlaceInfoByLevel(String placeLevel);

    /**
     * 根据行业方向查询场地信息列表
     * 
     * @param industryDirection 行业方向
     * @return 场地信息集合
     */
    public List<PlaceInfo> selectPlaceInfoByIndustry(String industryDirection);

    /**
     * 根据运营模式查询场地信息列表
     * 
     * @param operationMode 运营模式
     * @return 场地信息集合
     */
    public List<PlaceInfo> selectPlaceInfoByOperationMode(String operationMode);

    /**
     * 查询场地统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> selectPlaceInfoStatistics();

    /**
     * 根据关键词搜索场地信息
     * 
     * @param keyword 关键词
     * @return 场地信息集合
     */
    public List<PlaceInfo> selectPlaceInfoByKeyword(String keyword);

    /**
     * 更新场地浏览次数
     * 
     * @param placeId 场地ID
     * @return 结果
     */
    public int updatePlaceInfoViewCount(Long placeId);

    /**
     * 查询即将到期的招商场地
     * 
     * @param days 天数
     * @return 场地信息集合
     */
    public List<PlaceInfo> selectPlaceInfoExpiringSoon(Integer days);

    /**
     * 查询场地详细信息（包含关联信息）
     * 
     * @param placeId 场地ID
     * @return 场地信息
     */
    public PlaceInfo selectPlaceInfoDetailByPlaceId(Long placeId);

    /**
     * 获取所有场地类型列表
     * 
     * @return 场地类型列表
     */
    public List<String> selectAllPlaceTypes();

    /**
     * 获取所有场地等级列表
     * 
     * @return 场地等级列表
     */
    public List<String> selectAllPlaceLevels();

    /**
     * 获取所有区域列表
     * 
     * @return 区域列表
     */
    public List<Map<String, String>> selectAllRegions();

    /**
     * 获取所有行业方向列表
     * 
     * @return 行业方向列表
     */
    public List<String> selectAllIndustryDirections();

    /**
     * 获取所有运营模式列表
     * 
     * @return 运营模式列表
     */
    public List<String> selectAllOperationModes();
}
