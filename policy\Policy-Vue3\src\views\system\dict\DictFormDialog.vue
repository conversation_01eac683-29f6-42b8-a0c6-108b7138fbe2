<template>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="formOption.dialogWidth" destroy-on-close
        :close-on-click-modal="false" :fullscreen="isFullscreen" @closed="handleDialogClosed" @open="handleDialogOpened"
        class="custom-dialog">
        <div class="dialog-content" :class="{ 'view-mode': dialogType === 'view' }" :style="dialogContentStyle">
            <FormList ref="formListRef" v-model="formData" :fields="currentFormFields" :is-view="dialogType === 'view'"
                :showActions="false" :labelWidth="formOption.labelWidth" :inline="false"
                @field-change="handleFieldChange" v-if="dialogType !== 'view'">
            </FormList>

            <!-- 查看模式使用ViewList组件 -->
            <ViewList v-else v-model="formData" :fields="currentFormFields" :labelWidth="formOption.labelWidth">
            </ViewList>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button class="custom-btn" @click="toggleFullscreen">
                    {{ isFullscreen ? '退出全屏' : '全屏显示' }}
                </el-button>
                <el-button class="custom-btn" @click="handleCancel">
                    {{ dialogType === 'view' ? '关闭' : '取消' }}
                </el-button>
                <el-button v-if="dialogType !== 'view'" type="primary" class="custom-btn" @click="handleSubmitForm"
                    :loading="submitLoading" :disabled="submitDisabled">
                    确 认
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup name="DictFormDialog">
import { ref, reactive, computed, getCurrentInstance } from 'vue';
import FormList from '@/components/FormList/index.vue';
import ViewList from '@/components/ViewList/index.vue';

const { proxy } = getCurrentInstance();

// Props
const props = defineProps({
    formFields: {
        type: Array,
        default: () => []
    },
    formOption: {
        type: Object,
        default: () => ({
            dialogWidth: '600px',
            dialogHeight: '60vh',
            labelWidth: '100px'
        })
    }
});

// Emits
const emit = defineEmits([
    'submit',
    'cancel'
]);

// 表单相关
const dialogVisible = ref(false);
const dialogType = ref('add'); // add, edit, view
const dialogTitle = ref('新增字典');
const formListRef = ref(null);
const formData = ref({});
const submitLoading = ref(false);
const submitDisabled = ref(false);

// 控制弹窗全屏状态
const isFullscreen = ref(false);

// 根据表单类型动态筛选字段
const currentFormFields = computed(() => {
    if (!props.formFields.length) return [];

    if (dialogType.value === 'add') {
        return props.formFields.filter(field => field.addDisplay !== false);
    }
    else if (dialogType.value === 'edit') {
        return props.formFields.filter(field => field.editDisplay !== false);
    }
    else { // view模式
        return props.formFields.filter(field => field.viewDisplay !== false);
    }
});

// 弹窗内容样式计算
const dialogContentStyle = computed(() => {
    if (isFullscreen.value) {
        return {
            maxHeight: 'calc(100vh - 150px)',
            height: 'auto'
        };
    }
    return {
        maxHeight: props.formOption.dialogHeight || '60vh',
        height: 'auto'
    };
});

// 切换全屏状态
const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
};

// 对外暴露的方法
const openDialog = (type, title, data = {}) => {
    dialogType.value = type;
    dialogTitle.value = title;
    formData.value = { ...data };

    dialogVisible.value = true;
};

const closeDialog = () => {
    dialogVisible.value = false;
};

// 处理表单字段变更
const handleFieldChange = (field, value) => {
    // 可以在这里添加字段联动逻辑
};

// 处理表单提交
const handleSubmitForm = async () => {
    // 防止重复提交
    if (submitLoading.value || submitDisabled.value) {
        return;
    }

    // 确保formListRef存在
    if (!formListRef.value) {
        return;
    }

    try {
        // 设置按钮状态
        submitLoading.value = true;
        submitDisabled.value = true;

        // 表单验证
        await formListRef.value.validate();

        // 发送提交事件给父组件
        emit('submit', {
            type: dialogType.value,
            data: formData.value
        });

    } catch (error) {
        // 验证失败不显示错误消息，因为表单会自动显示错误
        submitLoading.value = false;
        submitDisabled.value = false;
    }
};

// 处理取消
const handleCancel = () => {
    emit('cancel');
    closeDialog();
};

// 处理表单对话框打开
const handleDialogOpened = () => {
    // 设置表单状态
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 处理表单对话框关闭
const handleDialogClosed = () => {
    // 清空表单数据
    formData.value = {};

    // 重置提交状态
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 提交成功后的回调
const onSubmitSuccess = () => {
    submitLoading.value = false;
    submitDisabled.value = false;
    closeDialog();
};

// 提交失败后的回调
const onSubmitError = () => {
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 暴露给父组件的方法
defineExpose({
    openDialog,
    closeDialog,
    onSubmitSuccess,
    onSubmitError
});
</script>

<style lang="scss" scoped>
// 使用公用的按钮样式，无需自定义样式

.dialog-content {
    max-height: v-bind("dialogContentStyle.maxHeight");
    overflow-y: auto;
    overflow-x: hidden;

    &.view-mode {
        padding: 10px;
    }
}
</style>