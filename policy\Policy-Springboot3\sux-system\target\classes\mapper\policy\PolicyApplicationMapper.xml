<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.PolicyApplicationMapper">
    
    <resultMap type="PolicyApplication" id="PolicyApplicationResult">
        <result property="applicationId"        column="application_id"        />
        <result property="policyId"             column="policy_id"             />
        <result property="applicantUserId"      column="applicant_user_id"     />
        <result property="applicantName"        column="applicant_name"        />
        <result property="applicantPhone"       column="applicant_phone"       />
        <result property="applicationStatus"    column="application_status"    />
        <result property="requiredMaterials"    column="required_materials"    />
        <result property="submitTime"           column="submit_time"           />
        <result property="completeTime"         column="complete_time"         />
        <result property="delFlag"              column="del_flag"              />
        <result property="createId"             column="create_id"             />
        <result property="createTime"           column="create_time"           />
        <result property="updateId"             column="update_id"             />
        <result property="updateTime"           column="update_time"           />
        <result property="remark"               column="remark"                />
        <result property="policyName"           column="policy_name"           />
        <result property="policyType"           column="policy_type"           />
        <result property="applicantUserName"    column="applicant_user_name"   />
        <result property="applicantNickName"    column="applicant_nick_name"   />
    </resultMap>

    <sql id="selectPolicyApplicationVo">
        select pa.application_id, pa.policy_id, pa.applicant_user_id, pa.applicant_name, pa.applicant_phone,
               pa.application_status, pa.required_materials,
               pa.company_name, pa.company_code, pa.company_legal_person, pa.company_address,
               pa.company_contact_person, pa.company_contact_phone,
               pa.bank_name, pa.bank_account_name, pa.bank_account_number,
               pa.submit_time, pa.complete_time, pa.del_flag,
               pa.create_id, pa.create_time, pa.update_id, pa.update_time, pa.remark,
               pi.policy_name, pi.policy_type,
               su.user_name as applicant_user_name, su.nick_name as applicant_nick_name
        from policy_application pa
        left join policy_info pi on pa.policy_id = pi.policy_id
        left join sys_user su on pa.applicant_user_id = su.user_id
    </sql>

    <select id="selectPolicyApplicationList" parameterType="PolicyApplication" resultMap="PolicyApplicationResult">
        <include refid="selectPolicyApplicationVo"/>
        <where>  
            pa.del_flag = '0'
            <if test="policyId != null "> and pa.policy_id = #{policyId}</if>
            <if test="applicantUserId != null "> and pa.applicant_user_id = #{applicantUserId}</if>
            <if test="applicationStatus != null  and applicationStatus != ''"> and pa.application_status = #{applicationStatus}</if>
            <if test="policyName != null  and policyName != ''"> and pi.policy_name like concat('%', #{policyName}, '%')</if>
            <if test="policyType != null  and policyType != ''"> and pi.policy_type = #{policyType}</if>
            <if test="applicantUserName != null  and applicantUserName != ''"> and su.user_name like concat('%', #{applicantUserName}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND pa.submit_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND pa.submit_time &lt;= #{params.endTime}
            </if>
        </where>
        order by pa.application_id desc
    </select>
    
    <select id="selectPolicyApplicationByApplicationId" parameterType="Long" resultMap="PolicyApplicationResult">
        <include refid="selectPolicyApplicationVo"/>
        where pa.application_id = #{applicationId} and pa.del_flag = '0'
    </select>

    <select id="selectPendingFirstReviewList" parameterType="PolicyApplication" resultMap="PolicyApplicationResult">
        <include refid="selectPolicyApplicationVo"/>
        <where>  
            pa.del_flag = '0' and pa.application_status = '0'
            <if test="policyName != null  and policyName != ''"> and pi.policy_name like concat('%', #{policyName}, '%')</if>
            <if test="policyType != null  and policyType != ''"> and pi.policy_type = #{policyType}</if>
            <if test="applicantUserName != null  and applicantUserName != ''"> and su.user_name like concat('%', #{applicantUserName}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND pa.submit_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND pa.submit_time &lt;= #{params.endTime}
            </if>
        </where>
        order by pa.submit_time asc
    </select>

    <select id="selectPendingFinalReviewList" parameterType="PolicyApplication" resultMap="PolicyApplicationResult">
        <include refid="selectPolicyApplicationVo"/>
        <where>  
            pa.del_flag = '0' and pa.application_status = '3'
            <if test="policyName != null  and policyName != ''"> and pi.policy_name like concat('%', #{policyName}, '%')</if>
            <if test="policyType != null  and policyType != ''"> and pi.policy_type = #{policyType}</if>
            <if test="applicantUserName != null  and applicantUserName != ''"> and su.user_name like concat('%', #{applicantUserName}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND pa.submit_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND pa.submit_time &lt;= #{params.endTime}
            </if>
        </where>
        order by pa.submit_time asc
    </select>

    <select id="selectMyApplicationsList" resultMap="PolicyApplicationResult">
        <include refid="selectPolicyApplicationVo"/>
        <where>  
            pa.del_flag = '0' and pa.applicant_user_id = #{userId}
            <if test="policyApplication.policyName != null  and policyApplication.policyName != ''"> and pi.policy_name like concat('%', #{policyApplication.policyName}, '%')</if>
            <if test="policyApplication.policyType != null  and policyApplication.policyType != ''"> and pi.policy_type = #{policyApplication.policyType}</if>
            <if test="policyApplication.applicationStatus != null  and policyApplication.applicationStatus != ''"> and pa.application_status = #{policyApplication.applicationStatus}</if>
            <if test="policyApplication.params.beginTime != null and policyApplication.params.beginTime != ''">
                AND pa.submit_time &gt;= #{policyApplication.params.beginTime}
            </if>
            <if test="policyApplication.params.endTime != null and policyApplication.params.endTime != ''">
                AND pa.submit_time &lt;= #{policyApplication.params.endTime}
            </if>
        </where>
        order by pa.application_id desc
    </select>

    <select id="selectAllApplicationsList" parameterType="PolicyApplication" resultMap="PolicyApplicationResult">
        <include refid="selectPolicyApplicationVo"/>
        <where>  
            pa.del_flag = '0'
            <if test="policyName != null  and policyName != ''"> and pi.policy_name like concat('%', #{policyName}, '%')</if>
            <if test="policyType != null  and policyType != ''"> and pi.policy_type = #{policyType}</if>
            <if test="applicantUserName != null  and applicantUserName != ''"> and su.user_name like concat('%', #{applicantUserName}, '%')</if>
            <if test="applicationStatus != null  and applicationStatus != ''"> and pa.application_status = #{applicationStatus}</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND pa.submit_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND pa.submit_time &lt;= #{params.endTime}
            </if>
        </where>
        order by pa.application_id desc
    </select>
        
    <insert id="insertPolicyApplication" parameterType="PolicyApplication" useGeneratedKeys="true" keyProperty="applicationId">
        insert into policy_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="policyId != null">policy_id,</if>
            <if test="applicantUserId != null">applicant_user_id,</if>
            <if test="applicantName != null">applicant_name,</if>
            <if test="applicantPhone != null">applicant_phone,</if>
            <if test="applicationStatus != null">application_status,</if>
            <if test="requiredMaterials != null">required_materials,</if>
            <if test="companyName != null">company_name,</if>
            <if test="companyCode != null">company_code,</if>
            <if test="companyLegalPerson != null">company_legal_person,</if>
            <if test="companyAddress != null">company_address,</if>
            <if test="companyContactPerson != null">company_contact_person,</if>
            <if test="companyContactPhone != null">company_contact_phone,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="bankAccountName != null">bank_account_name,</if>
            <if test="bankAccountNumber != null">bank_account_number,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="policyId != null">#{policyId},</if>
            <if test="applicantUserId != null">#{applicantUserId},</if>
            <if test="applicantName != null">#{applicantName},</if>
            <if test="applicantPhone != null">#{applicantPhone},</if>
            <if test="applicationStatus != null">#{applicationStatus},</if>
            <if test="requiredMaterials != null">#{requiredMaterials},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyLegalPerson != null">#{companyLegalPerson},</if>
            <if test="companyAddress != null">#{companyAddress},</if>
            <if test="companyContactPerson != null">#{companyContactPerson},</if>
            <if test="companyContactPhone != null">#{companyContactPhone},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="bankAccountName != null">#{bankAccountName},</if>
            <if test="bankAccountNumber != null">#{bankAccountNumber},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="completeTime != null">#{completeTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePolicyApplication" parameterType="PolicyApplication">
        update policy_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="policyId != null">policy_id = #{policyId},</if>
            <if test="applicantUserId != null">applicant_user_id = #{applicantUserId},</if>
            <if test="applicantName != null">applicant_name = #{applicantName},</if>
            <if test="applicantPhone != null">applicant_phone = #{applicantPhone},</if>
            <if test="applicationStatus != null">application_status = #{applicationStatus},</if>
            <if test="requiredMaterials != null">required_materials = #{requiredMaterials},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
            <if test="companyLegalPerson != null">company_legal_person = #{companyLegalPerson},</if>
            <if test="companyAddress != null">company_address = #{companyAddress},</if>
            <if test="companyContactPerson != null">company_contact_person = #{companyContactPerson},</if>
            <if test="companyContactPhone != null">company_contact_phone = #{companyContactPhone},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="bankAccountName != null">bank_account_name = #{bankAccountName},</if>
            <if test="bankAccountNumber != null">bank_account_number = #{bankAccountNumber},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <update id="updateApplicationStatus">
        update policy_application set application_status = #{status} where application_id = #{applicationId}
    </update>

    <update id="completeApplication">
        update policy_application set complete_time = now() where application_id = #{applicationId}
    </update>

    <delete id="deletePolicyApplicationByApplicationId" parameterType="Long">
        update policy_application set del_flag = '2' where application_id = #{applicationId}
    </delete>

    <delete id="deletePolicyApplicationByApplicationIds" parameterType="String">
        update policy_application set del_flag = '2' where application_id in
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>
</mapper>
