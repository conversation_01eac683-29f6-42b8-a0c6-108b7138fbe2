<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>企业就业服务 - 预览页面</title>
    <link rel="stylesheet" type="text/css" href="./css/zh.min.css" />
    <link rel="stylesheet" type="text/css" href="./css/common.css" />
    <link rel="stylesheet" type="text/css" href="css/policySpecial.css?v=202506201048" />
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .preview-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        .header-preview {
            background: linear-gradient(135deg, #0052d9 0%, #13b0be 100%);
            padding: 20px 0;
            text-align: center;
        }
        .content-preview {
            padding: 40px 20px;
            text-align: center;
        }
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 10px;
            background: #fafafa;
        }
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <!-- 头部预览 -->
        <div class="header-preview">
            <div class="conAuto1400">
                <a class="logoBox fl">
                    <h1 class="enterprise-service-title">企业就业服务</h1>
                </a>
                <div style="clear: both;"></div>
            </div>
        </div>

        <!-- 内容预览 -->
        <div class="content-preview">
            <div class="demo-section">
                <div class="demo-title">标题样式预览</div>
                <p>上方显示的"企业就业服务"标题已经应用了简洁的样式设计，包括：</p>
                <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                    <li>清晰的字体尺寸（32px）</li>
                    <li>简洁的蓝色主题色彩</li>
                    <li>悬浮时的颜色变化效果</li>
                    <li>简约的设计风格</li>
                    <li>响应式设计支持</li>
                </ul>
            </div>

            <div class="demo-section">
                <div class="demo-title">底部内容预览</div>
                <p>页面底部已添加完整的政府网站标准底部内容，包括：</p>
                <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                    <li>政府部门官方链接</li>
                    <li>各区市人社局选择器</li>
                    <li>备案信息和版权声明</li>
                    <li>二维码关注区域</li>
                    <li>现代化的视觉设计</li>
                </ul>
            </div>
        </div>

        <!-- 底部内容预览 -->
        <div class="footerBar">
            <div class="footerMain">
                <p class="linkBox">
                    <a class="transi" href="http://www.mohrss.gov.cn/" target="_blank">中华人民共和国人力资源和社会保障部</a>
                    <em>|</em>
                    <a class="transi" href="http://hrss.shandong.gov.cn/" target="_blank">山东省人力资源和社会保障厅</a>
                    <em>|</em>
                    <a class="transi" href="http://www.qingdao.gov.cn/" target="_blank">西宁市政务网</a>
                    <em>|</em>
                    <a class="transi" href="http://hrsswb.qingdao.gov.cn/" target="_blank">网上办事大厅</a>
                    <em>|</em>
                    <a class="transi" href="https://fw.rc.qingdao.gov.cn/" target="_blank">西宁市人才网</a>
                    <em>|</em>
                    <select onchange="javascript:window.open(this.options[this.selectedIndex].value);this.selectedIndex=0;" name="" id="">
                        <option value="-1"> 各区市人社局 </option>
                        <option value="http://www.qdsn.gov.cn/"> 市南区 </option>
                        <option value="http://www.qingdaoshibei.gov.cn/"> 市北区 </option>
                        <option value="http://www.qdlc.gov.cn/"> 李沧区 </option>
                        <option value="http://www.laoshan.gov.cn/"> 崂山区 </option>
                        <option value="https://www.xihaian.gov.cn/"> 西海岸新区 </option>
                        <option value="http://www.chengyang.gov.cn/"> 城阳区 </option>
                        <option value="http://www.jimo.gov.cn/"> 即墨区 </option>
                        <option value="http://www.jiaozhou.gov.cn/"> 胶州市 </option>
                        <option value="http://www.pingdu.gov.cn/"> 平度市 </option>
                        <option value="http://www.laixi.gov.cn/"> 莱西市 </option>
                    </select>
                </p>
                <div class="clearfix">
                    <div class="fl bottomBoxfoot mt10 clearfix">
                        <a href="http://bszs.conac.cn/sitename?method=show&id=0699D8BBDCC72A61E053012819ACDEFE" target="_blank" class="picBox1 fl"></a>
                        <div class="fl">
                            <div class="clearfix">
                                <div class="fl textBox">
                                    <p>
                                        <a href="https://beian.miit.gov.cn/" target="_blank">鲁ICP备05038584号</a>
                                        <a>政府网站标识码3702000055</a>
                                        <a href="https://hrss.qingdao.gov.cn/ditu.shtml" target="_blank">网站地图</a>
                                    </p>
                                    <p>
                                        <a>主办单位：西宁市人力资源和社会保障局</a>
                                        <a>版权所有：西宁市人力资源和社会保障局</a>
                                    </p>
                                    <p>
                                        <a>政策咨询电话：12333</a>
                                        <a href="http://www.beian.gov.cn/" target="_blank">鲁公网安备 37020202000549号</a>
                                    </p>
                                </div>
                                <a href="https://zfwzgl.www.gov.cn/" target="_blank" class="picBox2 fl"></a>
                            </div>
                            <p>分辨率1920X1080获得最佳浏览体验，建议使用谷歌浏览器、360浏览器极速模式、QQ浏览器极速模式访问达到最佳效果；如果网页显示不正常，请清理浏览器缓存。</p>
                        </div>
                    </div>
                    
                    <div class="fr codeBox clearfix">
                        <img src="./images/pics/footerCodeBg.png" class="fl" style="width: 100px; height: 100px; background: #ddd; border-radius: 8px;">
                        <p class="fl">扫码关注</br>"创在青岛"</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
