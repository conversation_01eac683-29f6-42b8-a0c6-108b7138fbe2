<template>
   <div class="job-log-container app-container">
      <!-- 使用 TableList 组件 -->
      <TableList v-if="isTableReady && tableColumns.length > 0" :columns="tableColumns" :data="tableData"
         :loading="tableLoading" :showIndex="true" :searchColumns="searchableColumns" :showOperation="true"
         :showSelection="true" operationLabel="操作" operationWidth="120" :fixedOperation="true" ref="tableListRef"
         @search="handleSearch" @reset="resetSearch" @selection-change="handleSelectionChange"
         :defaultPage="{ pageSize: pageSize, currentPage: currentPage, total: total }"
         @current-change="handleCurrentChange" @size-change="handleSizeChange">

         <!-- 左侧按钮插槽 -->
         <template #menu-left>
            <el-button type="danger" class="custom-btn" :disabled="multiple" @click="handleDelete"
               v-hasPermi="['monitor:job:remove']">
               删 除
            </el-button>
            <el-button type="danger" plain class="custom-btn" @click="handleClean" v-hasPermi="['monitor:job:remove']">
               清 空
            </el-button>
            <el-button type="warning" class="custom-btn" @click="handleExport" v-hasPermi="['monitor:job:export']">
               导 出
            </el-button>
            <el-button type="info" plain class="custom-btn" @click="handleClose">
               关 闭
            </el-button>
         </template>

         <!-- 操作列插槽 -->
         <template #menu="{ row }">
            <div>
               <el-button type="primary" link @click="handleView(row)" v-hasPermi="['monitor:job:query']">
                  详细
               </el-button>
            </div>
         </template>
      </TableList>

      <div v-else class="loading-placeholder">
         <el-empty description="正在加载表格配置..."></el-empty>
      </div>

      <!-- 表单弹窗组件 -->
      <JobLogFormDialog ref="jobLogFormDialogRef" :formFields="formFields" :formOption="formOption"
         @submit="handleFormSubmit" @cancel="handleFormCancel" />
   </div>
</template>

<script setup name="JobLog">
import { ref, reactive, onMounted, getCurrentInstance, computed, nextTick } from 'vue';
import { createJobLogTableOption } from "@/const/system/jobLog";
import { getJob } from "@/api/monitor/job";
import { listJobLog, delJobLog, cleanJobLog } from "@/api/monitor/jobLog";
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils";
import TableList from '@/components/TableList/index.vue';
import JobLogFormDialog from './JobLogFormDialog.vue';

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();
const { sys_common_status, sys_job_group } = proxy.useDict("sys_common_status", "sys_job_group");

const tableColumns = ref([]);
const searchableColumns = ref([]); // 可搜索的字段列表
const tableLoading = ref(false);
const isTableReady = ref(false);
const formOption = ref({
   dialogWidth: '800px',
   dialogHeight: '70vh'
});
const tableListRef = ref(null);
const jobLogFormDialogRef = ref(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref({});

// 表格数据
const tableData = ref([]);

// 多选相关
const selectedRows = ref([]);
const multiple = ref(true);

// 表单字段配置
const formFields = ref([]);

// 初始化时获取数据
onMounted(() => {
   // 确保表格数据为空数组，避免undefined问题
   tableData.value = [];
   initializeConfig();
});

// 初始化配置
const initializeConfig = async () => {
   try {
      // 获取基础配置
      const baseOption = createJobLogTableOption(proxy);

      // 使用工具类获取合并后的配置
      const mergedConfig = await getCoSyncColumn({
         baseOption,
         proxy
      });

      // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
      const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

      // 设置表格和搜索配置
      tableColumns.value = extractedTableColumns;
      searchableColumns.value = searchColumns;

      // 设置表单字段配置
      formFields.value = extractedFormFields;

      // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
      formOption.value = {
         ...formOption.value, // 保留默认配置
         ...formOptions       // 使用从配置文件中提取的完整选项
      };

      isTableReady.value = true;

      // 检查是否有特定任务ID参数
      await checkJobIdParam();

      // 加载表格数据
      loadData();
   } catch (error) {
      isTableReady.value = false;
      console.error('初始化配置失败:', error);
   }
};

// 检查任务ID参数
const checkJobIdParam = async () => {
   const jobId = route.params?.jobId;
   if (jobId && jobId !== '0') {
      try {
         const response = await getJob(jobId);
         if (response.data) {
            // 设置初始搜索参数
            searchParams.value = {
               jobName: response.data.jobName,
               jobGroup: response.data.jobGroup
            };
         }
      } catch (error) {
         console.error('获取任务信息失败:', error);
      }
   }
};

// 加载表格数据
const loadData = () => {
   tableLoading.value = true;

   const queryParams = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...searchParams.value
   };

   listJobLog(queryParams).then(response => {
      // 确保数据完整性
      const validData = (response.rows || []).filter(item => item && item.jobLogId);

      tableData.value = validData;
      total.value = response.total || 0;

      // 更新TableList组件的分页信息
      updateTablePagination();

      tableLoading.value = false;
   }).catch(error => {
      console.error('加载任务日志列表失败:', error);
      tableData.value = [];
      total.value = 0;
      tableLoading.value = false;
   });
};

// 更新表格分页信息
const updateTablePagination = () => {
   if (tableListRef.value && tableListRef.value.page) {
      tableListRef.value.page.total = total.value;
      tableListRef.value.page.currentPage = currentPage.value;
      tableListRef.value.page.pageSize = pageSize.value;
   }
};

// 搜索处理
const handleSearch = (params) => {
   searchParams.value = params;
   currentPage.value = 1;
   loadData();
};

// 重置搜索
const resetSearch = () => {
   searchParams.value = {};
   currentPage.value = 1;
   loadData();
};

// 分页处理
const handleCurrentChange = (page) => {
   currentPage.value = page;
   loadData();
};

const handleSizeChange = (size) => {
   pageSize.value = size;
   currentPage.value = 1;
   loadData();
};

// 多选处理
const handleSelectionChange = (selection) => {
   selectedRows.value = selection;
   multiple.value = !selection.length;
};

// 查看详情
const handleView = (row) => {
   jobLogFormDialogRef.value.openDialog('view', '调度日志详细', row);
};

// 删除
const handleDelete = () => {
   if (!selectedRows.value.length) {
      proxy.$modal.msgWarning('请选择要删除的数据');
      return;
   }

   const jobLogIds = selectedRows.value.map(item => item.jobLogId);
   proxy.$modal.confirm('是否确认删除调度日志编号为"' + jobLogIds.join(',') + '"的数据项?').then(function () {
      return delJobLog(jobLogIds);
   }).then(() => {
      loadData();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
};

// 清空
const handleClean = () => {
   proxy.$modal.confirm("是否确认清空所有调度日志数据项?").then(function () {
      return cleanJobLog();
   }).then(() => {
      loadData();
      proxy.$modal.msgSuccess("清空成功");
   }).catch(() => { });
};

// 导出
const handleExport = () => {
   proxy.download("monitor/jobLog/export", {
      ...searchParams.value,
   }, `job_log_${new Date().getTime()}.xlsx`);
};

// 关闭页面
const handleClose = () => {
   const obj = { path: "/system/job" };
   proxy.$tab.closeOpenPage(obj);
};

// 处理表单提交
const handleFormSubmit = ({ type, data }) => {
   // 日志详情页面通常不涉及提交操作
};

// 处理表单取消
const handleFormCancel = () => {
   // 表单取消时的处理逻辑
};
</script>

