package com.sux.system.mapper;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sux.system.domain.EmploymentInfo;

/**
 * 用工信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface EmploymentInfoMapper extends BaseMapper<EmploymentInfo>
{
    /**
     * 查询用工信息列表
     * 
     * @param employmentInfo 用工信息
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectEmploymentInfoList(EmploymentInfo employmentInfo);

    /**
     * 查询用工信息
     * 
     * @param employmentId 用工信息主键
     * @return 用工信息
     */
    public EmploymentInfo selectEmploymentInfoByEmploymentId(Long employmentId);

    /**
     * 新增用工信息
     * 
     * @param employmentInfo 用工信息
     * @return 结果
     */
    public int insertEmploymentInfo(EmploymentInfo employmentInfo);

    /**
     * 修改用工信息
     * 
     * @param employmentInfo 用工信息
     * @return 结果
     */
    public int updateEmploymentInfo(EmploymentInfo employmentInfo);

    /**
     * 删除用工信息
     * 
     * @param employmentId 用工信息主键
     * @return 结果
     */
    public int deleteEmploymentInfoByEmploymentId(Long employmentId);

    /**
     * 批量删除用工信息
     * 
     * @param employmentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmploymentInfoByEmploymentIds(Long[] employmentIds);

    /**
     * 查询已发布的用工信息列表
     * 
     * @param employmentInfo 用工信息
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectPublishedEmploymentInfoList(EmploymentInfo employmentInfo);

    /**
     * 查询推荐用工信息列表
     * 
     * @param employmentInfo 用工信息
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectFeaturedEmploymentInfoList(EmploymentInfo employmentInfo);

    /**
     * 查询我发布的用工信息列表
     * 
     * @param employmentInfo 用工信息
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectMyEmploymentInfoList(EmploymentInfo employmentInfo);

    /**
     * 根据用工类型查询用工信息列表
     * 
     * @param employmentType 用工类型
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectEmploymentInfoByType(String employmentType);

    /**
     * 根据工作类别查询用工信息列表
     * 
     * @param workCategory 工作类别
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectEmploymentInfoByCategory(String workCategory);

    /**
     * 根据区域代码查询用工信息列表
     * 
     * @param regionCode 区域代码
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectEmploymentInfoByRegion(String regionCode);

    /**
     * 根据薪资类型查询用工信息列表
     * 
     * @param salaryType 薪资类型
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectEmploymentInfoBySalaryType(String salaryType);

    /**
     * 根据薪资范围查询用工信息列表
     * 
     * @param minSalary 最低薪资
     * @param maxSalary 最高薪资
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectEmploymentInfoBySalaryRange(java.math.BigDecimal minSalary, java.math.BigDecimal maxSalary);

    /**
     * 根据紧急程度查询用工信息列表
     * 
     * @param urgencyLevel 紧急程度
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectEmploymentInfoByUrgency(String urgencyLevel);

    /**
     * 查询用工信息统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> selectEmploymentInfoStatistics();

    /**
     * 根据关键词搜索用工信息
     * 
     * @param keyword 关键词
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectEmploymentInfoByKeyword(String keyword);

    /**
     * 更新用工信息浏览次数
     * 
     * @param employmentId 用工信息ID
     * @return 结果
     */
    public int updateEmploymentInfoViewCount(Long employmentId);

    /**
     * 更新用工信息申请次数
     * 
     * @param employmentId 用工信息ID
     * @return 结果
     */
    public int updateEmploymentInfoApplicationCount(Long employmentId);

    /**
     * 查询即将到期的用工信息
     * 
     * @param days 天数
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectEmploymentInfoExpiringSoon(Integer days);

    /**
     * 查询用工信息详细信息（包含关联信息）
     * 
     * @param employmentId 用工信息ID
     * @return 用工信息
     */
    public EmploymentInfo selectEmploymentInfoDetailByEmploymentId(Long employmentId);

    /**
     * 查询相似的用工信息列表
     * 
     * @param employmentInfo 用工信息
     * @param limit 限制数量
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectSimilarEmploymentInfoList(EmploymentInfo employmentInfo, Integer limit);

    /**
     * 获取所有用工类型列表
     * 
     * @return 用工类型列表
     */
    public List<String> selectAllEmploymentTypes();

    /**
     * 获取所有工作类别列表
     * 
     * @return 工作类别列表
     */
    public List<String> selectAllWorkCategories();

    /**
     * 获取所有薪资类型列表
     * 
     * @return 薪资类型列表
     */
    public List<String> selectAllSalaryTypes();

    /**
     * 获取所有区域列表
     * 
     * @return 区域列表
     */
    public List<Map<String, String>> selectAllRegions();

    /**
     * 获取所有学历要求列表
     * 
     * @return 学历要求列表
     */
    public List<String> selectAllEducationRequirements();

    /**
     * 根据核心字段搜索用工信息
     * 
     * @param employmentType 用工类型
     * @param workCategory 工作类别
     * @param salaryType 薪资类型
     * @param regionCode 区域代码
     * @param keyword 关键词
     * @return 用工信息集合
     */
    public List<EmploymentInfo> selectEmploymentInfoByCoreFields(String employmentType, String workCategory, 
                                                                String salaryType, String regionCode, String keyword);
}
