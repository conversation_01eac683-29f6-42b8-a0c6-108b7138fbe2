package com.sux.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.StringUtils;
import com.sux.system.domain.PolicyInfo;
import com.sux.system.mapper.PolicyInfoMapper;
import com.sux.system.service.IPolicyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 政策信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class PolicyInfoServiceImpl extends ServiceImpl<PolicyInfoMapper, PolicyInfo> implements IPolicyInfoService
{
    @Autowired
    private PolicyInfoMapper policyInfoMapper;

    /**
     * 查询政策信息列表
     * 
     * @param policyInfo 政策信息
     * @return 政策信息
     */
    @Override
    public List<PolicyInfo> selectPolicyInfoList(PolicyInfo policyInfo)
    {
        return policyInfoMapper.selectPolicyInfoList(policyInfo);
    }

    /**
     * 查询政策信息
     * 
     * @param policyId 政策信息主键
     * @return 政策信息
     */
    @Override
    public PolicyInfo selectPolicyInfoByPolicyId(Long policyId)
    {
        return policyInfoMapper.selectPolicyInfoByPolicyId(policyId);
    }

    /**
     * 新增政策信息
     * 
     * @param policyInfo 政策信息
     * @return 结果
     */
    @Override
    public int insertPolicyInfo(PolicyInfo policyInfo)
    {
        policyInfo.setCreateId(SecurityUtils.getUserId());
        policyInfo.setCreateTime(DateUtils.getNowDate());
        return policyInfoMapper.insertPolicyInfo(policyInfo);
    }

    /**
     * 修改政策信息
     * 
     * @param policyInfo 政策信息
     * @return 结果
     */
    @Override
    public int updatePolicyInfo(PolicyInfo policyInfo)
    {
        policyInfo.setUpdateId(SecurityUtils.getUserId());
        policyInfo.setUpdateTime(DateUtils.getNowDate());
        return policyInfoMapper.updatePolicyInfo(policyInfo);
    }

    /**
     * 批量删除政策信息
     * 
     * @param policyIds 需要删除的政策信息主键
     * @return 结果
     */
    @Override
    public int deletePolicyInfoByPolicyIds(Long[] policyIds)
    {
        return policyInfoMapper.deletePolicyInfoByPolicyIds(policyIds);
    }

    /**
     * 删除政策信息信息
     * 
     * @param policyId 政策信息主键
     * @return 结果
     */
    @Override
    public int deletePolicyInfoByPolicyId(Long policyId)
    {
        return policyInfoMapper.deletePolicyInfoByPolicyId(policyId);
    }

    /**
     * 校验政策名称是否唯一
     * 
     * @param policyInfo 政策信息
     * @return 结果
     */
    @Override
    public boolean checkPolicyNameUnique(PolicyInfo policyInfo)
    {
        Long policyId = StringUtils.isNull(policyInfo.getPolicyId()) ? -1L : policyInfo.getPolicyId();
        PolicyInfo info = policyInfoMapper.checkPolicyNameUnique(policyInfo.getPolicyName());
        if (StringUtils.isNotNull(info) && info.getPolicyId().longValue() != policyId.longValue())
        {
            return false;
        }
        return true;
    }
}
