/* 招聘信息匹配页面样式 */

.breadcrumb {
    padding: 20px 0;
    font-size: 14px;
    color: #666;
}

.breadcrumb a {
    color: #0052d9;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.separator {
    margin: 0 10px;
    color: #ccc;
}

.current {
    color: #333;
    font-weight: bold;
}

/* 招聘信息详情卡片 */
.jobDetailSection {
    margin-bottom: 30px;
}

.jobDetailCard {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-left: 4px solid #0052d9;
}

.jobDetailHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.jobDetailTitle {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin: 0;
    flex: 1;
}

.jobDetailSalary {
    font-size: 20px;
    font-weight: bold;
    color: #ff6000;
    background: #fff5f0;
    padding: 8px 16px;
    border-radius: 8px;
}

.jobDetailInfo {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.jobDetailItem {
    display: flex;
    align-items: center;
}

.jobDetailItem .label {
    font-weight: bold;
    color: #666;
    min-width: 80px;
}

.jobDetailItem .value {
    color: #333;
    flex: 1;
}

/* 匹配结果区域 */
.matchResultSection {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.sectionTitle {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.matchStats {
    display: flex;
    align-items: center;
    gap: 15px;
}

.matchCount {
    color: #666;
    font-size: 14px;
}

.refreshBtn {
    padding: 8px 16px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.refreshBtn:hover {
    background: #218838;
}

/* 筛选和排序 */
.matchFilters {
    display: flex;
    gap: 30px;
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.sortOptions, .filterOptions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.matchFilters label {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.matchFilters select {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
}

/* 加载指示器 */
.loading {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.loadingSpinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0052d9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 匹配结果项（优化版） */
.matchResultItem {
    display: flex;
    align-items: stretch;
    padding: 20px;
    border: 1px solid #e6e6e6;
    border-radius: 12px;
    margin-bottom: 15px;
    background: #fff;
    transition: all 0.3s ease;
    position: relative;
    min-height: 120px;
}

.matchResultItem:hover {
    border-color: #0052d9;
    box-shadow: 0 6px 16px rgba(0,82,217,0.15);
    transform: translateY(-2px);
}

.matchResultItem.high-match {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, #fff 0%, #f8fff9 100%);
}

.matchResultItem.medium-match {
    border-left: 4px solid #ffc107;
    background: linear-gradient(135deg, #fff 0%, #fffdf5 100%);
}

.matchResultItem.low-match {
    border-left: 4px solid #dc3545;
    background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
}

.workerAvatar {
    margin-right: 20px;
}

.workerAvatar img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #f0f0f0;
}

.workerMainInfo {
    flex: 1;
    min-width: 0;
}

.workerName {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0 0 8px 0;
}

.workerTags {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.workerTag {
    padding: 3px 8px;
    background: #e3f2fd;
    color: #1976d2;
    border-radius: 12px;
    font-size: 12px;
    white-space: nowrap;
}

.workerStats {
    display: flex;
    gap: 15px;
    font-size: 13px;
    color: #666;
    margin-bottom: 10px;
}

.workerStat {
    display: flex;
    align-items: center;
    gap: 4px;
}

.statIcon {
    font-size: 14px;
}

/* 匹配因素展示 */
.matchFactors {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}

.matchFactorsTitle {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.matchFactorsList {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.matchFactor {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
}

.factor-excellent {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.factor-good {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.factor-basic {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.matchScoreSection {
    text-align: center;
    margin: 0 25px;
}

.similarityCircle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    position: relative;
    background: conic-gradient(from 0deg, #28a745 0%, #28a745 var(--percentage), #e9ecef var(--percentage), #e9ecef 100%);
}

.similarityInner {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.similarityText {
    font-weight: bold;
    font-size: 16px;
    color: #28a745;
}

.matchLabel {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.workerActions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.actionBtn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s;
    min-width: 80px;
}

.btn-contact {
    background: #007bff;
    color: white;
}

.btn-contact:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-view {
    background: #6c757d;
    color: white;
}

.btn-view:hover {
    background: #545b62;
    transform: translateY(-1px);
}

/* 模态框样式增强 */
.worker-detail-modal {
    max-width: 900px;
    width: 95%;
}

.contact-modal {
    max-width: 500px;
    width: 90%;
}

.contact-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.contact-form h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.contact-form textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    resize: vertical;
    font-family: inherit;
    margin-bottom: 15px;
}

.contact-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn-send {
    padding: 10px 20px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

.btn-cancel {
    padding: 10px 20px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .jobDetailHeader {
        flex-direction: column;
        gap: 15px;
    }
    
    .jobDetailInfo {
        grid-template-columns: 1fr;
    }
    
    .matchResultItem {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .workerMainInfo {
        order: 2;
    }
    
    .matchScoreSection {
        order: 1;
        margin: 0;
    }
    
    .workerActions {
        order: 3;
        flex-direction: row;
        justify-content: center;
    }
    
    .matchFilters {
        flex-direction: column;
        gap: 15px;
    }
}

/* 零工详情模态框内容样式 */
.worker-detail-content {
    padding: 0;
}

.worker-header {
    display: flex;
    align-items: center;
    gap: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.worker-detail-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #f0f0f0;
}

.worker-basic-info h3 {
    margin: 0 0 5px 0;
    font-size: 20px;
    color: #333;
}

.worker-title {
    color: #666;
    margin: 0 0 10px 0;
    font-size: 14px;
}

.worker-rating {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rating-stars {
    font-size: 16px;
}

.rating-text {
    font-size: 13px;
    color: #666;
}

.worker-sections {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.worker-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 8px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.info-item {
    display: flex;
    align-items: center;
}

.info-item .label {
    font-weight: 500;
    color: #666;
    min-width: 80px;
}

.info-item .value {
    color: #333;
    flex: 1;
}

.intro-text {
    line-height: 1.6;
    color: #555;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 0;
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-results p {
    font-size: 16px;
    margin: 0;
}
