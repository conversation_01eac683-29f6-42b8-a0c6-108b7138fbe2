import { parseTime } from "@/utils/ruoyi";

export const createPolicyInfoTableOption = (proxy) => {
    const {
        sys_normal_disable
    } = proxy.useDict("sys_normal_disable");

    // 政策类型选项
    const policyTypeOptions = [
        { label: "就业扶持", value: "就业扶持" },
        { label: "创业支持", value: "创业支持" },
        { label: "技能培训", value: "技能培训" },
        { label: "社会保障", value: "社会保障" },
        { label: "其他", value: "其他" }
    ];

    return {
        dialogWidth: '800px',  // 弹窗宽度
        dialogHeight: '60vh',  // 弹窗内容区最大高度
        labelWidth: '100px',
        column: [
            // ==================== 基础信息分组 ====================
            {
                label: "基础信息",
                prop: "divider_basic_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true // 分隔线标识
            },
            {
                label: "政策名称",
                prop: "policyName",
                search: true,
                searchSpan: 12,
                minWidth: 200,
                rules: [
                    { required: true, message: "政策名称不能为空", trigger: "blur" },
                    { min: 2, max: 200, message: "政策名称长度必须介于 2 和 200 之间", trigger: "blur" }
                ],
                span: 24
            },
            {
                label: "政策类型",
                prop: "policyType",
                search: true,
                searchSpan: 12,
                width: 120,
                align: "center",
                type: "select",
                dicData: policyTypeOptions,
                span: 12,
                rules: [
                    { required: true, message: "政策类型不能为空", trigger: "change" }
                ]
            },
            {
                label: "状态",
                prop: "status",
                search: true,
                searchSpan: 12,
                width: 100,
                align: "center",
                type: "select",
                dicData: sys_normal_disable,
                span: 12,
                slot: true,
                rules: [
                    { required: true, message: "状态不能为空", trigger: "change" }
                ]
            },
            {
                label: "政策描述",
                prop: "policyDescription",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 4,
                maxRows: 8,
                showWordLimit: true,
                maxlength: 2000,
                rules: [
                    { max: 2000, message: "政策描述不能超过2000个字符", trigger: "blur" }
                ]
            },
            // ==================== 系统信息分组 ====================
            {
                label: "系统信息",
                prop: "divider_system_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "备注",
                prop: "remark",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                showWordLimit: true,
                maxlength: 500,
                addDisplay: false,
                editDisplay: false
            }
        ]
    };
};
