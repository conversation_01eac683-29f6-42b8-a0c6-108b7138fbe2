package com.sux.system.service.impl;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sux.system.mapper.PlaceInfoMapper;
import com.sux.system.domain.PlaceInfo;
import com.sux.system.service.IPlaceInfoService;

/**
 * 场地信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class PlaceInfoServiceImpl extends ServiceImpl<PlaceInfoMapper, PlaceInfo> implements IPlaceInfoService
{
    @Autowired
    private PlaceInfoMapper placeInfoMapper;

    /**
     * 查询场地信息列表
     * 
     * @param placeInfo 场地信息
     * @return 场地信息
     */
    @Override
    public List<PlaceInfo> selectPlaceInfoList(PlaceInfo placeInfo)
    {
        return placeInfoMapper.selectPlaceInfoList(placeInfo);
    }

    /**
     * 查询场地信息
     * 
     * @param placeId 场地信息主键
     * @return 场地信息
     */
    @Override
    public PlaceInfo selectPlaceInfoByPlaceId(Long placeId)
    {
        return placeInfoMapper.selectPlaceInfoByPlaceId(placeId);
    }

    /**
     * 新增场地信息
     * 
     * @param placeInfo 场地信息
     * @return 结果
     */
    @Override
    public int insertPlaceInfo(PlaceInfo placeInfo)
    {
        placeInfo.setCreateId(SecurityUtils.getUserId());
        placeInfo.setCreateTime(DateUtils.getNowDate());
        
        // 设置默认值
        if (StringUtils.isEmpty(placeInfo.getStatus())) {
            placeInfo.setStatus("0");
        }
        if (placeInfo.getIsFeatured() == null) {
            placeInfo.setIsFeatured(0);
        }
        if (placeInfo.getIsOpenSettle() == null) {
            placeInfo.setIsOpenSettle(1);
        }
        if (placeInfo.getApplyTimeStatus() == null) {
            placeInfo.setApplyTimeStatus(0);
        }
        if (placeInfo.getCompanyCount() == null) {
            placeInfo.setCompanyCount(0);
        }
        if (placeInfo.getAvailablePositions() == null) {
            placeInfo.setAvailablePositions(0);
        }
        if (placeInfo.getOccupiedPositions() == null) {
            placeInfo.setOccupiedPositions(0);
        }
        if (placeInfo.getViewCount() == null) {
            placeInfo.setViewCount(0);
        }
        if (placeInfo.getSortOrder() == null) {
            placeInfo.setSortOrder(0);
        }
        if (StringUtils.isEmpty(placeInfo.getDelFlag())) {
            placeInfo.setDelFlag("0");
        }

        return placeInfoMapper.insertPlaceInfo(placeInfo);
    }

    /**
     * 修改场地信息
     * 
     * @param placeInfo 场地信息
     * @return 结果
     */
    @Override
    public int updatePlaceInfo(PlaceInfo placeInfo)
    {
        placeInfo.setUpdateId(SecurityUtils.getUserId());
        placeInfo.setUpdateTime(DateUtils.getNowDate());
        return placeInfoMapper.updatePlaceInfo(placeInfo);
    }

    /**
     * 批量删除场地信息
     * 
     * @param placeIds 需要删除的场地信息主键
     * @return 结果
     */
    @Override
    public int deletePlaceInfoByPlaceIds(Long[] placeIds)
    {
        return placeInfoMapper.deletePlaceInfoByPlaceIds(placeIds);
    }

    /**
     * 删除场地信息信息
     * 
     * @param placeId 场地信息主键
     * @return 结果
     */
    @Override
    public int deletePlaceInfoByPlaceId(Long placeId)
    {
        return placeInfoMapper.deletePlaceInfoByPlaceId(placeId);
    }

    /**
     * 查询推荐场地信息列表
     * 
     * @param placeInfo 场地信息
     * @return 场地信息集合
     */
    @Override
    public List<PlaceInfo> selectFeaturedPlaceInfoList(PlaceInfo placeInfo)
    {
        return placeInfoMapper.selectFeaturedPlaceInfoList(placeInfo);
    }

    /**
     * 查询活跃场地信息列表
     * 
     * @param placeInfo 场地信息
     * @return 场地信息集合
     */
    @Override
    public List<PlaceInfo> selectActivePlaceInfoList(PlaceInfo placeInfo)
    {
        return placeInfoMapper.selectActivePlaceInfoList(placeInfo);
    }

    /**
     * 根据场地类型查询场地信息列表
     * 
     * @param placeType 场地类型
     * @return 场地信息集合
     */
    @Override
    public List<PlaceInfo> selectPlaceInfoByType(String placeType)
    {
        return placeInfoMapper.selectPlaceInfoByType(placeType);
    }

    /**
     * 根据区域代码查询场地信息列表
     * 
     * @param regionCode 区域代码
     * @return 场地信息集合
     */
    @Override
    public List<PlaceInfo> selectPlaceInfoByRegion(String regionCode)
    {
        return placeInfoMapper.selectPlaceInfoByRegion(regionCode);
    }

    /**
     * 根据场地等级查询场地信息列表
     * 
     * @param placeLevel 场地等级
     * @return 场地信息集合
     */
    @Override
    public List<PlaceInfo> selectPlaceInfoByLevel(String placeLevel)
    {
        return placeInfoMapper.selectPlaceInfoByLevel(placeLevel);
    }

    /**
     * 根据行业方向查询场地信息列表
     * 
     * @param industryDirection 行业方向
     * @return 场地信息集合
     */
    @Override
    public List<PlaceInfo> selectPlaceInfoByIndustry(String industryDirection)
    {
        return placeInfoMapper.selectPlaceInfoByIndustry(industryDirection);
    }

    /**
     * 根据运营模式查询场地信息列表
     * 
     * @param operationMode 运营模式
     * @return 场地信息集合
     */
    @Override
    public List<PlaceInfo> selectPlaceInfoByOperationMode(String operationMode)
    {
        return placeInfoMapper.selectPlaceInfoByOperationMode(operationMode);
    }

    /**
     * 查询场地统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectPlaceInfoStatistics()
    {
        return placeInfoMapper.selectPlaceInfoStatistics();
    }

    /**
     * 根据关键词搜索场地信息
     * 
     * @param keyword 关键词
     * @return 场地信息集合
     */
    @Override
    public List<PlaceInfo> selectPlaceInfoByKeyword(String keyword)
    {
        return placeInfoMapper.selectPlaceInfoByKeyword(keyword);
    }

    /**
     * 更新场地浏览次数
     * 
     * @param placeId 场地ID
     * @return 结果
     */
    @Override
    public int updatePlaceInfoViewCount(Long placeId)
    {
        return placeInfoMapper.updatePlaceInfoViewCount(placeId);
    }

    /**
     * 查询即将到期的招商场地
     * 
     * @param days 天数
     * @return 场地信息集合
     */
    @Override
    public List<PlaceInfo> selectPlaceInfoExpiringSoon(Integer days)
    {
        return placeInfoMapper.selectPlaceInfoExpiringSoon(days);
    }

    /**
     * 查询场地详细信息（包含关联信息）
     * 
     * @param placeId 场地ID
     * @return 场地信息
     */
    @Override
    public PlaceInfo selectPlaceInfoDetailByPlaceId(Long placeId)
    {
        return placeInfoMapper.selectPlaceInfoDetailByPlaceId(placeId);
    }

    /**
     * 获取所有场地类型列表
     * 
     * @return 场地类型列表
     */
    @Override
    public List<String> selectAllPlaceTypes()
    {
        return placeInfoMapper.selectAllPlaceTypes();
    }

    /**
     * 获取所有场地等级列表
     * 
     * @return 场地等级列表
     */
    @Override
    public List<String> selectAllPlaceLevels()
    {
        return placeInfoMapper.selectAllPlaceLevels();
    }

    /**
     * 获取所有区域列表
     * 
     * @return 区域列表
     */
    @Override
    public List<Map<String, String>> selectAllRegions()
    {
        return placeInfoMapper.selectAllRegions();
    }

    /**
     * 获取所有行业方向列表
     * 
     * @return 行业方向列表
     */
    @Override
    public List<String> selectAllIndustryDirections()
    {
        return placeInfoMapper.selectAllIndustryDirections();
    }

    /**
     * 获取所有运营模式列表
     * 
     * @return 运营模式列表
     */
    @Override
    public List<String> selectAllOperationModes()
    {
        return placeInfoMapper.selectAllOperationModes();
    }
}
