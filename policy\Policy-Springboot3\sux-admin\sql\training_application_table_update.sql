-- 培训报名表字段更新脚本
-- 如果已经创建了旧的表结构，使用此脚本更新字段类型

-- 检查并更新create_by字段为create_id
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'training_application' 
    AND COLUMN_NAME = 'create_by');

SET @sql = IF(@column_exists > 0, 
    'ALTER TABLE training_application 
     DROP COLUMN create_by, 
     ADD COLUMN create_id bigint(20) DEFAULT NULL COMMENT "创建者ID" AFTER application_note',
    'SELECT "create_by column does not exist" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并更新update_by字段为update_id
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'training_application' 
    AND COLUMN_NAME = 'update_by');

SET @sql = IF(@column_exists > 0, 
    'ALTER TABLE training_application 
     DROP COLUMN update_by, 
     ADD COLUMN update_id bigint(20) DEFAULT NULL COMMENT "更新者ID" AFTER create_time',
    'SELECT "update_by column does not exist" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证表结构
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'training_application' 
AND COLUMN_NAME IN ('create_id', 'update_id')
ORDER BY ORDINAL_POSITION;
